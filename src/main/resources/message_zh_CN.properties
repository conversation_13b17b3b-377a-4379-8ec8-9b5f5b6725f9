apis.sptalentrv.sys.error=4444444;451;服务异常。
apis.sptalentrv.sys.operating.frequently=2181016;400;操作频繁。
apis.sptalentrv.sys.concurrent.error=2181404;400;并发错误。
apis.sptalentrv.global.enum.exceed=2181017;400;枚举值不在有效范围。

apis.sptalentrv.auth.dept_ids.no_permission=2181375;400;无法查看非本人管辖部门的数据
apis.sptalentrv.auth.team.dept_ids.only_one=2181398;400;查看人才看板时,有且只能选中一个部门。
apis.sptalentrv.auth.param.error=2181400;400;参数错误。

apis.sptalentrv.transmit.import.fileId.invalid=2181405;400;导入的文件ID无效
apis.sptalentrv.transmit.import.file.read.error=2181406;400;导入的文件读取错误
apis.sptalentrv.transmit.import.data.out.limit=2181030;400;导入数据超出限制

apis.sptalentrv.org.id.blank=2181326;400;机构id不能为空
apis.sptalentrv.org.id.notMatch=2181396;400;机构id不匹配

apis.sptalentrv.user.focus.target_id.necessary=2181376;400;关注目标id不能为空
apis.sptalentrv.user.focus.action.range=2181378;400;操作类型范围是[0-1]
apis.sptalentrv.user.focus.target_type.range=2181379;400;关注目标类型范围是[0-1]
apis.sptalentrv.user.pos.infos.not.empty=2181393;400;用户前端搜索参数岗位信息不能为空
apis.sptalentrv.user.not.existed=2181051;400;用户不存在
apis.sptalentrv.user.id.blank=2181274;400;用户id不能为空
apis.sptalentrv.user.third_user_id.necessary=2181395;400;第三方用户id不能为空
apis.sptalentrv.user.career_history_id.necessary=2181437;400;任职履历id不能为空
apis.sptalentrv.user.reward_punishment_history.rpType.invalid=2181397;400;奖惩类型不合法
apis.sptalentrv.user.reward_punishment_history.rpName.necessary=2181441;400;奖惩名称不能为空

apis.sptalentrv.dept.not.exist=2181399;400;部门不存在

apis.sptalentrv.prj.existed=2181000;400;项目已存在。
apis.sptalentrv.prj.notExists=2181002;400;项目不存在。
apis.sptalentrv.prj.name.notBlank=2181243;400;项目名称不能为空
apis.sptalentrv.prj.category.id.notBlank=2181244;400;项目分类ID不能为空
apis.sptalentrv.prj.id.notBlank=2181246;400;项目ID不能为空
apis.sptalentrv.prj.cloud.is.null=2181253;400;云项目为空
apis.sptalentrv.prj.rule.check.expression=2181005;400;项目规则表达式不合法。
apis.sptalentrv.prj.status=2181023;400;项目正在进行中。
apis.sptalentrv.prj.status.invalid=2181026;400;项目状态不允许。
apis.sptalentrv.prj.manager.not.empty=2181032;400;盘点项目负责人不能为空
apis.sptalentrv.prj.manager.size.exceeded=2181033;400;盘点项目负责人最大支持1000人
apis.sptalentrv.prj.dim.conf.tool.notExists=2181031;404;维度工具不存在
apis.sptalentrv.prj.perf.period.name.conflict=2181411;400;绩效周期名称重复
apis.sptalentrv.prj.perf.import.no.username=2181412;400;用户名为空
apis.sptalentrv.prj.perf.import.no.period=2181413;400;绩效周期为空
apis.sptalentrv.prj.perf.import.no.level=2181414;400;绩效为空
apis.sptalentrv.prj.perf.period.not.exist=2181416;404;绩效周期不存在
apis.sptalentrv.prj.perf.period.has.data=2181417;400;周期关联数据
apis.sptalentrv.prj.perf.cycle.necessary=2181435;400;绩效类型不能为空
apis.sptalentrv.prj.perf.period.necessary=2181436;400;绩效周期不能为空
apis.sptalentrv.prj.perf.sync.period.invalid_1=2181443;400;半年度绩效周期的期数必须等于1或2
apis.sptalentrv.prj.perf.sync.period.invalid_2=2181444;400;季度绩效周期的期数必须等于1-4
apis.sptalentrv.prj.perf.sync.period.invalid_3=2181445;400;月度绩效周期的期数必须等于1-12
apis.sptalentrv.prj.perf.sync.period.invalid_4=2181446;400;年度绩效周期的期数必须等于年份
apis.sptalentrv.prj.perf.sync.error_1=2181447;400;绩效总分和绩效等级必须有一个有值
apis.sptalentrv.prj.perf.sync.error_2=2181448;400;绩效得分时，必须要有绩效总分
apis.sptalentrv.prj.perf.yearly.necessary=2181442;400;绩效年份不能为空
apis.sptalentrv.prj.perf.grade.name.conflict=2181425;400;绩效等级值重复
apis.sptalentrv.prj.perf.grade.not.exist=2181426;400;绩效等级不存在
apis.sptalentrv.prj.dim.max.exceed=2181418;400;自定义维度上限为20
apis.sptalentrv.prj.dim.name.conflict=2181419;400;维度名称重复
apis.sptalentrv.prj.dim.not.exist=2181420;404;维度不存在
apis.sptalentrv.prj.dim.enabled=2181421;400;维度已经是启用状态
apis.sptalentrv.prj.dim.disabled=2181422;400;维度已禁用
apis.sptalentrv.prj.dim.config.count=2181004;400;项目维度配置计数小于 2。
apis.sptalentrv.prj.dim.not.configured=2181007;400个已存在维度未配置。
apis.sptalentrv.prj.dim.config.not.existed=2181009;400;维度配置不存在
apis.sptalentrv.prj.dim.tool.not.configured=2181012;400;未配置项目维度工具。
apis.sptalentrv.prj.dim.enable=2181019;400;维度已启用。
apis.sptalentrv.prj.dim.deleted=2181020;400;维度已删除。
apis.sptalentrv.prj.dim.tool.exist.same.model=2181022;400;尺寸工具存在相同型号。
apis.sptalentrv.prj.dim.tools.missing=2181035;400;项目维度工具缺失
apis.sptalentrv.prj.dim.conf.not.exists=2181040;400;项目维度配置不存在
apis.sptalentrv.prj.dim.not.config=2181041;400;项目未配置维度
apis.sptalentrv.prj.dim.conf.invalid=2181053;400;维度配置不完整
apis.sptalentrv.prj.dim.system.notDisabled=2181230;400;维度系统未禁用
apis.sptalentrv.prj.dim.name.size.out.limit=2181235;400;维度名称长度超出限制
apis.sptalentrv.prj.dim.remark.size.out.limit=2181236;400;维度备注长度超出限制
apis.sptalentrv.prj.dim.conf.notBlank=2181245;400;维度配置ID不能为空
apis.sptalentrv.prj.dim.been.used=2181423;400;维度已被使用
apis.sptalentrv.prj.dim.rule.not.existed=2181428;400;维度规则不存在
apis.sptalentrv.prj.dim.not.existed=2181429;400;维度不存在
apis.sptalentrv.prj.user.add.must.not.null=2181042;400;项目添加用户不能为空
apis.sptalentrv.prj.user.remove.not.null=2181043;400;项目移除用户不能为空
apis.sptalentrv.prj.user.must.not.null=2181044;400;项目用户不能为空
apis.sptalentrv.prj.user.result.calc.type.import.overflow=2181047;400;项目计算类型导入溢出
apis.sptalentrv.prj.user.result.calc.update.import.overflow=2181048;400;项目计算更新导入溢出
apis.sptalentrv.prj.user.result.import.fileId.invalid=2181252;400;计算结果导入的文件ID无效
apis.sptalentrv.prj.user.suggestion.size=2181238;400;用户建议长度超出限制
apis.sptalentrv.prj.eval.not.existed=2181010;400;测评不存在或已删除。
apis.sptalentrv.prj.eval.user.check.error=2181039;400;检查评估用户错误
apis.sptalentrv.prj.eval.relation.necessary=2181024:400;该项目维度测评存在人员未设置评估关系，请先设置
apis.sptalentrv.prj.eval.overflow=2181046;400;项目评估费用溢出
apis.sptalentrv.prj.eval.id.null=2181394;400;评估id不能为空
apis.sptalentrv.prj.label.name.conflict=2181052;400;项目标签名称冲突
apis.sptalentrv.prj.grade.name.not.null=2181239;400;等级名称不能为空
apis.sptalentrv.prj.grade.name.size.out.limit=2181240;400;等级名称长度超出限制
apis.sptalentrv.prj.period.name.not.null=2181241;400;周期名称不能为空
apis.sptalentrv.prj.period.name.size.out.limit=2181242;400;周期名称长度超出限制
apis.sptalentrv.prj.training.user_id.empty=2181382;400;绑定培训项目时用户id不能为空
apis.sptalentrv.prj.training.training_id.empty=2181427;400;绑定培训项目时培训id不能为空
apis.sptalentrv.prj.mine.status.range=2181429;400;项目状态范围错误
apis.sptalentrv.prj.label.rule.not.empty=2181501;400;项目标签规则不能为空
apis.sptalentrv.prj.logic.not.null=2181502;400;项目逻辑不能为空
apis.sptalentrv.prj.logic.range.over=2181503;400;项目逻辑范围错误
apis.sptalentrv.prj.label.rule.target.id.not.null=2181504;400;项目标签规则目标id不能为空
apis.sptalentrv.prj.label.rule.operators.not.empty=2181505;400;项目标签规则操作符不能为空
apis.sptalentrv.prj.label.rule.operator.not.blank=2181506;400;项目标签规则操作符不能为空
apis.sptalentrv.prj.label.rule.value.not.null=2181507;400;项目标签规则值不能为空
apis.sptalentrv.prj.result.label.conflict=2181438;400;人才定义名称重复
apis.sptalentrv.prj.result.notExists=2181439;400;人才定义不存在

apis.sptalentrv.perf.cycle.range=2181432;400;绩效类型不在范围内
apis.sptalentrv.perf.yearly.range=2181433;400;绩效年份不在范围内
apis.sptalentrv.perf.used=2181434;400;绩效周期被使用不允许删除
apis.sptalentrv.perf.level.used=2181435;400;绩效等级被使用不允许删除
apis.sptalentrv.perf.key.exist=2181436;400;当前绩效年份下已经存在相同的绩效类型和绩效周期
apis.sptalentrv.perf.level.state.used=2181437;400;绩效等级启用状态下不允许删除
apis.sptalentrv.perf.period.empty=2181440;400;绩效周期不能为空

apis.sptalentrv.category.existed=2181001;400;类别已存在。
apis.sptalentrv.category.not.existed=2181003;400;类别不存在。
apis.sptalentrv.category.used=2181050;400;分类已被使用
apis.sptalentrv.category.validation.name.NotBlank=2181054;400;分类名称不能为空
apis.sptalentrv.category.max.count=2181229;400;分类数量达到上限
apis.sptalentrv.category.name.size.exceed=2181234;400;分类名称长度超出限制

apis.sptalentrv.calimeet.not.existed=2181006;400;calMeet 不存在。
apis.sptalentrv.calimeet.user.not.existed=2181008;400;会议用户不存在。
apis.sptalentrv.calimeet.name.existed=2181013;400;校准会议名称已存在。
apis.sptalentrv.calimeet.user.batch_add.list.empty=2181014;400;用户列表为空。
apis.sptalentrv.calimeet.user.not.exists=2181015;400;用户不在项目中。
apis.sptalentrv.calimeet.delete.forbidden=2181018;400;无法删除。
apis.sptalentrv.calimeet.name.notBlank=2181231;400;校准会议名称不能为空
apis.sptalentrv.calimeet.name.size.exceed=2181232;400;校准会议名称长度超出限制
apis.sptalentrv.calimeet.minutes.exceed=2181233;400;校准会议时长超出限制
apis.sptalentrv.calimeet.user.userIdList.notNull=2181237;400;会议用户ID列表不能为空
apis.sptalentrv.calimeet.user.suggestion.size=2181238;400;会议用户建议长度超出限制
apis.sptalentrv.calimeet.meetTime.notBlank=2181513;400;会议时间不能为空

apis.sptalentrv.dmp.task.not_start=2181300;400;无法发布，方案设计中任务未全部启用，请前往操作
apis.sptalentrv.dmp.rule.notExists=2181301;400;项目中匹配规则未设置
apis.sptalentrv.dmp.dmpMatchRule.dmp.hadSet=2181302;400;匹配规则已存在
apis.sptalentrv.dmp.dmpMatchRule.dmp.notMatch=2181303;400;匹配规则和当前项目不匹配
apis.sptalentrv.dmp.matchRule.type.notNull=2181305;400;匹配规则类型不为空
apis.sptalentrv.dmp.matchRule.notExist=2181306;400;匹配规则不存在
apis.sptalentrv.dmp.dmpUser.ids.notEmpty=2181307;400;用户不为空
apis.sptalentrv.dmp.matchRule.layerRule.layerType.notNull=2181308;400;匹配规则的分层设置类型为空
apis.sptalentrv.dmp.userId.dmpId.notMatch=2181309;400;用户和项目信息不匹配
apis.sptalentrv.dmp.matchRule.type.overRange=2181311;400;匹配规则类型错误
apis.sptalentrv.dmp.matchRule.layerRule.layerName.notBlank=2181312;400;匹配规则的分层名称为空
apis.sptalentrv.dmp.matchRule.layerRule.leastOne=2181313;400;匹配规则的分层设置不为空
apis.sptalentrv.dmp.dmpMsgConfig.sendFlag.overRange=2181314;400;消息配置发送标识不存在
apis.sptalentrv.dmp.matchRule.layerRule.layerValue.notNull=2181315;400;匹配规则分层设置值不为空
apis.sptalentrv.dmp.eval.getDimScoreByProjectId.error=2181316;400;调用测评获取维度信息失败
apis.sptalentrv.dmp.dmpUser.ids.position.notMatch=2181317;400;添加人员和项目岗不匹配
apis.sptalentrv.dmp.name.repeat=2181318;400;项目名称重复
apis.sptalentrv.dmp.copy.status.error=2181319;400;已经完成的项目才能被复制
apis.sptalentrv.dmp.notExists=2181320;400;人岗匹配项目不存在
apis.sptalentrv.dmp.finished.cannot.modify=2181321;400;已经完成的项目不能修改
apis.sptalentrv.dmp.hadFinished=2181323;400;项目已结束不支持修改信息
apis.sptalentrv.dmp.user.checkin.max.size=2181324;400;待检的人员数量超过1000人上限
apis.sptalentrv.dmp.task_type.not.null=2181325;400;任务类型不能为空
apis.sptalentrv.dmp.id.not.blank=2181327;400;dmpId不能为空
apis.sptalentrv.dmp.task_name.not.blank=2181328;400;dmpId不能为空
apis.sptalentrv.dmp.task_name.conflict=2181329;400;任务名称已存在
apis.sptalentrv.dmp.not.existed=2181330;400;dmp不存在
apis.sptalentrv.dmp.task.not.existed=2181331;400;动态岗位匹配任务不存在
apis.sptalentrv.dmp.task_id.not.blank=2181332;400;动态岗位匹配任务id不能为空
apis.sptalentrv.dmp.jq_dim.not.existed=2181333;400;动态岗位匹配任务维度不存在
apis.sptalentrv.dmp.form.config.empty=2181339;400;动态岗位匹配任务表单配置不能为空
apis.sptalentrv.dmp.task.active_status.not.null=2181341;400;动态岗位匹配任务激活状态不能为空
apis.sptalentrv.dmp.task.active_status.range.exceeded=2181342;400;动态岗位匹配任务激活状态范围超出
apis.sptalentrv.dmp.match.rule.not.found=2181344;400;动态岗位匹配规则未找到
apis.sptalentrv.dmp.rule.layer.not.found=2181347;400;动态岗位匹配规则分层未找到
apis.sptalentrv.dmp.plan.used=2181349;400;该岗位方案已经被使用
apis.sptalentrv.dmp.plan.id.not.blank=2181350;400;人岗匹配项目的任职资格方案id不能为空
apis.sptalentrv.dmp.task.empty=2181351;400;方案设计中未配置任务，请前往配置
apis.sptalentrv.dmp.dmpMatchRule.layerName.notRepeat=2181353;400;匹配规则分层名称不可重复
apis.sptalentrv.dmp.auth.error=2181354;400;当前操作人无操作权限
apis.sptalentrv.dmp.disableEval.range=2181355;400;动态项目禁用学员参与表单测评的开关字段范围超出
apis.sptalentrv.dmp.disableEval.error=2181356;400;动态项目禁用学员参与表单测评的开关字段错误
apis.sptalentrv.dmp.stopTime.error=2181357;400;动态项目自动结束时间不能小于项目指定的结束时间
apis.sptalentrv.dmp.conf.not.exists=2181358;400;动态项目配置不存在
apis.sptalentrv.dmp.endTime.expired=2181359;400;动态项目截止时间已过期
apis.sptalentrv.dmp.cannot.publish.or.timed=2181360;400;动态项目不能发布或定时发布
apis.sptalentrv.dmp.launch_time.expired=2181361;400;动态项目定时发布时间必须在当前时间之后
apis.sptalentrv.dmp.id.empty=2181362;400;动态项目id不能为空
apis.sptalentrv.dmp.status.notOperate=2181364;400;动态项目状态不可操作
apis.sptalentrv.dmp.dmpName.size=2181365;400;动态项目名称长度超出限制
apis.sptalentrv.dmp.dmpUser.positionId.notMatch=2181367;400;用户和项目岗不匹配
apis.sptalentrv.dmp.dmpUser.import.empty=2181369;400;导入的用户列表为空
apis.sptalentrv.dmp.dmpUser.import.fail=2181370;400;导入的用户列表失败
apis.sptalentrv.dmp.status.notTimedPublish=2181372;400;动态项目状态不是定时发布
apis.sptalentrv.dmp.task.create.failed=2181373;400;动态项目任务创建失败
apis.sptalentrv.dmp.plan.empty=2181374;400;岗位方案保存不能为空
apis.sptalentrv.dmp.copy.dimId.null=2181384;400;复制的维度id不能为空
apis.sptalentrv.dmp.copy.rule.null=2181385;400;复制的规则不能为空
apis.sptalentrv.dmp.rule_weight.error=2181386;400;规则权重错误
apis.sptalentrv.dmp.copy.rule.layer.null=2181387;400;复制的规则分层不能为空
apis.sptalentrv.dmp.copy.task.null=2181389;400;复制的任务不能为空
apis.sptalentrv.dmp.rule.score.detail.empty=2181390;400;规则分数详情不能为空
apis.sptalentrv.dmp.task.job.not.exists=2181409;400;动态岗位匹配任务job不存在
apis.sptalentrv.dmp.user.not.exists=2181410;400;动态岗位匹配任务用户不存在
apis.sptalentrv.dmp.task.dim.type.not.null=2181419;400;动态岗位匹配任务维度类型不能为空
apis.sptalentrv.dmp.task.dim.jq_dim.id.not.empty=2181420;400;动态岗位匹配任务维度id不能为空
apis.sptalentrv.dmp.task.dim.empty=2181421;400;动态岗位匹配任务维度不能为空
apis.sptalentrv.dmp.project.dim.id.is.null=2181422;400;项目维度id为空
apis.sptalentrv.dmp.copy.task.dim.null=2181423;400;复制的任务维度不能为空
apis.sptalentrv.dmp.rule.not.existed=2181424;400;规则不存在
apis.sptalentrv.dmp.task.desc.too.long=2181428;400;任务描述超出长度限制
apis.sptalentrv.dmp.remark.size=2181429;400;备注长度超出限制
apis.sptalentrv.dmp.user.auto.group.not.exists=2181431;400;自动分组不存在
apis.sptalentrv.dmp.startEndTime.invalid=2181508;400;开始时间不能大于结束时间

apis.sptalentrv.perf.level.no.right=2181510;400;绩效等级不存在或已禁用

apis.sptalentrv.calimeet.score.over.maxs=2181511;400;校准分数不能大于等于10000
apis.sptalentrv.prj.dimension.not.config=2181512;400;项目中有维度未配置