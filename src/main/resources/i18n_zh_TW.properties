#############################################################################################
###                                    導入導出國際化                                       ###
#############################################################################################
#評估人員導入模板
apis.sptalentrv.eval.user.export.header.fullName=被評估人姓名
apis.sptalentrv.eval.user.export.header.userName=被評估賬號
apis.sptalentrv.eval.user.export.header.relationType=評估關係
apis.sptalentrv.eval.user.export.header.evaluatorFullName=評估人姓名
apis.sptalentrv.eval.user.export.header.evaluatorIdUserName=評估人賬號
apis.sptalentrv.eval.user.export.file.name=導入評估關係
apis.sptalentrv.performance.user.export.header.userName=賬號
apis.sptalentrv.performance.user.export.header.fullName=姓名
apis.sptalentrv.performance.user.export.header.period=績效週期
apis.sptalentrv.performance.user.export.header.level=績效
apis.sptalentrv.performance.user.export.header.perfScore=績效總分
apis.sptalentrv.performance.user.export.header.perfPoint=績效得分
apis.sptalentrv.performance.user.export.header.errorMsg=錯誤信息
apis.sptalentrv.perf.user.export.file.name=績效導入

#人才盤點結果導出
apis.sptalentrv.prj.result.export.header.fullName=姓名
apis.sptalentrv.prj.result.export.header.userName=賬號
apis.sptalentrv.prj.result.export.header.status=賬號狀態
apis.sptalentrv.prj.result.export.header.deptName=部門
apis.sptalentrv.prj.result.export.header.positionName=崗位
apis.sptalentrv.prj.result.export.header.result=盤點結果
apis.sptalentrv.prj.result.export.file.name=人才盤點結果導出

#導入盤點結果（維度等級）
apis.sptalentrv.prj.user.cacl.export.header.fullName=姓名
apis.sptalentrv.prj.user.cacl.export.header.userName=賬號
apis.sptalentrv.prj.user.cacl.export.header.deptName=部門
apis.sptalentrv.prj.user.cacl.export.header.positionName=崗位
apis.sptalentrv.prj.user.cacl.export.header.dimensionName=維度名稱
apis.sptalentrv.prj.user.cacl.export.header.dimensionLevel=維度等級
apis.sptalentrv.prj.user.cacl.export.header.dimensionModel=維度模型
apis.sptalentrv.prj.user.cacl.export.file.name=導入盤點結果模板（維度等級）
apis.sptalentrv.prj.user.cacl.1.export.file.name=導入盤點結果模板（維度模型評分）

#校準會-校準結果導出
apis.sptalentrv.meeting.user.export.header.fullName=姓名
apis.sptalentrv.meeting.user.export.header.userName=賬號
apis.sptalentrv.meeting.user.export.header.deptName=部門
apis.sptalentrv.meeting.user.export.header.positionName=崗位
apis.sptalentrv.meeting.user.export.header.suggestion=發展建議
apis.sptalentrv.meeting.user.export.file.name=%s會議盤點結果
apis.sptalentrv.meeting.user.export.template.level.file.name=導入校準結果（維度等級）
apis.sptalentrv.meeting.user.export.template.score.file.name=導入校準結果（維度評分）
apis.sptalentrv.meeting.user.import.level.error.file.name=導入校準結果錯誤（維度等級）
apis.sptalentrv.meeting.user.import.score.error.file.name=導入校準結果錯誤（維度評分）
apis.sptalentrv.perf.temp.export.file.name=績效週期模板導出
apis.sptalentrv.perf.user.export.header.fullName=員工姓名
apis.sptalentrv.perf.user.export.header.userName=員工賬號
apis.sptalentrv.perf.user.export.header.period=績效週期
apis.sptalentrv.perf.user.export.header.level=績效
apis.sptalentrv.perf.user.export.header.perfScore=績效總分
apis.sptalentrv.perf.user.export.header.perfPoint=績效得分
apis.sptalentrv.prj.overview.export.header.deptName=部門
apis.sptalentrv.prj.overview.export.header.goodUser=優秀人員數量
apis.sptalentrv.prj.overview.export.header.goodUserPct=優秀人員佔比
apis.sptalentrv.prj.overview.export.header.middleUser=中堅人員數量
apis.sptalentrv.prj.overview.export.header.middleUserPct=中堅人員佔比
apis.sptalentrv.prj.overview.export.header.improveUser=待提升人員數量
apis.sptalentrv.prj.overview.export.header.improveUserPct=待提升人員佔比
apis.sptalentrv.prj.overview.export.header.count=數量
apis.sptalentrv.prj.overview.export.header.percent=佔比
apis.sptalentrv.prj.overview.export.header.others=其他人員數量
apis.sptalentrv.prj.overview.export.header.othersPct=其他人員佔比
apis.sptalentrv.prj.overview.export.file.name=盤點項目人才分佈表格

#盤點導入人員
apis.sptalentrv.prj.user.export.header.fullName=姓名
apis.sptalentrv.prj.user.export.header.userName=賬號
apis.sptalentrv.prj.user.export.file.name=導入盤點人員模板
apis.sptalentrv.prj.user.export.validation.user.exist=人員已存在
apis.sptalentrv.prj.user.export.validation.user.nonexist=人員不存在/賬號被禁用
apis.sptalentrv.prj.user.export.validation.user.evaluation=收費測評額度不足，無法繼續添加
apis.sptalentrv.prj.user.export.validation.user.study=雲方案可添加人數份額不足，無法繼續添加
apis.sptalentrv.prj.user.export.validation.user.name.empty=賬號為空
apis.sptalentrv.prj.user.export.validation.user.repeat=賬號重复

#導出動態匹配任務下員工跟踪信息
apis.sptalentrv.dmp.taskuser.export.file.name=動態匹配任務員工跟踪信息
apis.sptalentrv.dmp.taskuser.export.header.fullUserName=姓名
apis.sptalentrv.dmp.taskuser.export.header.statusDesc=賬號狀態
apis.sptalentrv.dmp.taskuser.export.header.deptName=部門
apis.sptalentrv.dmp.taskuser.export.header.matchedDimCount=達標數
apis.sptalentrv.dmp.taskuser.export.header.unMatchedDimCount=未達標數
apis.sptalentrv.dmp.taskuser.export.task.name=動態匹配任務員工跟踪信息


apis.sptalentrv.team.rv.detail.export.file.name=動態人崗匹配導出人員明細
apis.sptalentrv.team.rv.detail.export.sheet1.name=維度達標率
apis.sptalentrv.team.rv.detail.export.sheet1.header.cataName=維度分類
apis.sptalentrv.team.rv.detail.export.sheet1.header.dimName=維度名稱
apis.sptalentrv.team.rv.detail.export.sheet1.header.achievedUserCount=達標人數
apis.sptalentrv.team.rv.detail.export.sheet1.header.unAchievedUserCount=未達標人數
apis.sptalentrv.team.rv.detail.export.sheet1.header.achievedRate=達標率
apis.sptalentrv.team.rv.detail.export.sheet2.name=學員達標明細
apis.sptalentrv.team.rv.detail.export.sheet2.header.cataName=維度分類
apis.sptalentrv.team.rv.detail.export.sheet2.header.dimName=維度名稱
apis.sptalentrv.team.rv.detail.export.sheet2.header.fullname=人員姓名
apis.sptalentrv.team.rv.detail.export.sheet2.header.username=人員賬號
apis.sptalentrv.team.rv.detail.export.sheet2.header.achieved=是否達標

# 人崗匹配概覽-按人員導出
apis.sptalentrv.dmp.user_result.export.file.name=人員明細導出
apis.sptalentrv.dmp.user_result.export.sheet1.fullname=姓名
apis.sptalentrv.dmp.user_result.export.sheet1.username=賬號
apis.sptalentrv.dmp.user_result.export.sheet1.pos_name=崗位
apis.sptalentrv.dmp.user_result.export.sheet1.dept_name=部門
apis.sptalentrv.dmp.user_result.export.sheet1.match_type=匹配方式
apis.sptalentrv.dmp.user_result.export.sheet1.actual_score=實際得分
apis.sptalentrv.dmp.user_result.export.sheet1.if_matched=是否匹配
apis.sptalentrv.dmp.user_result.export.sheet1.match_rate=匹配率
apis.sptalentrv.dmp.user_result.export.sheet1.score=分值
apis.sptalentrv.dmp.user_result.export.sheet1.match_layer=匹配等級

#人崗匹配概覽-按維度導出
apis.sptalentrv.dmp.dim_result.export.file.name=維度明細導出
apis.sptalentrv.dmp.dim_result.export.sheet1.dim_cata_name=維度分類
apis.sptalentrv.dmp.dim_result.export.sheet1.dim_item_name=維度項
apis.sptalentrv.dmp.dim_result.export.sheet1.dim_name=維度
apis.sptalentrv.dmp.dim_result.export.sheet1.matched_user_cnt=達標人數
apis.sptalentrv.dmp.dim_result.export.sheet1.unmatched_user_cnt=未達標人數
apis.sptalentrv.dmp.dim_result.export.sheet1.total_user_cnt=總人數
apis.sptalentrv.dmp.dim_result.export.sheet1.matched_rate=達標率

#############################################################################################
###                                    業務日誌國際化                                       ###
#############################################################################################
# com.yxt.talent.rv.infrastructure.service.audit.dmp
com.yxt.talent.rv.domain.dmp.dmp.dmp_name=項目名稱
com.yxt.talent.rv.domain.dmp.dmp.pos_name=崗位
com.yxt.talent.rv.domain.dmp.dmp.jq_name=任職資格
com.yxt.talent.rv.domain.dmp.dmp.start_time=開始時間
com.yxt.talent.rv.domain.dmp.dmp.end_time=結束時間
com.yxt.talent.rv.domain.dmp.dmp.leader_name=負責人
com.yxt.talent.rv.domain.dmp.dmp.remark=項目說明

#com.yxt.talent.rv.infrastructure.service.audit.dmp.task
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.task_name=方案名稱
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.task_type=方案類型
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.eval_relations=評估關係
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.dmp_task_dims=維度
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.remark=方案說明

#com.yxt.talent.rv.infrastructure.service.audit.dmp.user
com.yxt.talent.rv.domain.dmp.entity.user.dmp_user.full_user_name=學員

#盘点校准人员导入
apis.sptalentrv.cali.username.empty=請輸入帳號
apis.sptalentrv.cali.username.not.exist=帳號不存在
apis.sptalentrv.cali.user.not.in.rv=帳號不在盤點項目中
apis.sptalentrv.cali.user.error.file=導入校準人員錯誤
apis.sptalentrv.cali.userName=員工帳號
apis.sptalentrv.cali.fullName=員工姓名
apis.sptalentrv.cali.excel.sheet.name=校準人員
apis.sptalentrv.cali.errorMsg=錯誤原因
apis.sptalentrv.cali.excel.language=tw

