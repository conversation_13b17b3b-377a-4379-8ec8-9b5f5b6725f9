

create table rv_cali_dim_comb_result
(
    id             	   char(36)         not null comment 'id',
    org_id             char(36)         not null comment '机构id',
    meeting_id         char(36)         not null default ''  comment '校准会id',
    meeting_user_id    char(36)         not null default ''  comment '校准会人员表id rv_meeting_user.id',
    user_id            char(36)         not null default ''  comment '被校准人id',
    cali_range         tinyint          default '0' comment '校准幅度',
    init_index         tinyint          comment '校准前宫格',
    last_index         tinyint          comment '校准后宫格',
    cali_user_id       char(36)         comment '准人id',
    cali_reason		   varchar(2000)    comment '活动/项目名称（冗余）',
    deleted            tinyint unsigned not null default '0' comment '是否删除',
    create_time        datetime(3)      not null comment '创建时间',
    create_user_id     char(36)         not null comment '创建人id',
    update_time        datetime(3)      not null comment '更新时间',
    update_user_id     char(36)         not null comment '更新人id',
    db_create_time     datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time     datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    primary key (id),
    key idx_org_meeting_user (org_id, meeting_id, user_id)
) comment = '校准会维度组合结果表';


create table rv_cali_dim_comb_result_his
(
    id             	   char(36)         not null comment 'id',
    org_id             char(36)         not null comment '机构id',
    meeting_id         char(36)         not null default ''  comment '校准会id',
    meeting_user_id    char(36)         not null default ''  comment '校准会人员表id rv_meeting_user.id',
    user_id            char(36)         not null default ''  comment '被校准人id',
    cali_range         tinyint          default '0' comment '校准幅度',
    init_index         tinyint          comment '校准前宫格',
    last_index         tinyint          comment '校准后宫格',
    cali_user_id       char(36)         comment '准人id',
    cali_reason		   varchar(2000)    comment '活动/项目名称（冗余）',
    deleted            tinyint unsigned not null default '0' comment '是否删除',
    create_time        datetime(3)      not null comment '创建时间',
    create_user_id     char(36)         not null comment '创建人id',
    update_time        datetime(3)      not null comment '更新时间',
    update_user_id     char(36)         not null comment '更新人id',
    db_create_time     datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time     datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    primary key (id),
    key idx_org_meeting_user (org_id, meeting_id, user_id)
) comment = '校准会维度组合结果历史表';


create table rv_meeting_user
(
    id             char(36)                                  not null comment '主键' primary key,
    org_id         char(36)                                  not null comment '机构id',
    project_id     char(36)                                  not null comment '盘点项目id',
    meeting_id     char(36)                                  not null comment '校准会id',
    user_id        char(36)                                  not null comment '人员id',
    suggestion     varchar(800) default ''                   null     comment '发展建议',

    create_user_id char(36)                                  not null comment '创建人主键',
    create_time    datetime(3)                               not null comment '创建时间',
    update_user_id char(36)                                  not null comment '更新人主键',
    update_time    datetime(3)                               not null comment '更新时间',
    db_create_time datetime(3)  default CURRENT_TIMESTAMP(3) null comment '数据创建时间(数据库专用，禁止用于业务)',
    db_update_time datetime(3)  default CURRENT_TIMESTAMP(3) null on update CURRENT_TIMESTAMP(3) comment '数据修改时间(数据库专用，禁止用于业务)',
    cali_reason    varchar(800) default ''                   null     comment '校准原因',
    master_record  tinyint unsigned not null default '1' 		      comment '主记录，1是，0否'
)
    comment '校准会人员关系表' row_format = DYNAMIC;


create table rv_calibration_meeting
(
    id                char(36)                                      not null comment '主键' primary key,
    org_id            char(36)                                      not null comment '机构id',
    project_id        char(36)                                      not null comment '盘点项目id',
    meet_status       tinyint          default 0                    not null comment '校准会状态（0-未开始，1-进行中，2-已结束）',
    meet_name         varchar(50)                                   not null comment '会议名称',
    meet_time         datetime(3)                                   null comment '会议时间',
    create_user_id    char(36)                                      not null comment '创建人主键',
    create_time       datetime(3)                                   not null comment '创建时间',
    update_user_id    char(36)                                      not null comment '更新人主键',
    update_time       datetime(3)                                   not null comment '更新时间',
    meet_minutes      varchar(500)     default ''                   not null comment '会议记录',
    deleted           tinyint unsigned default '0'                  not null comment '是否删除(0-否,1-是)',
    meet_route_status tinyint          default 1                    not null comment '显示校准路径 0 关闭 1开启',
    db_create_time    datetime(3)      default CURRENT_TIMESTAMP(3) null comment '数据创建时间(数据库专用，禁止用于业务)',
    db_update_time    datetime(3)      default CURRENT_TIMESTAMP(3) null on update CURRENT_TIMESTAMP(3) comment '数据修改时间(数据库专用，禁止用于业务)',
    duration          int unsigned     default '0'                  not null comment '会议持续时间，单位分钟（用于绚星工作场会议）',
    meet_no           varchar(36)                                   null comment '会议号（11位号码）',
    meet_id           varchar(36)                                   null comment '会议id',
    cali_form		  tinyint          default 0                    not null comment '组织形式（0-在线校准，1-线下校准）',
    cali_type		  tinyint          default 0                    not null comment '校准方式（0-维度分层结果，1-维度结果，2-指标结果）',
    meet_end_time     datetime(3)                                   null comment '会议结束时间',
    cali_ratio        tinyint unsigned default '0'                  not null comment '开启校准比例控制，0否，1是'
)
    comment '校准会' ;


