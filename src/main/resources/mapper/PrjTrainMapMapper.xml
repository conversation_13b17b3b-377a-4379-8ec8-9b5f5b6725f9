<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjTrainMapMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.train.PrjTrainMapPO">
        <!--@mbg.generated-->
        <!--@Table rv_project_training-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="project_id" jdbcType="CHAR" property="projectId"/>
        <result column="training_id" jdbcType="CHAR" property="trainingId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , project_id
             , training_id
             , deleted
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update rv_project_training
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.orgId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="project_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.projectId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="training_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.trainingId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.deleted,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.createUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.updateUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR}
                        then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=CHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_project_training
            (id,
             org_id,
             project_id,
             training_id,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.projectId,jdbcType=CHAR},
             #{item.trainingId,jdbcType=CHAR},
             #{item.deleted,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="countByOrgIdAndTrainingId" resultType="int">
        select count(*)
        from rv_project_training a
        where a.org_id = #{orgId}
          and a.training_id = #{trainingId}
          and a.deleted = 0
    </select>

    <select id="selectByOrgIdAndProjectId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_project_training a
        where a.deleted = 0
          and a.org_id = #{orgId}
          and a.project_id = #{prjId}
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_project_training a
        where a.org_id = #{orgId}
    </select>


    <select id="selectTrainingByOrgIdAndProjectId" resultType="java.lang.String">
        select distinct a.training_id
        from rv_project_training a
        where a.org_id = #{orgId}
          and a.project_id = #{prjId}
          and a.deleted = 0
    </select>

    <select id="selectByProjectIdAndTrainingIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_project_training a
        where a.org_id = #{orgId}
          and a.project_id = #{prjId}
          and a.deleted = 0
        <choose>
            <when test="trainingIds != null and trainingIds.size() != 0">
                and a.training_id in
                <foreach collection="trainingIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByPrjId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_project_training
        where project_id = #{prjId}
          and org_id = #{orgId}
          and deleted = 0
    </select>

    <insert id="batchInsertOrUpdate">
        <!--@mbg.generated-->
        insert into rv_project_training
            (id,
             org_id,
             project_id,
             training_id,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.projectId,jdbcType=CHAR},
             #{item.trainingId,jdbcType=CHAR},
             #{item.deleted,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            org_id         = values(org_id),
            project_id     = values(project_id),
            training_id    = values(training_id),
            deleted        = values(deleted),
            update_user_id = values(update_user_id),
            update_time    = values(update_time)
        </trim>
    </insert>

    <select id="selectPrjIdByTrainId" resultType="java.lang.String">
        select distinct a.project_id
        from rv_project_training a
        inner join rv_project b
        on a.project_id = b.id
        and b.deleted = 0
        where a.org_id = #{orgId}
          and a.training_id = #{trainId}
          and a.deleted = 0
    </select>

    <select id="selectPrjIdByTrainIds" resultMap="BaseResultMap">
        select
         a.*
        from rv_project_training a
        inner join rv_project b
        on a.project_id = b.id
        and b.deleted = 0
        where a.org_id = #{orgId}
        and a.training_id in
        <foreach collection="trainIds" item="trainId" index="index" open="(" separator=","
                 close=")">
            #{trainId}
        </foreach>
        and a.deleted = 0
    </select>
</mapper>
