<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserImportResultMapper">
    <sql id="Base_Column_List">
        id
             , org_id
             , project_id
             , user_id
             , dimension_id
             , dimension_config_id
             , skill_id
             , score
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserImportResultPO">
        <!--@Table rv_prj_user_calc_import-->
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="project_id" property="projectId"/>
        <result column="user_id" property="userId"/>
        <result column="dimension_id" property="dimensionId"/>
        <result column="dimension_config_id" property="dimensionConfigId"/>
        <result column="skill_id" property="skillId"/>
        <result column="score" property="score"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="selectUserCountByPrjIdAndDimId" resultType="int">
        select count(distinct user_id)
        from rv_prj_user_calc_import
        where org_id = #{orgId}
          and project_id = #{projectId}
          and dimension_id = #{dimensionId}
    </select>

    <select id="selectByPrjIdAndUserId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserImportResultPO">
        select org_id, project_id, user_id, dimension_id, skill_id, max(score) as score
        from rv_prj_user_calc_import a
        where project_id = #{projectId}
          and user_id = #{userId}
          and org_id = #{orgId}
        group by org_id, project_id, dimension_id, skill_id
    </select>

    <select id="selectDimensionMaxScore"
            resultType="com.yxt.talent.rv.application.prj.dim.dto.PrjDimMaxScoreDTO">
        select a.dimension_id, ifnull(max(a.score), 0) as maxScore
        from rv_prj_user_calc_import a
        where a.org_id = #{orgId}
          and a.project_id = #{projectId}
        <choose>
            <when test="dimensionIds != null and dimensionIds.size() != 0">
                and a.dimension_id in
                <foreach collection="dimensionIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        group by a.dimension_id
    </select>

    <insert id="batchInsertOrUpdate">
        insert into rv_prj_user_calc_import
            (id,
             org_id,
             project_id,
             user_id,
             dimension_id,
             dimension_config_id,
             skill_id,
             score,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},
             #{item.orgId},
             #{item.projectId},
             #{item.userId},
             #{item.dimensionId},
             #{item.dimensionConfigId},
             #{item.skillId},
             #{item.score},
             #{item.createUserId},
             #{item.createTime},
             #{item.updateUserId},
             #{item.updateTime})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            org_id              = values(org_id),
            project_id          = values(project_id),
            user_id             = values(user_id),
            dimension_id        = values(dimension_id),
            dimension_config_id = values(dimension_config_id),
            skill_id            = values(skill_id),
            score               = values(score),
            update_user_id      = values(update_user_id),
            update_time         = values(update_time)
        </trim>
    </insert>

    <delete id="deleteBatch">
        delete from rv_prj_user_calc_import
        where org_id = #{orgId}
        <choose>
            <when test="removedIds != null and removedIds.size() != 0">
                and id in
                <foreach collection="removedIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <delete id="deleteByOrgIdAndPrjIdAndDimIdsAndUserIds">
        delete
        from rv_prj_user_calc_import
        where org_id = #{orgId}
          and project_id = #{prjId}
        <choose>
            <when test="dimIds != null and dimIds.size() != 0">
                and dimension_id in
                <foreach collection="dimIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <select id="selectByOrgIdAndPrjIdAndDimIdAndIfPrjUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_import
        where org_id = #{orgId}
          and project_id = #{prjId}
          and dimension_id = #{dimId}
        <if test="prjUserIds != null and prjUserIds.size() != 0">
            and user_id in
            <foreach collection="prjUserIds" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByOrgIdAndPrjIdAndDimId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_import
        where org_id = #{orgId}
          and project_id = #{prjId}
          and dimension_id = #{dimId}
    </select>

    <select id="selectByOrgIdAndPrjIdAndDimConfIdAndUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_import
        where org_id = #{orgId}
          and project_id = #{prjId}
          and dimension_config_id = #{dimConfId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_import
        where org_id = #{orgId}
    </select>

    <select id="selectByOrgIdAndPrjId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_import
        where org_id = #{orgId}
          and project_id = #{prjId}
    </select>
</mapper>
