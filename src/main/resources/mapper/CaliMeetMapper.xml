<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        id
             , org_id
             , project_id
             , meet_status
             , meet_name
             , meet_time
             , create_user_id
             , create_time
             , update_user_id
             , update_time
             , meet_minutes
             , deleted
             , meet_route_status
             , duration
             , meet_no
             , meet_id<!--@sql from rv_calibration_meeting -->
    </sql>
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="project_id" property="projectId"/>
        <result column="meet_status" property="meetStatus"/>
        <result column="meet_name" property="meetName"/>
        <result column="meet_time" property="meetTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="meet_minutes" property="meetMinutes"/>
        <result column="deleted" property="deleted"/>
        <result column="meet_route_status" property="meetRouteStatus"/>
        <result column="duration" property="duration"/>
        <result column="meet_no" property="meetNo"/>
        <result column="meet_id" property="meetId"/>
    </resultMap>
    <sql id="Base_Columns">
        <!--@sql select -->
        t1.id                       as id
             , t1.org_id            as orgid
             , t1.project_id        as projectid
             , t1.meet_status       as meetstatus
             , t1.meet_name         as meetname
             , t1.meet_time         as meettime
             , t1.duration          as duration
             , t1.meet_id           as meetid
             , t1.meet_no           as meetno
             , t1.create_user_id    as createuserid
             , t1.create_time       as createtime
             , t1.update_user_id    as updateuserid
             , t1.update_time       as updatetime
             , t1.deleted           as deleted
             , t1.meet_route_status as meetroutestatus
             , t1.duration          as duration
             , t1.meet_no           as meetno
             , t1.meet_id           as meetid
             , t1.meet_minutes      as meetminutes
        <!--@sql from rv_calibration_meeting t1 -->
    </sql>

    <select id="paging"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO">
        select t1.id
             , t1.org_id
             , t1.project_id
             , c.id as activeId
             , c.actv_name as projectName
             , t1.meet_status
             , t1.meet_name
             , t1.meet_time
             , t1.create_user_id
             , t1.create_time
             , t1.update_user_id
             , t1.update_time
             , t1.meet_minutes
             , t1.meet_route_status
             , t1.duration
             , t1.meet_no
             , t1.meet_id
        from rv_calibration_meeting t1
        join rv_xpd b on t1.project_id = b.id and b.deleted = 0 and b.org_id = t1.org_id
        join rv_activity c on c.org_id = t1.org_id and c.deleted = 0 and c.id = b.aom_prj_id
        where t1.org_id = #{orgId}
          and t1.deleted = 0
        <if test="search.projectId != '' and search.projectId != null">
            and t1.project_id = #{search.projectId}
        </if>
        <if test="search.keyword != '' and search.keyword != null">
            and (t1.meet_name like concat('%', #{search.escapedKeyword}, '%') escape '\\')
        </if>
        <if test="search.meetStatus != null and search.meetStatus != -1">
            and t1.meet_status = #{search.meetStatus}
        </if>
        <if test="search.userIds != null and search.userIds.size() > 0">
            and ( t1.create_user_id in
            <foreach collection="search.userIds" item="userId" open="(" separator="," close=")">#{userId}
            </foreach>
            <if test="search.meetingIds != null and search.meetingIds.size() > 0">
                or t1.id in
                <foreach collection="search.meetingIds" item="meetingId" open="(" separator="," close=")">#{meetingId}
                </foreach>
            </if>
            )

        </if>
        order by t1.create_time desc
    </select>

    <select id="pagingVO"
            resultType="com.yxt.talent.rv.controller.client.general.calimeet.viewobj.CaliMeetClientVO">
        select t2.id
             , t2.meet_name   as meetname
             , t2.duration    as duration
             , t2.project_id  as projectid
             , t2.meet_status as meetstatus
             , t2.meet_time   as meettime
        from rv_calibration_meeting_user t1
        join rv_calibration_meeting      t2 on t1.org_id = t2.org_id and t1.meeting_id = t2.id
        join rv_xpd c on c.org_id = t2.org_id and c.deleted = 0 and c.id = t2.project_id
        where t2.org_id = #{orgId}
          and t2.deleted = 0
          and t1.user_id = #{userId}

        <if test="search.userType != null and search.userType != 0">
            and t1.user_type = #{search.userType}
        </if>

        <if test="search.meetStatus != null and search.meetStatus != -1">
            and t2.meet_status = #{search.meetStatus}
            <!--<if test="search.meetStatus == 2">
                and t2.meet_status = #{search.meetStatus}
            </if>
            <if test="search.meetStatus == 1">
                and t2.meet_time &lt;= #{nowDate}
                and t2.meet_status != 2
            </if>
            <if test="search.meetStatus == 0">
                and t2.meet_time > #{nowDate}
            </if>-->
        </if>

        <if test="search.keyword != '' and search.keyword != null">
            and (t2.meet_name like concat('%', #{search.escapedKeyword}, '%') escape '\\')
        </if>
        group by t2.id, t2.meet_name, t2.duration, t2.project_id, t2.meet_status, t2.meet_time
        order by t2.meet_time desc
    </select>

    <select id="listByMeetTimeRange"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO">
        select rcm.id, rcm.org_id, rcm.meet_time, rcm.duration, rcm.meet_id, rcm.meet_no
        from rv_calibration_meeting rcm
        left join udp_org u on rcm.org_id = u.id
        where rcm.deleted = 0
          and rcm.meet_time &gt;= #{startTime}
          and rcm.meet_time &lt; #{endTime}
          and u.deleted = 0
          and u.domain is not null
          and u.domain != ''
          and u.end_date > DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByMeetStatusAndIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting
        <where>
            meet_status = #{meetStatus}
                and deleted = 0
            <if test="idCollection != null and idCollection.size() > 0">
                and id in
                <foreach item="item" index="index" collection="idCollection" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="countByOrgIdAndProjectIdAndMeetNameAndIdNot" resultType="java.lang.Integer">
        select count(1)
        from rv_calibration_meeting
        <where>
            org_id = #{orgId}
                and project_id = #{projectId}
                and meet_name = #{meetName}
                and deleted = 0
            <if test="notId != null and notId != ''">
                and id <![CDATA[<>]]> #{notId}
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="countByOrgIdAndId" resultType="java.lang.Integer">
        select count(1) from rv_calibration_meeting where org_id = #{orgId} and id = #{id}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="selectByIdAndOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting
        where org_id = #{orgId}
          and id = #{id}
          and deleted = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="selectByIdAndOrgIdAndDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting
        where id = #{id}
          and org_id = #{orgId}
          and deleted = #{deleted}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="countByOrgIdAndProjectIdAndMeetStatus" resultType="java.lang.Integer">
        select count(1)
        from rv_calibration_meeting
        where org_id = #{orgId}
          and project_id = #{projectId}
          and deleted = 0
          and meet_status in (1)
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="countByOrgIdAndProjectId" resultType="java.lang.Integer">
        select count(1)
        from rv_calibration_meeting
        where org_id = #{orgId}
          and project_id = #{projectId}
          and deleted = 0
    </select>

    <select id="listByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting
        where org_id = #{orgId}
          and deleted = 0
    </select>
    <select id="getAllPrjCreators" resultType="java.lang.String">
        select create_user_id
        from rv_calibration_meeting
        where org_id = #{orgId}
          and deleted = 0
    </select>

    <update id="updateById">
        update rv_calibration_meeting
        <set>
            <if test="meetStatus != null">
                meet_status = #{meetStatus},
            </if>
            <if test="meetName != null">
                meet_name = #{meetName},
            </if>
            <if test="meetTime != null">
                meet_time = #{meetTime},
            </if>
            <if test="duration != null">
                duration = #{duration},
            </if>
            <if test="meetId != null">
                meet_id = #{meetId},
            </if>
            <if test="meetNo != null">
                meet_no = #{meetNo},
            </if>
            <if test="meetMinutes != null">
                meet_minutes = #{meetMinutes},
            </if>
            <if test="meetRouteStatus != null">
                meet_route_status = #{meetRouteStatus},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->

    <insert id="insertOrUpdate">
        insert into rv_calibration_meeting
            (id,
             org_id,
             project_id,
             meet_status,
             meet_name,
             meet_time,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             meet_minutes,
             deleted,
             meet_route_status,
             duration,
             meet_no,
             meet_id)
        values
            (#{element.id},
             #{element.orgId},
             #{element.projectId},
             #{element.meetStatus},
             #{element.meetName},
             #{element.meetTime},
             #{element.createUserId},
             #{element.createTime},
             #{element.updateUserId},
             #{element.updateTime},
             #{element.meetMinutes},
             #{element.deleted},
             #{element.meetRouteStatus},
             #{element.duration},
             #{element.meetNo},
             #{element.meetId})
        on duplicate key update
        <trim suffixOverrides=",">
            project_id        = values(project_id),
            meet_status       = values(meet_status),
            meet_name         = values(meet_name),
            meet_time         = values(meet_time),
            update_user_id    = values(update_user_id),
            update_time       = values(update_time),
            meet_minutes      = values(meet_minutes),
            deleted           = values(deleted),
            meet_route_status = values(meet_route_status),
            duration          = values(duration),
            meet_no           = values(meet_no),
            meet_id           = values(meet_id)
        </trim>
    </insert>

    <select id="countByUserScope" resultType="int">
        select count(distinct a.id) from rv_calibration_meeting a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and (
            a.create_user_id = #{userId}
            or
            exists(
                select 1 from rv_calibration_meeting_user b
                where b.user_id = #{userId}
                and b.meeting_id = a.id
                and b.user_type = 1 -- 只转移组织者
            )
        )
    </select>

    <update id="transferResource">
        update rv_calibration_meeting
        set create_user_id = #{toUserId},
        update_time = now()
        where create_user_id = #{fromUserId}
          and org_id = #{orgId}
          and deleted = 0
    </update>

    <insert id="batchInsertOrUpdate">
        insert into rv_calibration_meeting
            (id,
             org_id,
             project_id,
             meet_status,
             meet_name,
             meet_time,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             meet_minutes,
             deleted,
             meet_route_status,
             duration,
             meet_no,
             meet_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
             #{item.orgId},
             #{item.projectId},
             #{item.meetStatus},
             #{item.meetName},
             #{item.meetTime},
             #{item.createUserId},
             #{item.createTime},
             #{item.updateUserId},
             #{item.updateTime},
             #{item.meetMinutes},
             #{item.deleted},
             #{item.meetRouteStatus},
             #{item.duration},
             #{item.meetNo},
             #{item.meetId})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            project_id        = values(project_id),
            meet_status       = values(meet_status),
            meet_name         = values(meet_name),
            meet_time         = values(meet_time),
            update_user_id    = values(update_user_id),
            update_time       = values(update_time),
            meet_minutes      = values(meet_minutes),
            deleted           = values(deleted),
            meet_route_status = values(meet_route_status),
            duration          = values(duration),
            meet_no           = values(meet_no),
            meet_id           = values(meet_id)
        </trim>
    </insert>

    <update id="deleteMeetingByProjectId">
        update rv_calibration_meeting
        set deleted = 1,
        update_user_id = #{userId},
        update_time = now()
        where org_id = #{orgId}
          and project_id = #{projectId}
          and deleted = 0
    </update>

</mapper>
