<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserRemarkMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserRemarkPO">
        <!--@mbg.generated-->
        <!--@Table rv_meeting_user_remark-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="project_id" jdbcType="CHAR" property="projectId"/>
        <result column="meeting_id" jdbcType="CHAR" property="meetingId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
          , org_id
          , project_id
          , meeting_id
          , user_id
          , remark
          , create_user_id
          , create_time
          , update_user_id
          , update_time
    </sql>

    <select id="listByPrjIdAndMeetingIdAndUserId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserRemarkPO">
        select
        <include refid="Base_Column_List"/>
        , (
        select b.meet_name from rv_calibration_meeting b where b.id = a.meeting_id
        ) as meetingname
        from rv_meeting_user_remark a
        where
        a.org_id = #{orgId}
        and a.project_id = #{prjId}
        <if test="meetingId != null and meetingId != ''">
            and a.meeting_id = #{meetingId}
        </if>
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <update id="updateById">
        update rv_meeting_user_remark
        <set>
            <if test="updated.id != null and updated.id != ''">
                id = #{updated.id,jdbcType=CHAR},
            </if>
            <if test="updated.orgId != null and updated.orgId != ''">
                org_id = #{updated.orgId,jdbcType=CHAR},
            </if>
            <if test="updated.projectId != null and updated.projectId != ''">
                project_id = #{updated.projectId,jdbcType=CHAR},
            </if>
            <if test="updated.meetingId != null and updated.meetingId != ''">
                meeting_id = #{updated.meetingId,jdbcType=CHAR},
            </if>
            <if test="updated.userId != null and updated.userId != ''">
                user_id = #{updated.userId,jdbcType=CHAR},
            </if>
            <if test="updated.remark != null and updated.remark != ''">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.createUserId != null and updated.createUserId != ''">
                create_user_id = #{updated.createUserId,jdbcType=CHAR},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateUserId != null and updated.updateUserId != ''">
                update_user_id = #{updated.updateUserId,jdbcType=CHAR},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where
            id = #{updated.id,jdbcType=CHAR}
    </update>

    <insert id="insertOrUpdate">
        insert into rv_meeting_user_remark
            (id,
             org_id,
             project_id,
             meeting_id,
             user_id,
             remark,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
            (#{id,jdbcType=CHAR},
             #{orgId,jdbcType=CHAR},
             #{projectId,jdbcType=CHAR},
             #{meetingId,jdbcType=CHAR},
             #{userId,jdbcType=CHAR},
             #{remark,jdbcType=VARCHAR},
             #{createUserId,jdbcType=CHAR},
             #{createTime,jdbcType=TIMESTAMP},
             #{updateUserId,jdbcType=CHAR},
             #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="orgId != null and orgId != ''">
                org_id = #{orgId,jdbcType=CHAR},
            </if>
            <if test="projectId != null and projectId != ''">
                project_id = #{projectId,jdbcType=CHAR},
            </if>
            <if test="meetingId != null and meetingId != ''">
                meeting_id = #{meetingId,jdbcType=CHAR},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=CHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
