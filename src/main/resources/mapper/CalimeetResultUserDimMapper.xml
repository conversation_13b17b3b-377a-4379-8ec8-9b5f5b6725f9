<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetResultUserDimMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO">
    <!--@mbg.generated-->
    <!--@Table rv_calimeet_result_user_dim-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="user_id" property="userId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="grid_level_id" property="gridLevelId" />
    <result column="score_value" property="scoreValue" />
    <result column="qualified_ptg" property="qualifiedPtg" />
    <result column="perf_result_id" property="perfResultId" />
    <result column="calc_batch_no" property="calcBatchNo" />
    <result column="cali_flag" property="caliFlag" />
    <result column="original_snap" property="originalSnap" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="calimeet_id" property="calimeetId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg, 
    perf_result_id, calc_batch_no, cali_flag, original_snap, deleted, create_user_id, 
    create_time, update_user_id, update_time, calimeet_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_calimeet_result_user_dim
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_calimeet_result_user_dim
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dim (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, 
      qualified_ptg, perf_result_id, calc_batch_no, cali_flag, original_snap, 
      deleted, create_user_id, create_time, update_user_id, update_time, calimeet_id
      )
    values (#{id}, #{orgId}, #{xpdId}, #{userId}, #{sdDimId}, #{gridLevelId}, #{scoreValue}, 
      #{qualifiedPtg}, #{perfResultId}, #{calcBatchNo}, #{caliFlag}, #{originalSnap}, 
      #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}, #{calimeetId}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO">
    <!--@mbg.generated-->
    update rv_calimeet_result_user_dim
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      user_id = #{userId},
      sd_dim_id = #{sdDimId},
      grid_level_id = #{gridLevelId},
      score_value = #{scoreValue},
      qualified_ptg = #{qualifiedPtg},
      perf_result_id = #{perfResultId},
      calc_batch_no = #{calcBatchNo},
      cali_flag = #{caliFlag},
      original_snap = #{originalSnap},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      calimeet_id = #{calimeetId}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_calimeet_result_user_dim
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="sd_dim_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.sdDimId}
        </foreach>
      </trim>
      <trim prefix="grid_level_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.gridLevelId}
        </foreach>
      </trim>
      <trim prefix="score_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.scoreValue}
        </foreach>
      </trim>
      <trim prefix="qualified_ptg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.qualifiedPtg}
        </foreach>
      </trim>
      <trim prefix="perf_result_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.perfResultId}
        </foreach>
      </trim>
      <trim prefix="calc_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcBatchNo}
        </foreach>
      </trim>
      <trim prefix="cali_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.caliFlag}
        </foreach>
      </trim>
      <trim prefix="original_snap = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.originalSnap}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="calimeet_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calimeetId}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dim
    (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg, 
      perf_result_id, calc_batch_no, cali_flag, original_snap, deleted, create_user_id, 
      create_time, update_user_id, update_time, calimeet_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.sdDimId}, #{item.gridLevelId}, 
        #{item.scoreValue}, #{item.qualifiedPtg}, #{item.perfResultId}, #{item.calcBatchNo}, 
        #{item.caliFlag}, #{item.originalSnap}, #{item.deleted}, #{item.createUserId}, 
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, #{item.calimeetId}
        )
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dim
    (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg, 
      perf_result_id, calc_batch_no, cali_flag, original_snap, deleted, create_user_id, 
      create_time, update_user_id, update_time, calimeet_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.sdDimId}, #{item.gridLevelId}, 
        #{item.scoreValue}, #{item.qualifiedPtg}, #{item.perfResultId}, #{item.calcBatchNo}, 
        #{item.caliFlag}, #{item.originalSnap}, #{item.deleted}, #{item.createUserId}, 
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, #{item.calimeetId}
        )
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    user_id=values(user_id),
    sd_dim_id=values(sd_dim_id),
    grid_level_id=values(grid_level_id),
    score_value=values(score_value),
    qualified_ptg=values(qualified_ptg),
    perf_result_id=values(perf_result_id),
    calc_batch_no=values(calc_batch_no),
    cali_flag=values(cali_flag),
    original_snap=values(original_snap),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time),
    calimeet_id=values(calimeet_id)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dim
    (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg, 
      perf_result_id, calc_batch_no, cali_flag, original_snap, deleted, create_user_id, 
      create_time, update_user_id, update_time, calimeet_id)
    values
    (#{id}, #{orgId}, #{xpdId}, #{userId}, #{sdDimId}, #{gridLevelId}, #{scoreValue}, 
      #{qualifiedPtg}, #{perfResultId}, #{calcBatchNo}, #{caliFlag}, #{originalSnap}, 
      #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}, #{calimeetId}
      )
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    user_id = #{userId}, 
    sd_dim_id = #{sdDimId}, 
    grid_level_id = #{gridLevelId}, 
    score_value = #{scoreValue}, 
    qualified_ptg = #{qualifiedPtg}, 
    perf_result_id = #{perfResultId}, 
    calc_batch_no = #{calcBatchNo}, 
    cali_flag = #{caliFlag}, 
    original_snap = #{originalSnap}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}, 
    calimeet_id = #{calimeetId}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="sdDimId != null">
        sd_dim_id,
      </if>
      <if test="gridLevelId != null">
        grid_level_id,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="qualifiedPtg != null">
        qualified_ptg,
      </if>
      <if test="perfResultId != null">
        perf_result_id,
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no,
      </if>
      <if test="caliFlag != null">
        cali_flag,
      </if>
      <if test="originalSnap != null">
        original_snap,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="calimeetId != null">
        calimeet_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="sdDimId != null">
        #{sdDimId},
      </if>
      <if test="gridLevelId != null">
        #{gridLevelId},
      </if>
      <if test="scoreValue != null">
        #{scoreValue},
      </if>
      <if test="qualifiedPtg != null">
        #{qualifiedPtg},
      </if>
      <if test="perfResultId != null">
        #{perfResultId},
      </if>
      <if test="calcBatchNo != null">
        #{calcBatchNo},
      </if>
      <if test="caliFlag != null">
        #{caliFlag},
      </if>
      <if test="originalSnap != null">
        #{originalSnap},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="calimeetId != null">
        #{calimeetId},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="sdDimId != null">
        sd_dim_id = #{sdDimId},
      </if>
      <if test="gridLevelId != null">
        grid_level_id = #{gridLevelId},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue},
      </if>
      <if test="qualifiedPtg != null">
        qualified_ptg = #{qualifiedPtg},
      </if>
      <if test="perfResultId != null">
        perf_result_id = #{perfResultId},
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no = #{calcBatchNo},
      </if>
      <if test="caliFlag != null">
        cali_flag = #{caliFlag},
      </if>
      <if test="originalSnap != null">
        original_snap = #{originalSnap},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="calimeetId != null">
        calimeet_id = #{calimeetId},
      </if>
    </trim>
  </insert>

  <delete id="deleteUserDimResults">
    delete from rv_calimeet_result_user_dim a
    where a.org_id = #{orgId}
    and a.calimeet_id = #{caliMeetId}
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </delete>

  <select id="getByUserIdDimIds" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO">
    select id,
    user_id,
    sd_dim_id,
    grid_level_id,
    score_value,
    qualified_ptg,
    perf_result_id,
    calimeet_id from rv_calimeet_result_user_dim
    where org_id = #{orgId} and calimeet_id = #{caliMeetId} and user_id in
    <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    and deleted = 0
    <choose>
      <when test="dimIds != null and dimIds.size() != 0">
        and sd_dim_id in
        <foreach collection="dimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 = 2
      </otherwise>
    </choose>
  </select>

  <select id="selectByCaliMeetIdAndUserIds" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO">
    select rurd.id
         , rurd.org_id
         , rurd.xpd_id
         , rurd.user_id
         , rurd.sd_dim_id
         , rurd.grid_level_id
         , rurd.score_value
         , rurd.qualified_ptg
         , rurd.deleted
         , rurd.create_user_id
         , rurd.create_time
         , rurd.update_user_id
         , rurd.update_time
         , rurd.calc_batch_no
         , rurd.perf_result_id
         , rurd.cali_flag
         , rurd.original_snap
         , gl.level_name      as gridLevelName
         , gl.level_name_i18n as gridLevelNameI18n
         , gl.third_dim_color as thirdDimColor
         , gl.order_index     as gridLevelOrderIndex
    from rv_calimeet_result_user_dim rurd
    join rv_calimeet_user rcu ON rurd.org_id = rcu.org_id and rurd.user_id = rcu.user_id and rcu.deleted = 0 and rcu.calimeet_id = rurd.calimeet_id
    left join rv_xpd_grid_level gl on rurd.grid_level_id = gl.id
    where rurd.org_id = #{orgId}
    and rurd.calimeet_id = #{caliMeetId}
    and rurd.deleted = 0
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and rurd.user_id  in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>

  <select id="listByOrgIdAndCalimeetId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM rv_calimeet_result_user_dim
    WHERE org_id = #{orgId,jdbcType=VARCHAR}
      AND calimeet_id = #{calimeetId,jdbcType=VARCHAR}
      AND deleted = 0
  </select>
</mapper>