<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfConfMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfConfPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_perf_conf-->
    <id column="id" property="id" />
    <result column="actv_perf_id" property="actvPerfId" />
    <result column="org_id" property="orgId" />
    <result column="period_id" property="periodId" />
    <result column="weight" property="weight" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_user_id" property="updateUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="deleted" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, actv_perf_id, org_id, period_id, weight, create_user_id, update_user_id, create_time, 
    update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_activity_perf_conf
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfConfPO">
    <!--@mbg.generated-->
    insert into rv_activity_perf_conf (id, actv_perf_id, org_id, period_id, weight, create_user_id, 
      update_user_id, create_time, update_time, deleted)
    values (#{id}, #{actvPerfId}, #{orgId}, #{periodId}, #{weight}, #{createUserId}, 
      #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfConfPO">
    <!--@mbg.generated-->
    insert into rv_activity_perf_conf
    (id, actv_perf_id, org_id, period_id, weight, create_user_id, update_user_id, create_time, 
      update_time, deleted)
    values
    (#{id}, #{actvPerfId}, #{orgId}, #{periodId}, #{weight}, #{createUserId}, #{updateUserId}, 
      #{createTime}, #{updateTime}, #{deleted})
    on duplicate key update 
    id = #{id}, 
    actv_perf_id = #{actvPerfId}, 
    org_id = #{orgId}, 
    period_id = #{periodId}, 
    weight = #{weight}, 
    create_user_id = #{createUserId}, 
    update_user_id = #{updateUserId}, 
    create_time = #{createTime}, 
    update_time = #{updateTime}, 
    deleted = #{deleted}
  </insert>
    <!--auto generated by MybatisCodeHelper on 2024-12-09-->
    <insert id="insertList">
        INSERT INTO rv_activity_perf_conf(
        id,
        actv_perf_id,
        org_id,
        period_id,
        weight,
        create_user_id,
        update_user_id,
        create_time,
        update_time,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.actvPerfId},
            #{element.orgId},
            #{element.periodId},
            #{element.weight},
            #{element.createUserId},
            #{element.updateUserId},
            #{element.createTime},
            #{element.updateTime},
            #{element.deleted}
            )
        </foreach>
    </insert>
    <select id="selectByActivityId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_activity_perf_conf
        where org_id = #{orgId} and actv_perf_id = #{actvPerfId} and deleted = 0
    </select>

    <update id="deleteByActivityId">
        update rv_activity_perf_conf set deleted = 1 where org_id = #{orgId} and actv_perf_id = #{actvPerfId} and deleted = 0
    </update>

</mapper>