<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetUserResultMapper">
    <sql id="Base_Column_List">
        id
          , org_id
          , project_id
          , meeting_id
          , dimension_id
          , user_id
          , init_level
          , init_score
          , last_level
          , init_type
          , last_score
          , create_user_id
          , create_time
          , update_user_id
          , update_time
    </sql>
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetUserResultPO">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="project_id" property="projectId"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="dimension_id" property="dimensionId"/>
        <result column="user_id" property="userId"/>
        <result column="init_level" property="originalLevel"/>
        <result column="init_score" property="originalScore"/>
        <result column="last_level" property="calibrationLevel"/>
        <result column="init_type" property="initType"/>
        <result column="last_score" property="calibrationScore"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="paging"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.XpdUserGridResultVO">
        select
            temp.userid
          , u.username as username
          , u.fullname as fullname
          , u.img_url  as imgurl
          , temp.xvalue
          , temp.yvalue
          , temp.xcheckvalue
          , temp.ycheckvalue
        from (
                 select
                     user_id  as userid
                   , org_id   as orgid
                   , max(case dimension_id
                             when #{search.axisX}
                                 then init_level
                             else 0
                         end) as xvalue
                   , max(case dimension_id
                             when #{search.axisY}
                                 then init_level
                             else 0
                         end) as yvalue
                   , max(case dimension_id
                             when #{search.axisX}
                                 then last_level
                             else 0
                         end) as xcheckvalue
                   , max(case dimension_id
                             when #{search.axisY}
                                 then last_level
                             else 0
                         end) as ycheckvalue
                 from rv_calibration_meeting_result
                 where
                       org_id = #{orgId}
                   and project_id = #{search.projectId}
                   and meeting_id = #{search.meetingId}
                 group by user_id
                 having
                       max(case dimension_id
                               when #{search.axisX}
                                   then last_level
                               else 0
                           end) > 0
                   and max(case dimension_id
                               when #{search.axisY}
                                   then last_level
                               else 0
                           end) > 0
             ) as                       temp
             join udp_lite_user_sp u on u.id = temp.userid and u.org_id = #{orgId}
        where u.org_id = #{orgId}
          and temp.xcheckvalue = #{xValue}
          and temp.ycheckvalue = #{yValue}

        <if test="search.deptIds != null and search.deptIds.size() != 0">
            and u.dept_id in
            <foreach collection="search.deptIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="search.positionIds != null and search.positionIds.size() != 0">
            and u.position_id in
            <foreach collection="search.positionIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="search.gradeIds != null and search.gradeIds.size() != 0">
            and u.grade_id in
            <foreach collection="search.gradeIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <!--   禁用/启用    -->
        <if test="search.status != null and (search.status == 0 or search.status == 1)">
            and u.status = #{search.status}
            and u.deleted = 0
        </if>
        <!--   已删除   -->
        <if test="search.status != null and search.status == 2">
            and u.deleted = 1
        </if>

        <if test="(search.searchKey != '' and search.searchKey != null) or (search.userIds != null and search.userIds.size() != 0)">
          and(
            1=0
          <if test="search.searchKey != '' and search.searchKey != null">
              or ((u.username like concat('%', #{search.escapedKeyword}, '%') escape '\\') or
                (u.fullname like concat('%', #{search.escapedKeyword}, '%') escape '\\'))
          </if>

          <if test="search.userIds != null and search.userIds.size() != 0">
                or u.id in
                <foreach collection="search.userIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
           </if>

            )
        </if>

      <if test="search.authUserIds != null and search.authUserIds.size() != 0">
        and  u.id in
        <foreach collection="search.authUserIds" item="authUserId" open="(" separator="," close=")">
          #{authUserId}
        </foreach>
      </if>

    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgIdAndMeetIdAndUserIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting_result
        <where>
            org_id = #{orgId}
              and meeting_id = #{meetingId}
            <if test="userIdCollection != null and userIdCollection.size() > 0">
                and user_id in
                <foreach item="item" index="index" collection="userIdCollection" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgIdAndMeetIdAndUserIdAndDimIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting_result
        <where>
            org_id = #{orgId}
              and meeting_id = #{meetingId}
              and user_id = #{userId}
            <if test="dimensionIdCollection != null and dimensionIdCollection.size() > 0">
                and dimension_id in
                <foreach item="item" index="index" collection="dimensionIdCollection" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByMeetIdAndPrjIdAndDimIdInAndUserIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting_result
        <where>
            org_id = #{orgId}
              and meeting_id = #{meetId}
              and project_id = #{prjId}
            <if test="dimIds != null and dimIds.size() > 0">
                and dimension_id in
                <foreach item="item" index="index" collection="dimIds" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userIds != null and userIds.size() > 0">
                and user_id in
                <foreach item="item" index="index" collection="userIds" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <delete id="deleteByOrgIdAndMeetIdAndUserIdIn">
        delete
        from rv_calibration_meeting_result
        where
              org_id = #{orgId}
          and meeting_id = #{meetingId}
          and user_id in
        <foreach item="item" index="index" collection="userIdCollection" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </delete>

    <select id="listByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting_result
        <where>
            org_id = #{orgId}
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <update id="updateById">
        update rv_calibration_meeting_result
        <set>
            <if test="updated.id != null and updated.id != ''">
                id = #{updated.id},
            </if>
            <if test="updated.orgId != null and updated.orgId != ''">
                org_id = #{updated.orgId},
            </if>
            <if test="updated.projectId != null and updated.projectId != ''">
                project_id = #{updated.projectId},
            </if>
            <if test="updated.meetingId != null and updated.meetingId != ''">
                meeting_id = #{updated.meetingId},
            </if>
            <if test="updated.dimensionId != null and updated.dimensionId != ''">
                dimension_id = #{updated.dimensionId},
            </if>
            <if test="updated.userId != null and updated.userId != ''">
                user_id = #{updated.userId},
            </if>
            <if test="updated.originalLevel != null">
                init_level = #{updated.originalLevel},
            </if>
            <if test="updated.originalScore != null">
                init_score = #{updated.originalScore},
            </if>
            <if test="updated.calibrationLevel != null">
                last_level = #{updated.calibrationLevel},
            </if>
            <if test="updated.initType != null">
                init_type = #{updated.initType},
            </if>
            <if test="updated.calibrationScore != null">
                last_score = #{updated.calibrationScore},
            </if>
            <if test="updated.createUserId != null and updated.createUserId != ''">
                create_user_id = #{updated.createUserId},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime},
            </if>
            <if test="updated.updateUserId != null and updated.updateUserId != ''">
                update_user_id = #{updated.updateUserId},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime},
            </if>
        </set>
        where
            id = #{updated.id}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <insert id="insertList">
        insert into rv_calibration_meeting_result
            (id,
             org_id,
             project_id,
             meeting_id,
             dimension_id,
             user_id,
             init_level,
             init_score,
             last_level,
             init_type,
             last_score,
             create_user_id,
             create_time,
             update_user_id,
             update_time)values
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.orgId},
             #{element.projectId},
             #{element.meetingId},
             #{element.dimensionId},
             #{element.userId},
             #{element.originalLevel},
             #{element.originalScore},
             #{element.calibrationLevel},
             #{element.initType},
             #{element.calibrationScore},
             #{element.createUserId},
             #{element.createTime},
             #{element.updateUserId},
             #{element.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdate">
        insert into rv_calibration_meeting_result
            (id,
             org_id,
             project_id,
             meeting_id,
             dimension_id,
             user_id,
             init_level,
             init_score,
             last_level,
             init_type,
             last_score,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
            (#{id},
             #{orgId},
             #{projectId},
             #{meetingId},
             #{dimensionId},
             #{userId},
             #{originalLevel},
             #{originalScore},
             #{calibrationLevel},
             #{initType},
             #{calibrationScore},
             #{createUserId},
             #{createTime},
             #{updateUserId},
             #{updateTime})
        on duplicate key update
        <trim suffixOverrides=",">
            org_id         = values(org_id),
            project_id     = values(project_id),
            meeting_id     = values(meeting_id),
            dimension_id   = values(dimension_id),
            user_id        = values(user_id),
            init_level     = values(init_level),
            init_score     = values(init_score),
            last_level     = values(last_level),
            init_type      = values(init_type),
            last_score     = values(last_score),
            update_user_id = values(update_user_id),
            update_time    = values(update_time)
        </trim>
    </insert>

    <select id="countByOrgIdAndProjectIdAndMeetingId" resultType="java.lang.Long">
        select count(*)
        from rv_calibration_meeting_result
        where org_id = #{orgId} and project_id = #{projectId} and meeting_id = #{meetingId}
    </select>
</mapper>
