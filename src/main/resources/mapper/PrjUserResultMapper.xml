<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserResultMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO">
        <!--@mbg.generated-->
        <!--@Table rv_prj_user_calc_result-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="project_id" jdbcType="CHAR" property="projectId"/>
        <result column="dimension_id" jdbcType="CHAR" property="dimensionId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="original_level" jdbcType="TINYINT" property="initLevel"/>
        <result column="original_score" jdbcType="DECIMAL" property="initScore"/>
        <result column="calibration_level" jdbcType="TINYINT" property="lastLevel"/>
        <result column="calibration_score" jdbcType="DECIMAL" property="lastScore"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="original_perf_point" jdbcType="DECIMAL" property="originalPerfPoint"/>
        <result column="calibration_perf_point" jdbcType="DECIMAL" property="calibrationPerfPoint"/>
        <result column="perf_score" jdbcType="DECIMAL" property="perfScore"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , project_id
             , dimension_id
             , user_id
             , original_level
             , original_score
             , calibration_level
             , calibration_score
             , create_user_id
             , create_time
             , update_user_id
             , update_time
             , original_perf_point
             , calibration_perf_point
             , perf_score
    </sql>

    <select id="selectByXyAxis"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.XpdUserGridResultVO">
        select temp.userid, u.username, u.fullname as fullname, u.img_url as imgurl
        from (
            select user_id  as userid
                 , org_id   as orgid
                 , max(case dimension_id
                           when #{search.axisX}
                               then calibration_level
                           else 0
                       end) as xvalue
                 , max(case dimension_id
                           when #{search.axisY}
                               then calibration_level
                           else 0
                       end) as yvalue

            from rv_prj_user_calc_result
            where org_id = #{orgId}
              and project_id = #{search.projectId}
            group by user_id
            having max(case dimension_id
                           when #{search.axisX}
                               then calibration_level
                           else 0
                       end) &gt; 0
               and max(case dimension_id
                           when #{search.axisY}
                               then calibration_level
                           else 0
                       end) &gt; 0
        ) as                  temp
        join udp_lite_user_sp u on u.id = temp.userid and u.org_id = temp.orgid
        where temp.xvalue = #{search.valueX}
          and temp.yvalue = #{search.valueY}
          and u.org_id = #{orgId}
        <choose>
            <when test="search != null and search.openAuth and !search.admin">
                <choose>
                    <when test="(search.scopeUserIds != null and search.scopeUserIds.size() != 0)
                    and (search.scopeDeptIds != null and search.scopeDeptIds.size() != 0)">
                        and (u.id in
                        <foreach close=")" collection="search.scopeUserIds" item="uid" open="("
                                 separator=",">
                            #{uid}
                        </foreach>
                        or
                        u.dept_id in
                        <foreach close=")" collection="search.scopeDeptIds" item="deptid" open="("
                                 separator=",">
                            #{deptid}
                        </foreach>
                        )
                    </when>
                    <when test="search.scopeUserIds != null and search.scopeUserIds.size() != 0">
                        and u.id in
                        <foreach close=")" collection="search.scopeUserIds" item="uid" open="("
                                 separator=",">
                            #{uid}
                        </foreach>
                    </when>
                    <when test="search.scopeDeptIds != null and search.scopeDeptIds.size() != 0">
                        and u.dept_id in
                        <foreach close=")" collection="search.scopeDeptIds" item="deptid" open="("
                                 separator=",">
                            #{deptid}
                        </foreach>
                    </when>
                    <otherwise>
                        <!--@ignoreSql-->
                        AND 1 != 1
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <if test="search.deptIds != null and search.deptIds.size() != 0">
                    and u.dept_id in
                    <foreach close=")" collection="search.deptIds" item="id" open="(" separator=",">
                        #{id}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test="search.positionIds != null and search.positionIds.size() != 0">
            and u.position_id in
            <foreach close=")" collection="search.positionIds" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="search.gradeIds != null and search.gradeIds.size() != 0">
            and u.grade_id in
            <foreach close=")" collection="search.gradeIds" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <!--   禁用/启用    -->
        <if test="search.status != null and (search.status == 0 or search.status == 1)">
            and u.status = #{search.status}
            and u.deleted = 0
        </if>
        <!--   已删除   -->
        <if test="search.status != null and search.status == 2">
            and u.deleted = 1
        </if>
        <!-- 排除删除状态 -->
        <if test="search.status != null and search.status == 3">
            and u.deleted = 0
        </if>

        <if test="(search.searchKey != '' and search.searchKey != null) or (search.userIds != null and search.userIds.size() != 0)">
            and(
            1=0
            <if test="search.searchKey != '' and search.searchKey != null">
                or ((u.username like concat('%', #{search.escapedSearchKey}, '%') escape '\\') or
                (u.fullname like concat('%', #{search.escapedSearchKey}, '%') escape '\\'))
            </if>

            <if test="search.userIds != null and search.userIds.size() != 0">
                or u.id in
                <foreach collection="search.userIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            )
        </if>
    </select>

    <select id="countUserByOrgIdAndPrjIdAndDimId" resultType="int">
        select count(distinct user_id)
        from rv_prj_user_calc_result
        where org_id = #{orgId}
          and project_id = #{projectId}
          and dimension_id = #{dimensionId}
          and calibration_level in (1, 2, 3)
    </select>

    <select id="selectUserDimensionResult"
            resultType="com.yxt.talent.rv.application.prj.user.dto.UserDimGridAppDTO">
        select a.project_id             as prjid
             , b.project_name           as prjname
             , a.dimension_id           as dimid
             , max(a.calibration_level) as calibrationlevel
        from rv_prj_user_calc_result a
        join rv_project              b on b.id = a.project_id
        where a.user_id = #{userId}
          and a.org_id = #{orgId}
          and b.deleted = 0
        group by a.project_id, a.user_id, a.dimension_id, b.create_time
        order by b.create_time desc
    </select>

    <select id="searchPage"
            resultType="com.yxt.talent.rv.controller.openapi.viewobj.PrjUserResultOpenVO">
        select a.project_id
             , a.dimension_id
             , a.user_id
             , b.third_user_id
             , a.calibration_level as calibrationlevel
             , a.calibration_score as calibrationscore
        from rv_prj_user_calc_result a
        join udp_lite_user_sp        b
             on a.user_id = b.id and b.third_user_id != '' and b.org_id = a.org_id
        where a.org_id = #{orgId}
        <if test="searchStartTime != null and searchStartTime != ''">
            and a.update_time &gt;= #{searchStartTime}
        </if>
        <if test="searchEndTime != null and searchEndTime != ''">
            and a.update_time  <![CDATA[<]]> #{searchEndTime}
        </if>
        <choose>
            <when test="projectIds != null and projectIds.size() != 0">
                and a.project_id in
                <foreach close=")" collection="projectIds" index="index" item="item" open="("
                         separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectPrjUserLatestResults"
            resultType="com.yxt.talent.rv.application.prj.user.dto.PrjUserSimpleResultDTO">
        select t.user_id
             , t.dimension_id
             , t.dimension_name
             , t.dimension_type
             , t.calibration_level
             , t.project_name
             , t.projectid
        from (
        select a.user_id
             , b.dimension_id
             , c.dimension_name
             , c.dimension_type
             , a.calibration_level
             , d.id                                                                                     as projectid
             , d.project_name
             , d.create_time
             , row_number() over (partition by a.user_id, c.dimension_type order by d.create_time desc) as ranks
        from rv_prj_user_calc_result a
        join rv_project              d on d.id = a.project_id and d.deleted = 0
        join rv_dimension_config     b on b.dimension_id = a.dimension_id and b.project_id = d.id
        join rv_dimension            c on c.id = b.dimension_id
        where a.org_id = #{orgId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and a.user_id in
                <foreach close=")" collection="userIds" index="index" item="item" open="("
                         separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        ) as t
        where t.ranks = 1
        order by t.user_id, t.dimension_type, t.dimension_id
    </select>

    <select id="listByPrjIdAndUserIds"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO">
        select id
             , org_id
             , project_id
             , dimension_id
             , user_id
             , original_level    as initlevel
             , original_score    as initscore
             , calibration_level as lastlevel
             , calibration_score as lastscore
        from rv_prj_user_calc_result a
        where a.org_id = #{orgId}
          and a.project_id = #{prjId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and a.user_id in
                <foreach close=")" collection="userIds" index="index" item="item" open="("
                         separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        order by a.user_id
    </select>

    <insert id="batchInsertOrUpdate">
        insert into rv_prj_user_calc_result
            (id,
             org_id,
             project_id,
             dimension_id,
             user_id,
             original_level,
             original_score,
             calibration_level,
             calibration_score,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             original_perf_point,
             calibration_perf_point,
             perf_score
             )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.projectId,jdbcType=CHAR},
             #{item.dimensionId,jdbcType=CHAR},
             #{item.userId,jdbcType=CHAR},
             #{item.initLevel,jdbcType=TINYINT},
             #{item.initScore,jdbcType=DECIMAL},
             #{item.lastLevel,jdbcType=TINYINT},
             #{item.lastScore,jdbcType=DECIMAL},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP},
             #{item.originalPerfPoint,jdbcType=DECIMAL},
             #{item.calibrationPerfPoint,jdbcType=DECIMAL},
             #{item.perfScore,jdbcType=DECIMAL}
             )
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            org_id            = values(org_id),
            project_id        = values(project_id),
            dimension_id      = values(dimension_id),
            user_id           = values(user_id),
            original_level    = values(original_level),
            original_score    = values(original_score),
            calibration_level = values(calibration_level),
            calibration_score = values(calibration_score),
            update_user_id    = values(update_user_id),
            update_time       = values(update_time),
            original_perf_point = values(original_perf_point),
            calibration_perf_point = values(calibration_perf_point),
            perf_score        = values(perf_score)
        </trim>
    </insert>

    <delete id="deleteBatch">
        delete from rv_prj_user_calc_result
        where org_id = #{orgId}
        <choose>
            <when test="removedIds != null and removedIds.size() != 0">
                and id in
                <foreach collection="removedIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <select id="selectByOrgIdAndPrjIdAndDimIdAndIfPrjUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_result
        where org_id = #{orgId}
        and project_id = #{prjId}
        and dimension_id = #{dimId}
        <if test="prjUserIds != null and prjUserIds.size() != 0">
            and user_id in
            <foreach collection="prjUserIds" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByOrgIdAndPrjIdAndUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_result
        where org_id = #{orgId}
        and project_id = #{prjId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgIdAndPrjIdAndLastLevelNe0AndIfUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_result
        where org_id = #{orgId}
        and project_id = #{prjId}
        and calibration_level != 0
        <if test="userIds != null and userIds.size() != 0">
            and user_id in
            <foreach collection="userIds" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>
    
    <delete id="removeByOrgIdAndPrjIdAndDimIdsAndUserIds">
        delete from rv_prj_user_calc_result
        where org_id = #{orgId}
        and project_id = #{prjId}
        <choose>
            <when test="dimIds != null and dimIds.size() != 0">
                and dimension_id in
                <foreach collection="dimIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>

        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <select id="selectByOrgIdAndPrjIdAndUserIdAndDimIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_result
        where org_id = #{orgId}
        and project_id = #{prjId}
        and user_id = #{userId}
        <choose>
            <when test="dimIds != null and dimIds.size() != 0">
                and dimension_id in
                <foreach collection="dimIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgIdAndPrjIdAndDimIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_result
        where org_id = #{orgId}
        and project_id = #{prjId}
        <choose>
            <when test="dimIds != null and dimIds.size() != 0">
                and dimension_id in
                <foreach collection="dimIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgIdAndUserIdAndPrjIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_result
        where org_id = #{orgId}
        and user_id = #{userId}
        <choose>
            <when test="prjIds != null and prjIds.size() != 0">
                and project_id in
                <foreach collection="prjIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_prj_user_calc_result
        where org_id = #{orgId}
    </select>
</mapper>
