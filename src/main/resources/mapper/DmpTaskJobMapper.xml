<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskJobMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskJobPO">
        <!--@mbg.generated-->
        <!--@Table rv_dmp_task_job-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="dmp_id" jdbcType="CHAR" property="dmpId"/>
        <result column="task_id" jdbcType="CHAR" property="taskId"/>
        <result column="job_status" jdbcType="TINYINT" property="jobStatus"/>
        <result column="calc_wait_time" jdbcType="TIMESTAMP" property="calcWaitTime"/>
        <result column="calc_start_time" jdbcType="TIMESTAMP" property="calcStartTime"/>
        <result column="calc_end_time" jdbcType="TIMESTAMP" property="calcEndTime"/>
        <result column="exception" jdbcType="VARCHAR" property="exception"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , dmp_id
             , task_id
             , job_status
             , calc_wait_time
             , calc_start_time
             , calc_end_time
             , exception
             , deleted
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <select id="selectByOrgIdAndId" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task_job
        where id = #{id,jdbcType=CHAR}
          and org_id = #{orgId}
          and deleted = 0
    </select>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskJobPO">
        <!--@mbg.generated-->
        insert into rv_dmp_task_job
            (id,
             org_id,
             dmp_id,
             task_id,
             job_status,
             calc_wait_time,
             calc_start_time,
             calc_end_time,
             exception,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
            (#{id,jdbcType=CHAR},
             #{orgId,jdbcType=CHAR},
             #{dmpId,jdbcType=CHAR},
             #{taskId,jdbcType=CHAR},
             #{jobStatus,jdbcType=TINYINT},
             #{calcWaitTime,jdbcType=TIMESTAMP},
             #{calcStartTime,jdbcType=TIMESTAMP},
             #{calcEndTime,jdbcType=TIMESTAMP},
             #{exception,jdbcType=VARCHAR},
             #{deleted,jdbcType=TINYINT},
             #{createUserId,jdbcType=CHAR},
             #{createTime,jdbcType=TIMESTAMP},
             #{updateUserId,jdbcType=CHAR},
             #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateById"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskJobPO">
        <!--@mbg.generated-->
        update rv_dmp_task_job
        set org_id          = #{orgId,jdbcType=CHAR},
            dmp_id          = #{dmpId,jdbcType=CHAR},
            task_id         = #{taskId,jdbcType=CHAR},
            job_status      = #{jobStatus,jdbcType=TINYINT},
            calc_wait_time  = #{calcWaitTime,jdbcType=TIMESTAMP},
            calc_start_time = #{calcStartTime,jdbcType=TIMESTAMP},
            calc_end_time   = #{calcEndTime,jdbcType=TIMESTAMP},
            exception       = #{exception,jdbcType=VARCHAR},
            deleted         = #{deleted,jdbcType=TINYINT},
            create_user_id  = #{createUserId,jdbcType=CHAR},
            create_time     = #{createTime,jdbcType=TIMESTAMP},
            update_user_id  = #{updateUserId,jdbcType=CHAR},
            update_time     = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=CHAR}
    </update>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskJobPO">
        <!--@mbg.generated-->
        insert into rv_dmp_task_job
            (id,
             org_id,
             dmp_id,
             task_id,
             job_status,
             calc_wait_time,
             calc_start_time,
             calc_end_time,
             exception,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
            (#{id,jdbcType=CHAR},
             #{orgId,jdbcType=CHAR},
             #{dmpId,jdbcType=CHAR},
             #{taskId,jdbcType=CHAR},
             #{jobStatus,jdbcType=TINYINT},
             #{calcWaitTime,jdbcType=TIMESTAMP},
             #{calcStartTime,jdbcType=TIMESTAMP},
             #{calcEndTime,jdbcType=TIMESTAMP},
             #{exception,jdbcType=VARCHAR},
             #{deleted,jdbcType=TINYINT},
             #{createUserId,jdbcType=CHAR},
             #{createTime,jdbcType=TIMESTAMP},
             #{updateUserId,jdbcType=CHAR},
             #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        <trim suffixOverrides=",">
            org_id          = #{orgId,jdbcType=CHAR},
            dmp_id          = #{dmpId,jdbcType=CHAR},
            task_id         = #{taskId,jdbcType=CHAR},
            job_status      = #{jobStatus,jdbcType=TINYINT},
            calc_wait_time  = #{calcWaitTime,jdbcType=TIMESTAMP},
            calc_start_time = #{calcStartTime,jdbcType=TIMESTAMP},
            calc_end_time   = #{calcEndTime,jdbcType=TIMESTAMP},
            exception       = #{exception,jdbcType=VARCHAR},
            deleted         = #{deleted,jdbcType=TINYINT},
            update_user_id  = #{updateUserId,jdbcType=CHAR},
            update_time     = #{updateTime,jdbcType=TIMESTAMP}
        </trim>
    </insert>

    <select id="selectLastByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task_job a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.task_id = #{taskId}
        order by a.create_time desc
        limit 1
    </select>

    <insert id="batchInsert">
        insert into rv_dmp_task_job
            (id,
             org_id,
             dmp_id,
             task_id,
             job_status,
             calc_wait_time,
             calc_start_time,
             calc_end_time,
             exception,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.dmpId,jdbcType=CHAR},
             #{item.taskId,jdbcType=CHAR},
             #{item.jobStatus,jdbcType=TINYINT},
             #{item.calcWaitTime,jdbcType=TIMESTAMP},
             #{item.calcStartTime,jdbcType=TIMESTAMP},
             #{item.calcEndTime,jdbcType=TIMESTAMP},
             #{item.exception,jdbcType=VARCHAR},
             #{item.deleted,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>
