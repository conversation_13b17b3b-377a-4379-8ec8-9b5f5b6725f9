<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetAttendeeMapper">
    <sql id="Base_Column_List">
        id
             , org_id
             , meeting_id
             , user_id
             , user_type
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetAttendeePO">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="user_id" property="userId"/>
        <result column="user_type" property="userType"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="listByMeetingIdAndUserType"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserSimpleVO">
        select t1.meeting_id as meetingid
             , t1.user_id    as userid
             , t2.fullname   as username
             , t1.user_type  as usertype
             , t2.img_url    as imgurl
        from rv_calibration_meeting_user t1
        join udp_lite_user_sp       t2 on t1.user_id = t2.id and t2.org_id = #{orgId}
        where t1.org_id = #{orgId}
        <if test="meetingId != null and meetingId != ''">
            and t1.meeting_id = #{meetingId}
        </if>

        <if test="userType != null">
            and t1.user_type = #{userType};
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgIdAndMeetingIdAndUserType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting_user
        where org_id = #{orgId}
          and meeting_id = #{meetingId}
          and user_type = #{userType}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgIdAndMeetingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting_user
        where org_id = #{orgId}
          and meeting_id = #{meetingId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <delete id="deleteByOrgIdAndCalimeetIdAndUserType">
        delete
        from rv_calibration_meeting_user
        where org_id = #{orgId}
          and meeting_id = #{meetingId}
          and user_type = #{userType}
    </delete>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calibration_meeting_user
        where org_id = #{orgId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <insert id="insertList">
        insert into rv_calibration_meeting_user
            (id,
             org_id,
             meeting_id,
             user_id,
             user_type,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.orgId},
             #{element.meetingId},
             #{element.userId},
             #{element.userType},
             #{element.createUserId},
             #{element.createTime},
             #{element.updateUserId},
             #{element.updateTime})
        </foreach>
    </insert>

    <update id="transferResource">
        UPDATE rv_calibration_meeting_user a
        SET a.user_id = #{toUserId},
            a.update_time = NOW()
        WHERE a.org_id = #{orgId}
          AND a.user_id = #{fromUserId}
          AND a.user_type = 1
    </update>
  <select id="listByOrgIdAndUserId4Org" resultType="java.lang.String">
    select
      meeting_id
    from rv_calibration_meeting_user
    where
    org_id = #{orgId,jdbcType=VARCHAR}
    and user_type = 1
    AND user_id in
    <foreach item="userId" index="index" collection="userIds" open="(" separator=","
             close=")">
      #{userId}
    </foreach>
  </select>

  <select id="listAllUserIds" resultType="java.lang.String">
    select
     user_id
    from rv_calibration_meeting_user
    where
    org_id = #{orgId,jdbcType=VARCHAR}
    and user_type = 1
  </select>

</mapper>
