<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO">
        <!--@mbg.generated-->
        <!--@Table rv_activity_perf-->
        <id column="id" property="id"/>
        <result column="aom_act_id" property="aomActId"/>
        <result column="model_id" property="modelId"/>
        <result column="org_id" property="orgId"/>
        <result column="actv_name" property="actvName"/>
        <result column="period_ids" property="periodIds"/>
        <result column="indicator_id" property="indicatorId"/>
        <result column="eval_type" property="evalType"/>
        <result column="eval_time_type" property="evalTimeType"/>
        <result column="eval_time" property="evalTime"/>
        <result column="actv_desc" property="actvDesc"/>
        <result column="score_qualified" property="scoreQualified"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, aom_act_id, model_id, org_id, actv_name, period_ids, indicator_id, eval_type,
        eval_time_type, eval_time, actv_desc, score_qualified, create_user_id, update_user_id,
        create_time, update_time, deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf
        where id = #{id}
    </select>
    <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO">
        <!--@mbg.generated-->
        insert into rv_activity_perf (id, aom_act_id, model_id, org_id, actv_name, period_ids, indicator_id,
        eval_type, eval_time_type, eval_time, actv_desc, score_qualified, create_user_id,
        update_user_id, create_time, update_time, deleted)
        values (#{id}, #{aomActId}, #{modelId}, #{orgId}, #{actvName}, #{periodIds}, #{indicatorId},
        #{evalType}, #{evalTimeType}, #{evalTime}, #{actvDesc}, #{scoreQualified}, #{createUserId},
        #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO">
        <!--@mbg.generated-->
        insert into rv_activity_perf
        (id, aom_act_id, model_id, org_id, actv_name, period_ids, indicator_id, eval_type,
        eval_time_type, eval_time, actv_desc, score_qualified, create_user_id, update_user_id,
        create_time, update_time, deleted)
        values
        (#{id}, #{aomActId}, #{modelId}, #{orgId}, #{actvName}, #{periodIds}, #{indicatorId},
        #{evalType}, #{evalTimeType}, #{evalTime}, #{actvDesc}, #{scoreQualified}, #{createUserId},
        #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
        on duplicate key update
        id = #{id},
        aom_act_id = #{aomActId},
        model_id = #{modelId},
        org_id = #{orgId},
        actv_name = #{actvName},
        period_ids = #{periodIds},
        indicator_id = #{indicatorId},
        eval_type = #{evalType},
        eval_time_type = #{evalTimeType},
        eval_time = #{evalTime},
        actv_desc = #{actvDesc},
        score_qualified = #{scoreQualified},
        create_user_id = #{createUserId},
        update_user_id = #{updateUserId},
        create_time = #{createTime},
        update_time = #{updateTime},
        deleted = #{deleted}
    </insert>
    <update id="deleteById">
        update rv_activity_perf
        set deleted       = 1,
            update_time=now(),
            update_user_id=#{opUserId}
        where org_id = #{orgId}
          and id = #{id}
          and deleted = 0
    </update>
    <select id="selectById" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf
        where id = #{id} and deleted = 0 and org_id = #{orgId}
    </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf
        where deleted = 0 and org_id = #{orgId}
        <if test="(ids != null and ids.size()>0)">
            AND id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="findByXpdId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf
        where aom_act_id = #{xpdId} and deleted = 0 and org_id = #{orgId}
    </select>
    <select id="selectByAomActId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf
        where aom_act_id = #{aomActId} and deleted = 0 and org_id = #{orgId}
    </select>

  <select id="findAllValidActs" resultMap="BaseResultMap">
    select ap.id,
           ap.aom_act_id,
           ap.model_id,
           ap.org_id,
           ap.actv_name,
           ap.eval_time_type,
           ap.eval_time
    from rv_activity_perf ap
           join rv_activity ra on ra.org_id = ap.org_id and ra.id = ap.aom_act_id
    where ap.deleted = 0
      and ra.actv_status = 2
  </select>

    <select id="findAllPeriodIds" resultType="java.lang.String">
        select period_ids
        from rv_activity_perf p
                 inner join rv_activity a on a.org_id = p.org_id and p.aom_act_id = a.id and a.deleted = 0
        where p.org_id = #{orgId}
          and p.deleted = 0
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf
        where org_id = #{orgId}
        and deleted = 0
    </select>
</mapper>