<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="project_id" property="projectId"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="user_id" property="userId"/>
        <result column="suggestion" property="suggestion"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@sql select -->
        id
             , org_id
             , project_id
             , meeting_id
             , user_id
             , suggestion
             , create_user_id
             , create_time
             , update_user_id
             , update_time
        <!--@sql from rv_meeting_user a -->
    </sql>

    <select id="pagingVo"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserVO">
        select rmu.id
             , rmu.user_id                                    as userid
             , ru.fullname
             , ru.username
             , ru.dept_name                                   as deptname
             , ru.dept_id                                     as deptid
             , ru.position_id                                 as positionid
             , ru.position_name                               as positionname
             , if(ru.deleted = 1, 2, if(ru.status = 1, 0, 1)) as status
             , rmu.suggestion
        from rv_meeting_user  rmu
        join udp_lite_user_sp ru on rmu.user_id = ru.id and ru.org_id = #{orgId}
        where rmu.meeting_id = #{meetingId}
          and rmu.org_id = #{orgId}

        <if test="search.deptIds != null and search.deptIds.size > 0">
            and ru.dept_id in
            <foreach collection="search.deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>

        <if test="(qwUserIds != null and qwUserIds.size() != 0) or (search.keyword != null and search.keyword != '')">
          and(
              1=0
           <if test="qwUserIds != null and qwUserIds.size() != 0">
                or rmu.user_id in
                <foreach collection="qwUserIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <if test="search.keyword != null and search.keyword != ''">
                    or (ru.fullname like concat('%', #{search.escapedKeyword}, '%') escape
                         '\\' or ru.username like concat('%', #{search.escapedKeyword}, '%') escape '\\')
            </if>
              )
        </if>

        <if test="search.authUserIds != null and search.authUserIds.size > 0">
          and  rmu.user_id in
          <foreach collection="search.authUserIds" item="authUserId" open="(" separator="," close=")">
            #{authUserId}
          </foreach>
        </if>

    </select>

    <select id="selectVoByIdAndOrgId"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserVO">
        select rmu.id
             , rmu.user_id      as userid
             , ru.fullname
             , ud.id_full_path  as deptidfullpath
             , ru.dept_id       as deptid
             , ru.dept_name     as deptname
             , ru.position_id   as positionid
             , ru.position_name as positionname
             , ru.username
             , ru.img_url       as imgurl
             , rmu.suggestion
        from rv_meeting_user  rmu
        join udp_lite_user_sp ru on rmu.user_id = ru.id and ru.org_id = #{orgId}
        join udp_dept ud on ru.dept_id = ud.id
        where rmu.id = #{id}
          and rmu.org_id = #{orgId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-06-19-->
    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_meeting_user a
        where org_id = #{orgId}
          and meeting_id = #{meetingId}
        <choose>
            <when test="userIdCollection != null and userIdCollection.size() != 0">
                and user_id in
                <foreach collection="userIdCollection" item="item" index="index" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgIdAndMeetingIdAndIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_meeting_user
        where org_id = #{orgId}
          and meeting_id = #{meetingId}
          and id in
        <foreach item="item" index="index" collection="idCollection" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="selectByIdAndOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_meeting_user
        where id = #{id}
          and org_id = #{orgId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgIdAndMeetingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_meeting_user
        where org_id = #{orgId}
          and meeting_id = #{meetingId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <select id="listByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_meeting_user
        where org_id = #{orgId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <insert id="insertList">
        insert into rv_meeting_user(id,
                                    org_id,
                                    project_id,
                                    meeting_id,
                                    user_id,
                                    suggestion,
                                    create_user_id,
                                    create_time,
                                    update_user_id,
                                    update_time)values
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.orgId},
             #{element.projectId},
             #{element.meetingId},
             #{element.userId},
             #{element.suggestion},
             #{element.createUserId},
             #{element.createTime},
             #{element.updateUserId},
             #{element.updateTime})
        </foreach>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <update id="updateById">
        update rv_meeting_user
        <set>
            <if test="updated.id != null and updated.id != ''">
                id = #{updated.id},
            </if>
            <if test="updated.orgId != null and updated.orgId != ''">
                org_id = #{updated.orgId},
            </if>
            <if test="updated.projectId != null and updated.projectId != ''">
                project_id = #{updated.projectId},
            </if>
            <if test="updated.meetingId != null and updated.meetingId != ''">
                meeting_id = #{updated.meetingId},
            </if>
            <if test="updated.userId != null and updated.userId != ''">
                user_id = #{updated.userId},
            </if>
            <if test="updated.suggestion != null">
                suggestion = #{updated.suggestion},
            </if>
            <if test="updated.createUserId != null and updated.createUserId != ''">
                create_user_id = #{updated.createUserId},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime},
            </if>
            <if test="updated.updateUserId != null and updated.updateUserId != ''">
                update_user_id = #{updated.updateUserId},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime},
            </if>
        </set>
        where id = #{updated.id}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <delete id="deleteByOrgIdAndIdIn">
        delete from rv_meeting_user
        where org_id = #{orgId} and id in
        <foreach item="item" index="index" collection="idCollection" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </delete>
    <select id="selectByOrgIdAndUserIds" resultType="java.lang.String">
        select
          meeting_id
        from rv_meeting_user
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        AND user_id in
      <foreach item="userId" index="index" collection="userIds" open="(" separator=","
               close=")">
        #{userId}
      </foreach>
    </select>
  <select id="selectByMeetingId4UserIds" resultType="java.lang.String">
    select
      user_id
    from rv_meeting_user
    where
    org_id = #{orgId,jdbcType=VARCHAR}
    AND meeting_id = #{meetingId,jdbcType=VARCHAR}
  </select>
  <select id="countByMeetingId" resultType="java.lang.Long">
    select count(*)
    from rv_meeting_user
    where
    org_id = #{orgId}
    and  meeting_id = #{meetingId,jdbcType=VARCHAR}
  </select>
</mapper>
