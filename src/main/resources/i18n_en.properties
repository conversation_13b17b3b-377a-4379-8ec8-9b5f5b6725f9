#############################################################################################
###                                    Import and Export Internationalization              ###
#############################################################################################
# Evaluation Personnel Import Template
apis.sptalentrv.eval.user.export.header.fullName=Evaluated Person's Name
apis.sptalentrv.eval.user.export.header.userName=Evaluated Person's Account
apis.sptalentrv.eval.user.export.header.relationType=Evaluation Relationship
apis.sptalentrv.eval.user.export.header.evaluatorFullName=Evaluator's Name
apis.sptalentrv.eval.user.export.header.evaluatorIdUserName=Evaluator's Account
apis.sptalentrv.eval.user.export.file.name=Import Evaluation Relationship
apis.sptalentrv.performance.user.export.header.userName=Account
apis.sptalentrv.performance.user.export.header.fullName=Name
apis.sptalentrv.performance.user.export.header.period=Performance Period
apis.sptalentrv.performance.user.export.header.level=Performance
apis.sptalentrv.performance.user.export.header.perfScore=Performance Total Score
apis.sptalentrv.performance.user.export.header.perfPoint=Performance Score
apis.sptalentrv.performance.user.export.header.errorMsg=Error Message
apis.sptalentrv.perf.user.export.file.name=Performance Import

# Talent Inventory Results Export
apis.sptalentrv.prj.result.export.header.fullName=Name
apis.sptalentrv.prj.result.export.header.userName=Account
apis.sptalentrv.prj.result.export.header.status=Account Status
apis.sptalentrv.prj.result.export.header.deptName=Department
apis.sptalentrv.prj.result.export.header.positionName=Position
apis.sptalentrv.prj.result.export.header.result=Inventory Result
apis.sptalentrv.prj.result.export.file.name=Talent Inventory Results Export

# Import Inventory Results (Dimension Level)
apis.sptalentrv.prj.user.cacl.export.header.fullName=Name
apis.sptalentrv.prj.user.cacl.export.header.userName=Account
apis.sptalentrv.prj.user.cacl.export.header.deptName=Department
apis.sptalentrv.prj.user.cacl.export.header.positionName=Position
apis.sptalentrv.prj.user.cacl.export.header.dimensionName=Dimension Name
apis.sptalentrv.prj.user.cacl.export.header.dimensionLevel=Dimension Level
apis.sptalentrv.prj.user.cacl.export.header.dimensionModel=Dimension Model
apis.sptalentrv.prj.user.cacl.export.file.name=Import Inventory Results Template (Dimension Level)
apis.sptalentrv.prj.user.cacl.1.export.file.name=Import Inventory Results Template (Dimension Model Score)

# Calibration Meeting - Calibration Results Export
apis.sptalentrv.meeting.user.export.header.fullName=Name
apis.sptalentrv.meeting.user.export.header.userName=Account
apis.sptalentrv.meeting.user.export.header.deptName=Department
apis.sptalentrv.meeting.user.export.header.positionName=Position
apis.sptalentrv.meeting.user.export.header.suggestion=Development Suggestions
apis.sptalentrv.meeting.user.export.file.name=%s Meeting Inventory Results
apis.sptalentrv.meeting.user.export.template.level.file.name=Import Calibration Results (Dimension Level)
apis.sptalentrv.meeting.user.export.template.score.file.name=Import Calibration Results (Dimension Score)
apis.sptalentrv.meeting.user.import.level.error.file.name=Import Calibration Results Error (Dimension Level)
apis.sptalentrv.meeting.user.import.score.error.file.name=Import Calibration Results Error (Dimension Score)
apis.sptalentrv.perf.temp.export.file.name=Performance Period Template Export
apis.sptalentrv.perf.user.export.header.fullName=Employee Name
apis.sptalentrv.perf.user.export.header.userName=Employee Account
apis.sptalentrv.perf.user.export.header.period=Performance Period
apis.sptalentrv.perf.user.export.header.level=Performance
apis.sptalentrv.perf.user.export.header.perfScore=Performance Total Score
apis.sptalentrv.perf.user.export.header.perfPoint=Performance Score
apis.sptalentrv.prj.overview.export.header.deptName=Department
apis.sptalentrv.prj.overview.export.header.goodUser=Number of Outstanding Personnel
apis.sptalentrv.prj.overview.export.header.goodUserPct=Percentage of Outstanding Personnel
apis.sptalentrv.prj.overview.export.header.middleUser=Number of Core Personnel
apis.sptalentrv.prj.overview.export.header.middleUserPct=Percentage of Core Personnel
apis.sptalentrv.prj.overview.export.header.improveUser=Number of Personnel Needing Improvement
apis.sptalentrv.prj.overview.export.header.improveUserPct=Percentage of Personnel Needing Improvement
apis.sptalentrv.prj.overview.export.header.count=Count
apis.sptalentrv.prj.overview.export.header.percent=Percent
apis.sptalentrv.prj.overview.export.header.others=OthersCount
apis.sptalentrv.prj.overview.export.header.othersPct=OthersPercent
apis.sptalentrv.prj.overview.export.file.name=Inventory Project Talent Distribution Table

# Inventory Import Personnel
apis.sptalentrv.prj.user.export.header.fullName=Name
apis.sptalentrv.prj.user.export.header.userName=Account
apis.sptalentrv.prj.user.export.file.name=Import Inventory Personnel Template
apis.sptalentrv.prj.user.export.validation.user.exist=Personnel Already Exists
apis.sptalentrv.prj.user.export.validation.user.nonexist=Personnel Does Not Exist/Account Disabled
apis.sptalentrv.prj.user.export.validation.user.evaluation=Insufficient Paid Evaluation Quota, Unable to Add
apis.sptalentrv.prj.user.export.validation.user.study=Insufficient Cloud Plan Quota, Unable to Add
apis.sptalentrv.prj.user.export.validation.user.name.empty=Account is Empty
apis.sptalentrv.prj.user.export.validation.user.repeat=Account repeat

# Export Employee Tracking Information Under Dynamic Matching Task
apis.sptalentrv.dmp.taskuser.export.file.name=Dynamic Matching Task Employee Tracking Information
apis.sptalentrv.dmp.taskuser.export.header.fullUserName=Name
apis.sptalentrv.dmp.taskuser.export.header.statusDesc=Account Status
apis.sptalentrv.dmp.taskuser.export.header.deptName=Department
apis.sptalentrv.dmp.taskuser.export.header.matchedDimCount=Number of Dimensions Met
apis.sptalentrv.dmp.taskuser.export.header.unMatchedDimCount=Number of Dimensions Not Met
apis.sptalentrv.dmp.taskuser.export.task.name=Dynamic Matching Task Employee Tracking Information

apis.sptalentrv.team.rv.detail.export.file.name=Dynamic Job-Person Matching Export Personnel Details
apis.sptalentrv.team.rv.detail.export.sheet1.name=Dimension Achievement Rate
apis.sptalentrv.team.rv.detail.export.sheet1.header.cataName=Dimension Category
apis.sptalentrv.team.rv.detail.export.sheet1.header.dimName=Dimension Name
apis.sptalentrv.team.rv.detail.export.sheet1.header.achievedUserCount=Number of Achieved Personnel
apis.sptalentrv.team.rv.detail.export.sheet1.header.unAchievedUserCount=Number of Unachieved Personnel
apis.sptalentrv.team.rv.detail.export.sheet1.header.achievedRate=Achievement Rate
apis.sptalentrv.team.rv.detail.export.sheet2.name=Achiever Details
apis.sptalentrv.team.rv.detail.export.sheet2.header.cataName=Dimension Category
apis.sptalentrv.team.rv.detail.export.sheet2.header.dimName=Dimension Name
apis.sptalentrv.team.rv.detail.export.sheet2.header.fullname=Personnel Name
apis.sptalentrv.team.rv.detail.export.sheet2.header.username=Personnel Account
apis.sptalentrv.team.rv.detail.export.sheet2.header.achieved=Achieved or Not

# Overview of job-person matching - export by person
apis.sptalentrv.dmp.user_result.export.file.name=User Details Export
apis.sptalentrv.dmp.user_result.export.sheet1.fullname=Full Name
apis.sptalentrv.dmp.user_result.export.sheet1.username=Username
apis.sptalentrv.dmp.user_result.export.sheet1.pos_name=Position
apis.sptalentrv.dmp.user_result.export.sheet1.dept_name=Department
apis.sptalentrv.dmp.user_result.export.sheet1.match_type=Matching Method
apis.sptalentrv.dmp.user_result.export.sheet1.actual_score=Actual Score
apis.sptalentrv.dmp.user_result.export.sheet1.if_matched=Matched
apis.sptalentrv.dmp.user_result.export.sheet1.match_rate=Matching Rate
apis.sptalentrv.dmp.user_result.export.sheet1.score=Score
apis.sptalentrv.dmp.user_result.export.sheet1.match_layer=Matching Level

# Job-Person Matching Overview - Export by Dimension
apis.sptalentrv.dmp.dim_result.export.file.name=Dimension Details Export
apis.sptalentrv.dmp.dim_result.export.sheet1.dim_cata_name=Dimension Category
apis.sptalentrv.dmp.dim_result.export.sheet1.dim_item_name=Dimension Item
apis.sptalentrv.dmp.dim_result.export.sheet1.dim_name=Dimension
apis.sptalentrv.dmp.dim_result.export.sheet1.matched_user_cnt=Number of Achieved Personnel
apis.sptalentrv.dmp.dim_result.export.sheet1.unmatched_user_cnt=Number of Unachieved Personnel
apis.sptalentrv.dmp.dim_result.export.sheet1.total_user_cnt=Total Number of Personnel
apis.sptalentrv.dmp.dim_result.export.sheet1.matched_rate=Achievement Rate

#############################################################################################
###                                    Business Log Internationalization                    ###
#############################################################################################
# com.yxt.talent.rv.infrastructure.service.audit.dmp
com.yxt.talent.rv.domain.dmp.dmp.dmp_name=Project Name
com.yxt.talent.rv.domain.dmp.dmp.pos_name=Position
com.yxt.talent.rv.domain.dmp.dmp.jq_name=Job Qualification
com.yxt.talent.rv.domain.dmp.dmp.start_time=Start Time
com.yxt.talent.rv.domain.dmp.dmp.end_time=End Time
com.yxt.talent.rv.domain.dmp.dmp.leader_name=Leader
com.yxt.talent.rv.domain.dmp.dmp.remark=Project Description

# com.yxt.talent.rv.infrastructure.service.audit.dmp.task
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.task_name=Plan Name
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.task_type=Plan Type
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.eval_relations=Evaluation Relationships
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.dmp_task_dims=Dimensions
com.yxt.talent.rv.domain.dmp.entity.task.dmp_task.remark=Plan Description

# com.yxt.talent.rv.infrastructure.service.audit.dmp.user
com.yxt.talent.rv.domain.dmp.entity.user.dmp_user.full_user_name=Student

apis.sptalentrv.cali.username.empty=Please enter your account
apis.sptalentrv.cali.username.not.exist=account not exist
apis.sptalentrv.cali.user.not.in.rv=The account is not included in the inventory project
apis.sptalentrv.cali.user.error.file=Import calibration personnel error
apis.sptalentrv.cali.userName=Employee account
apis.sptalentrv.cali.fullName=Employee name
apis.sptalentrv.cali.excel.sheet.name=Calibration personnel
apis.sptalentrv.cali.errorMsg=Reason for error
apis.sptalentrv.cali.excel.language=en
