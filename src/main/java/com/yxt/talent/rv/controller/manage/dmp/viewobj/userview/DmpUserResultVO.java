package com.yxt.talent.rv.controller.manage.dmp.viewobj.userview;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 盘点结果概览
 *
 * <AUTHOR>
 * @Date 2024/4/25 10:33
 **/
@Data
public class DmpUserResultVO {

    private String userId;

    @Schema(description = "名称")
    private String fullname;

    @Schema(description = "账号")
    private String username;

    @Schema(description = "部门名称")
    private String deptName;


    @Schema(description = "岗位名称")
    private String positionName;

    @Schema(description = "头像")
    private String imgUrl;

    @Schema(description = "是否胜任, 0-不胜任 1-胜任")
    private Integer competent = 0;

    @Schema(description = "结果分层名称")
    private String layerName;

    @Schema(description = "匹配规则 0-按匹配率 1-按分值")
    private Integer matchType = 0;

    @Schema(description = "匹配率或者 分值")
    private BigDecimal score;

    @Schema(description = "维度数")
    private Integer dimCount = 0;


    @Schema(description = "达标维度数")
    private Integer matchDimCount = 0;

    @Schema(description = "学员结果计算状态是否已完成 0-未完成 1-已完成")
    private Integer complete = 0;
}
