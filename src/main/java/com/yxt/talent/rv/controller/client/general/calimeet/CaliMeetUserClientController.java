package com.yxt.talent.rv.controller.client.general.calimeet;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppManage;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserUpdateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserResultVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserVO;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static org.springframework.http.HttpStatus.OK;

/**
 * 校准会人员列表
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "客户端盘点校准会-校准人员", description = "客户端盘点校准会-校准人员")
@RequestMapping(value = "/client/meeting/user")
public class CaliMeetUserClientController {
    private final AuthService authService;
    private final CaliMeetAppService caliMeetAppService;
    private final CaliMeetAppManage caliMeetAppComponent;

    @SwaggerPageQuery
    @Operation(summary = "人员列表-查询时部门多选(客户端)")
    @PostMapping(value = "/list/{meetingId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public PagingList<CaliMeetUserVO> list(
            HttpServletRequest request, @PathVariable String meetingId,
            @RequestBody CaliMeetUserQuery search) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return caliMeetAppService.findPage(
                ApiUtil.getPageRequest(request), meetingId, search, userCache);
    }

    @Operation(summary = "获取详情(客户端)")
    @GetMapping(value = "/detail/{id}")
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public CaliMeetUserResultVO getMeetingDetail(
            HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppComponent.getMeetingUser4GetById(
                id, currentUser.getOrgId(), currentUser.getLocale());
    }

    @Operation(summary = "校准人员(客户端)")
    @PutMapping(value = "", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public void update(
            HttpServletRequest request,
            @RequestBody @Valid CaliMeetUserUpdateCmd caliMeetUserUpdateCmd) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppComponent.calibration(
                caliMeetUserUpdateCmd, operator.getUserId(), operator.getOrgId());
    }
}
