package com.yxt.talent.rv.controller.manage.meet.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import com.yxt.criteria.Command;
import com.yxt.talent.rv.controller.manage.common.command.AttachmentCreateCmd;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "创建校准会")
public class MeetCreateCmd implements Command {

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

    @Schema(description = "主键Id【编辑时必填】")
    private String id;

    @Schema(description = "会议名称（ 500 =< 长度 > 1 ）")
    @NotBlank(message = "apis.sptalentrv.calimeet.name.notBlank")
    @Size(max = 50, min = 1, message = "apis.sptalentrv.calimeet.name.size.exceed")
    private String meetName;

    @Schema(description = "所属Xpd盘点项目id")
    private String projectId = "";

    @NotNull(message = "apis.sptalentrv.calimeet.meetTime.notBlank")
    @Schema(description = "会议时间 【格式：2021-12-14 16:53:00】")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date meetTime;

    @Schema(description = "会议持续时间，单位：分钟，最短60，最长1440分钟（即1天）")
    private Integer duration;

    @Schema(description = "人才委员会人员Id列表")
    private List<String> talentCommitteeList = new ArrayList<>();

    @Schema(description = "会议组织者人员Id列表")
    private List<String> organizerList = new ArrayList<>();

    @Schema(description = "会议记录")
    @Size(max = 500, message = "apis.sptalentrv.calimeet.minutes.exceed")
    private String meetMinutes = "";

    @Schema(description = "附件列表")
    private List<AttachmentCreateCmd> appendixList = new ArrayList<>();
}
