package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
@Getter
@Setter
@Schema(description = "盘点宫格引用")
public class XpdGrid4Get implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description="盘点项目;按照业务需求,返回应用的实体字段")
    @JsonProperty("@projectid")
    private AmSlDrawer4RespDTO projectid;
    @Schema(description="状态")
    private String gridstate;
    @Schema(description="宫格配置类型")
    private String template;
    @Schema(description="宫格类型")
    private String gridtype;
    @Schema(description="来源")
    private String sourcetype;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description="模板描述")
    private String griddesc;
    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="模板名称;名称")
    private String name;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="配置方式")
    private String configtype;
}
