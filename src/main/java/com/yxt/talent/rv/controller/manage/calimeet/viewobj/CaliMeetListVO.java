package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会列表元素")
public class CaliMeetListVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "会议名称")
    private String meetName;

    @Schema(description = "所属盘点项目id")
    private String projectId;

    @Schema(description = "")
    private String activeId;

    @Schema(description = "所属盘点项目")
    private String projectName;

    @Schema(description = "校准会状态（0-未开始，1-进行中，2-已结束）")
    private Integer meetStatus;

    @Schema(description = "会议时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date meetTime;

    @Schema(description = "会议持续时间，用于绚星工作场会议 (单位分钟，最少60分钟，最长1440分钟（即1天）)")
    private Integer duration;

    @Schema(description = "人才委员会列表")
    private List<CaliMeetUserSimpleVO> talentCommitteeList = new ArrayList<>();

    @Schema(description = "会议组织者列表")
    private List<CaliMeetUserSimpleVO> organizerList = new ArrayList<>();

    @Schema(description = "创建人主键")
    private String createUserId;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date createTime;

    @Schema(description = "创建人名称")
    private String createUserName;

    @Schema(description = "校准人数")
    private Long userCount;

}
