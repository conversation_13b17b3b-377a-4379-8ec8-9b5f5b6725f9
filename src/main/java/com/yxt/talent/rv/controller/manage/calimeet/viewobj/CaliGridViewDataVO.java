package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 宫格概览
 *
 * @date 2025/5/21
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "宫格概览")
public class CaliGridViewDataVO {

    @Schema(description = "维度组宫格名称，以/拼接在一起")
    private String cellName;

    @Schema(description = "预设落位比例")
    private BigDecimal ratio;

    @Schema(description = "实际落位比例")
    private BigDecimal actualRatio;

    @Schema(description = "排序")
    private Integer orderIndex;

}
