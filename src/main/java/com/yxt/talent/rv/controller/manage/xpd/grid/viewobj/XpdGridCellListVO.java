package com.yxt.talent.rv.controller.manage.xpd.grid.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class XpdGridCellListVO
{

    private String id;

    @Schema(description = "格子名称")
    private String cellName;

    @Schema(description = "颜色")
    private String cellColor;

    @Schema(description = "文字颜色")
    private String textColor;

    @Schema(description = "格子编号")
    private Integer cellIndex;

    @Schema(description = "格子描述")
    private String cellDesc;

}
