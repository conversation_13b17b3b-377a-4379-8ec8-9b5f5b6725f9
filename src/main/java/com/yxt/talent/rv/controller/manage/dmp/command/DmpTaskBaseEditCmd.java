package com.yxt.talent.rv.controller.manage.dmp.command;

import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @Description 任务名称和说明编辑
 *
 * <AUTHOR>
 * @Date 2024/4/29 11:06
 **/
@Data
public class DmpTaskBaseEditCmd {

    @Schema(description = "任务类型,0-动态匹配 1-表单评价")
    private Integer taskType;

    @Schema(description = "任务名称")
    @Length(min = 0, max = 200, message = ExceptionKeys.DMP_TASK_NAME_TOO_LONG)
    private String taskName;

    @Schema(description = "任务描述")
    @Length(min = 0, max = 200, message = ExceptionKeys.DMP_TASK_DESC_TOO_LONG)
    private String taskDesc;
}
