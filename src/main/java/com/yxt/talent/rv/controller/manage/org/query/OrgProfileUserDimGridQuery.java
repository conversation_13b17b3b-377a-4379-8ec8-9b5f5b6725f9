package com.yxt.talent.rv.controller.manage.org.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "组织画像-管理端-查询员工维度九宫格")
public class OrgProfileUserDimGridQuery extends SearchUdpScopeAuthQuery {

    @JsonProperty("xDimId")
    @Schema(description = "x轴维度id", requiredMode = REQUIRED)
//    @NotBlank(message = ExceptionKeys.DMP_DIM_ID_IS_NULL)
    private String xDimId;

    @JsonProperty("yDimId")
    @Schema(description = "y轴维度id", requiredMode = REQUIRED)
//    @NotBlank(message = ExceptionKeys.DMP_DIM_ID_IS_NULL)
    private String yDimId;

    @Schema(description = "宫格id", requiredMode = REQUIRED)
    private String gridId;

    @Schema(description = "维度组id", requiredMode = REQUIRED)
    private String dimCombId;

    @Schema(description = "宫格编号，当查询宫格内人员分页列表时必传")
    private Integer cellIndex;

}
