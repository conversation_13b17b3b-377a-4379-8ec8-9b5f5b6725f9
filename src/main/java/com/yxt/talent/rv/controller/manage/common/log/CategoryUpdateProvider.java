package com.yxt.talent.rv.controller.manage.common.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.common.command.CategoryCreateCmd;
import com.yxt.talent.rv.controller.manage.common.command.CategoryUpdateCmd;
import com.yxt.talent.rv.controller.manage.common.viewobj.CategoryLogDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.CategoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.CategoryPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * @Description 更新盘点分类
 *
 * <AUTHOR>
 * @Date 2024/9/4 18:10
 **/

@Slf4j
@AllArgsConstructor
@Component
public class CategoryUpdateProvider implements AuditLogDataProvider<CategoryUpdateCmd, CategoryLogDTO> {

    private final CategoryMapper categoryMapper;

    @Override
    public CategoryLogDTO before(CategoryUpdateCmd param, AuditLogBasicBean logBasic) {
        CategoryPO categoryPO =
                categoryMapper.selectByOrgIdAndId(logBasic.getOrgId(), param.getCategoryId());
        if (categoryPO == null) {
            return null;
        }
        CategoryLogDTO dto = new CategoryLogDTO();
        dto.setCategoryName(categoryPO.getCategoryName());

        return dto;
    }

    @Override
    public CategoryLogDTO after(CategoryUpdateCmd param, AuditLogBasicBean logBasic) {
        CategoryLogDTO dto = new CategoryLogDTO();
        dto.setCategoryName(param.getCategoryName());

        return dto;
    }

    @Override
    public Pair<String, String> entityInfo(
            CategoryUpdateCmd param, CategoryLogDTO beforeObj, CategoryLogDTO afterObj,
            AuditLogBasicBean logBasic) {

        String name = AuditLogHelper.Module.SETTING.getName() + "-盘点分类-" + param.getCategoryName();
        return Pair.of(param.getCategoryId(), name);
    }
}
