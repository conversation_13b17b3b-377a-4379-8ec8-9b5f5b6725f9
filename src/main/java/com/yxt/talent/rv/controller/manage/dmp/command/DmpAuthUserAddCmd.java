package com.yxt.talent.rv.controller.manage.dmp.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpAuthUserAddCmd implements Command {

    @Schema(description = "人岗匹配项目id")
    private String dmpId;

    @Schema(description = "管理员")
    private List<String> leaders;

    @Schema(description = "查看")
    private List<String> viewUsers;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
