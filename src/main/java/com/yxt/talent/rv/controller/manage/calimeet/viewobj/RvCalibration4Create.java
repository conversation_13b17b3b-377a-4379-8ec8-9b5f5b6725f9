package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import java.lang.String;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.Date;
import jakarta.validation.constraints.NotNull;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import java.util.List;
import java.lang.Long;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
@Getter
@Setter
@Schema(description = "校准创建请求")
public class RvCalibration4Create implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="项目id", requiredMode = Schema.RequiredMode.REQUIRED )
    @NotBlank
    private String projectId;

    @Schema(description="校准会名称;名称", requiredMode = Schema.RequiredMode.REQUIRED )
    @NotBlank
    @Size(max=255)
    private String name;

    @Schema(description="会议时间", requiredMode = Schema.RequiredMode.REQUIRED )
    @NotNull
    private Date meettime;

    @Schema(description="会议组织者")
    private List<AmSlDrawer4ReqDTO> meetorganizator;

    @Schema(description="人才委员会")
    private List<AmSlDrawer4ReqDTO> meetcalibrator;

}
