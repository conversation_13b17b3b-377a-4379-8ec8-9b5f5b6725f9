package com.yxt.talent.rv.controller.client.general.calimeet.query;

import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会列表搜索")
public class CaliMeetClientQuery {

    @Schema(description = "关键字查询（目前是会议名称）")
    private String keyword;

    @Schema(description = "校准会状态（0-未开始，1-进行中，2-已结束，-1-全部）")
    private Integer meetStatus;

    @Schema(description = "角色类型（0-全部,1-组织者，2-参会人）")
    private Integer userType;

    public String getEscapedKeyword() {
        return SqlUtil.escapeSql(keyword);
    }
}
