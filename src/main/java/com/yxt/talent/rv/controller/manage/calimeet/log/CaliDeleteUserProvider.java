package com.yxt.talent.rv.controller.manage.calimeet.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserBatchAddCmd;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/4 17:20
 **/
@Slf4j
@AllArgsConstructor
@Component
public class CaliDeleteUserProvider implements AuditLogDataProvider<CaliMeetUserBatchAddCmd, String> {

    private final UdpLiteUserMapper udpLiteUserMapper;
    private final CaliMeetMapper caliMeetMapper;

    @Override
    public String before(CaliMeetUserBatchAddCmd param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public String after(CaliMeetUserBatchAddCmd param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public Pair<String, String> entityInfo(
            CaliMeetUserBatchAddCmd param, String beforeObj, String afterObj,
            AuditLogBasicBean logBasic) {
        String meetingId = param.getMeetingId();
        CaliMeetPO cm = caliMeetMapper.selectByIdAndOrgId(meetingId, logBasic.getOrgId());
        if (cm == null) {
            return null;
        }

        // 组装日志
        if (CollectionUtils.isEmpty(param.getUserIdList())) {
            return null;
        }


        String userString = getUserString(logBasic.getOrgId(), param.getUserIdList());

        String name = AuditLogHelper.Module.CALIBRATION.getName() + "-" + cm.getMeetName() + "-删除人员-" +
                      userString;
        return Pair.of(cm.getId(), name);

    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
                // .append("（").append(a.getUsername()).append("）")
                .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }
}
