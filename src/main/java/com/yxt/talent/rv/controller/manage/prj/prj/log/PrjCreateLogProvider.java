package com.yxt.talent.rv.controller.manage.prj.prj.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.prj.prj.command.PrjCreateCmd;
import com.yxt.talent.rv.controller.manage.prj.prj.viewobj.PrjCreate4LogDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.CategoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.CategoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * @Description 创建项目
 *
 * <AUTHOR>
 * @Date 2024/8/28 14:15
 **/

@Slf4j
@AllArgsConstructor
@Component
public class PrjCreateLogProvider implements AuditLogDataProvider<PrjCreateCmd, PrjCreate4LogDTO> {

    private final CategoryMapper categoryMapper;

    @Override
    public PrjCreate4LogDTO before(PrjCreateCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public PrjCreate4LogDTO after(PrjCreateCmd param, AuditLogBasicBean logBasic) {

        PrjCreate4LogDTO dt = new PrjCreate4LogDTO();
        dt.setPrjName(param.getProjectName());
        PrjPO newPrjPO = new PrjPO();
        newPrjPO.setStartTime(param.getStartTime());
        newPrjPO.setEndTime(param.getEndTime());

        dt.setStartTime(getFormatTime(newPrjPO));

        CategoryPO category =
                categoryMapper.selectByOrgIdAndId(logBasic.getOrgId(), param.getProjectCategoryId());

        if (category != null) {
            dt.setProjectCategory(category.getCategoryName());
        }

        dt.setRemark(param.getRemark());
        return dt;
    }

    private String getFormatTime(PrjPO prj) {
        String formatTime = "";
        if (prj == null) {
            return formatTime;
        }

        if (prj.getStartTime() != null) {
            formatTime =
                    FastDateFormat.getInstance("yyyy-MM-dd HH:mm").format(prj.getStartTime()) +
                    " ～ ";
        }

        if (prj.getEndTime() != null) {
            formatTime += FastDateFormat.getInstance("yyyy-MM-dd HH:mm").format(prj.getEndTime());
        }
        return formatTime;
    }

    @Override
    public Pair<String, String> entityInfo(
            PrjCreateCmd param, PrjCreate4LogDTO beforeObj, PrjCreate4LogDTO afterObj,
            AuditLogBasicBean logBasic) {
            String name = String.format("盘点-%s", param.getProjectName());

            return Pair.of("", name);

    }
}
