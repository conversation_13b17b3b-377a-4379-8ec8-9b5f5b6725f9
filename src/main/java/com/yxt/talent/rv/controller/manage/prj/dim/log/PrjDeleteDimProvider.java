package com.yxt.talent.rv.controller.manage.prj.dim.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/2 15:56
 **/
@Slf4j
@AllArgsConstructor
@Component
public class PrjDeleteDimProvider implements AuditLogDataProvider<String, String> {

    private final PrjDimMapper prjDimMapper;

    private List<PrjDimPO> list = new ArrayList<>();

    @Override
    public String before(String param, AuditLogBasicBean logBasic) {
        PrjDimPO oldPrjDim = prjDimMapper.selectByOrgIdAndId(logBasic.getOrgId(), param);
        if (oldPrjDim != null) {
            list.add(oldPrjDim);
        }
        return null;
    }

    @Override
    public String after(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public Pair<String, String> entityInfo(
            String param, String beforeObj, String afterObj, AuditLogBasicBean logBasic) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        PrjDimPO prjDimPO = list.get(0);
        String name = AuditLogHelper.Module.SETTING.getName() + "-维度名称-" +
        prjDimPO.getDimensionName();
        return Pair.of(prjDimPO.getId(), name);
    }
}
