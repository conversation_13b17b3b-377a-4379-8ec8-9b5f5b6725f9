package com.yxt.talent.rv.controller.manage.calimeet.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "人才盘点结果九宫格校准更新")
public class CaliMeetPrjUserResultAddCmd {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "x维度id")
    private String dimensionIdx;

    @Schema(description = "x校准等级")
    private Integer levelx;

    @Schema(description = "y维度id")
    private String dimensionIdy;

    @Schema(description = "y校准等级")
    private Integer levely;
}
