package com.yxt.talent.rv.controller.manage.xpd.log;

import com.yxt.aom.base.entity.common.Activity;
import com.yxt.o2ofacade.bean.response.talent.SpProjectInfoResp;
import com.yxt.o2ofacade.service.ProjectFacade;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdPlanDelLogDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdActionPlanMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdActionPlanPO;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/16
 */
@Slf4j
@AllArgsConstructor
@Component
public class XpdPrjDelTrainingLogProvider implements AuditLogDataProvider<String, XpdPlanDelLogDTO> {
    private final XpdActionPlanMapper xpdActionPlanMapper;
    private final XpdService xpdService;
    private final ProjectFacade projectFacade;

    @Override
    public XpdPlanDelLogDTO before(String id, AuditLogBasicBean logBasic) {
        return getXpdPlanDelLogDTO(id, logBasic);
    }

    @Override
    public XpdPlanDelLogDTO after(String id, AuditLogBasicBean logBasic) {
        return getXpdPlanDelLogDTO(id, logBasic);
    }

    @Nullable
    private XpdPlanDelLogDTO getXpdPlanDelLogDTO(String id, AuditLogBasicBean logBasic) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        XpdActionPlanPO entity = xpdActionPlanMapper.selectByPrimaryKey(id);
        if (Objects.isNull(entity)) {
            return null;
        }
        XpdPlanDelLogDTO xpdCreateLogDTO = new XpdPlanDelLogDTO();
        String prjId = entity.getTargetId();
        if (StringUtils.isNotBlank(prjId)) {
            List<SpProjectInfoResp> spProjectInfoResps = projectFacade.listSpProjects(logBasic.getOrgId(),
                    Collections.singletonList(Long.valueOf(prjId)));
            if (CollectionUtils.isNotEmpty(spProjectInfoResps)) {
                xpdCreateLogDTO.setTrainingPrjName(spProjectInfoResps.get(0).getName());
            }
        }
        //        String xpdId = entity.getXpdId();
        //        Activity activity = xpdService.findAomPrjByAomId(entity.getOrgId(), xpdId);
        //        XpdPlanDelLogDTO xpdCreateLogDTO = new XpdPlanDelLogDTO();
        //        xpdCreateLogDTO.setTrainingPrjName(activity.getActvName());
        return xpdCreateLogDTO;
    }

    @Override
    public Pair<String, String> entityInfo(String id, XpdPlanDelLogDTO beforeObj, XpdPlanDelLogDTO afterObj,
            AuditLogBasicBean logBasic) {
        String name = StringPool.EMPTY;
        if (StringUtils.isNotBlank(id)) {
            XpdActionPlanPO entity = xpdActionPlanMapper.selectByPrimaryKey(id);
            String xpdId = entity.getXpdId();
            Activity activity = xpdService.findAomPrjByAomId(entity.getOrgId(), xpdId);
            name = activity.getActvName();
        }
        return Pair.of(id, "盘点-" + name + "-移除培训");
    }
}
