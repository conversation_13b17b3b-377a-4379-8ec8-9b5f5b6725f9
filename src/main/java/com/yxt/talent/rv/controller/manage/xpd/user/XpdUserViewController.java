package com.yxt.talent.rv.controller.manage.xpd.user;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.talent.rv.application.xpd.user.XpdUserViewComponent;
import com.yxt.talent.rv.application.xpd.common.dto.RvUsers4Get;
import com.yxt.talent.rv.controller.manage.xpd.user.command.ViewCell4UserCmd;
import com.yxt.talent.rv.controller.manage.xpd.user.viewobj.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequestMapping("/mgr/xpd/view/user")
@RequiredArgsConstructor
@Tag(name = "新盘点项目-管理端-盘点个人报告", description = "新盘点项目-管理端-盘点个人报告")
public class XpdUserViewController {
    private final AuthService authService;
    private final XpdUserViewComponent userViewComponet;

    @Operation(summary = "个人详情")
    @Parameter(name = "id", description = "人员id", in = ParameterIn.PATH)
    @GetMapping(value = "/{id}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public XpdUserInfoVO getUserDetail(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return userViewComponet.getUserDetail(userCache.getOrgId(), id);
    }

    @Operation(summary = "盘点分数，进度")
    @Parameters({
        @Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH),
        @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/{xpdId}/{userId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public XpdUserPrjResultVO getViewData(
        HttpServletRequest request, @PathVariable String xpdId,
        @PathVariable String userId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return userViewComponet.getViewData(xpdId, userId);
    }

    @Operation(summary = "宫格布局")
    @Parameters({
        @Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH),
        @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/cell/{xpdId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public XpdGridMsgVO getGridData(
        HttpServletRequest request, @PathVariable String xpdId) {
        return userViewComponet.getGridData(xpdId);
    }

    @Operation(summary = "人员宫格位置")
    @Parameters({@Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH)})
    @PostMapping(value = "/cell/index")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public XpdUserCellVO getUserGridCell(HttpServletRequest request, @RequestBody ViewCell4UserCmd bean) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return userViewComponet.getUserGridCell(userCache.getOrgId(), bean);
    }

    @Operation(summary = "维度对应层级")
    @Parameters({
        @Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH),
        @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/grid/level/{xpdId}/{userId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public List<XpdUserDimGridLevelVO> findUserGridLevel(
        HttpServletRequest request, @PathVariable String xpdId,
        @PathVariable String userId) {
        return userViewComponet.findUserGridLevel(xpdId, userId);
    }

    @Operation(summary = "维度盘点结果")
    @Parameters({
        @Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH),
        @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/dim/result/{xpdId}/{userId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public List<XpdDimResult4ViewVO> findDimResult(
        HttpServletRequest request, @PathVariable String xpdId,
        @PathVariable String userId) {
        return userViewComponet.findDimResult(xpdId, userId);
    }

    @Operation(summary = "维度结果明细")
    @Parameters({
        @Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH),
        @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/dim/indicator/{xpdId}/{userId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public List<XpdDimIndicator4ViewVO> findDimIndicator(
        HttpServletRequest request, @PathVariable String xpdId,
        @PathVariable String userId) {
        return userViewComponet.findDimIndicator(xpdId, userId);
    }

    @Operation(summary = "绩效柱状图")
    @Parameters({
        @Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH),
        @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/perf/{xpdId}/{userId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public XpdChart4UserAllVO findPerf(
        HttpServletRequest request, @PathVariable String xpdId, @PathVariable String userId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return userViewComponet.findPerf(userCache.getOrgId(), xpdId, userId);
    }

    @Parameter(name = "id", description = "盘点人员id", in = ParameterIn.PATH)
    @Operation(summary = "胶水层-盘点人员详情（过滤字段）")
    @PostMapping(value = "/app/{id}/detail")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public RvUsers4Get userInfoDetail(@PathVariable String id, @RequestBody SearchDTO bean) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        return userViewComponet.userInfoDetail(userCacheBasic.getOrgId(), id, bean);
    }

    @Operation(summary = "获取人员关联的盘点项目列表")
    @Parameters({
        @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @GetMapping(value = "/prj/{userId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public List<XpdUserPrjVO> findUserXpdPrj(
        HttpServletRequest request, @PathVariable String userId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return userViewComponet.findUserXpdPrj(userCache.getOrgId(), userId);
    }


}
