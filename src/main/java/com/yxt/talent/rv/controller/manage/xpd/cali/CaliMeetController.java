package com.yxt.talent.rv.controller.manage.xpd.cali;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultResp;
import com.yxt.talent.rv.application.xpd.common.dto.CaliResultSaveReq;
import com.yxt.talent.rv.application.xpd.common.dto.CaliUpdateUserResultReq;
import com.yxt.talent.rv.application.xpd.common.dto.CaliUserResultContainer;
import com.yxt.talent.rv.application.xpd.common.enums.CaliResultTypeEnum;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;

@Slf4j
@RestController
@RequestMapping("/mgr/xpd/calimeet")
@RequiredArgsConstructor
@Tag(name = "xpd-管理端-校准会相关", description = "校准会相关")
public class CaliMeetController extends BaseController {
    private final XpdResultCalcService xpdResultCalcService;

    @Operation(summary = "用户校准结果")
    @GetMapping(value = "/{caliMeetId}/userResult")
    @Auth(codes = {AUTH_CODE_ALL})
    public CaliDimResultResp caliUserResult(@PathVariable String caliMeetId, @RequestParam String userId) {
        UserCacheBasic userBasic = getUserCacheBasic();
        return xpdResultCalcService.queryCaliResult(userBasic.getOrgId(), caliMeetId, userId);
    }

    @Operation(summary = "预览用户校准结果")
    @PostMapping(value = "/{caliMeetId}/calcCaliResult")
    @Auth(codes = {AUTH_CODE_ALL})
    public CaliDimResultResp calcCaliResult(@PathVariable String caliMeetId, @RequestParam String userId,
        @RequestParam int resultType,
        @RequestBody List<CaliUpdateUserResultReq> resultList) {
        UserCacheBasic userBasic = getUserCacheBasic();
        xpdResultCalcService.preHandleCaliResult(resultType, resultList);
        return xpdResultCalcService.viewCalcCaliResult(userBasic.getOrgId(), caliMeetId, userId, resultList);
    }

    @Operation(summary = "用户校准保存")
    @PostMapping(value = "/{caliMeetId}/saveCaliResult")
    @Auth(codes = {AUTH_CODE_ALL})
    public void saveCaliResult(@PathVariable String caliMeetId, @RequestParam String userId,
        @RequestParam int resultType,
        @RequestBody CaliResultSaveReq saveReq) {
        UserCacheBasic userBasic = getUserCacheBasic();
        xpdResultCalcService.preHandleCaliResult(resultType, saveReq.getResultList());
        CaliUserResultContainer container = new CaliUserResultContainer();
        container.setUserId(userId);
        container.setSuggestion(saveReq.getSuggestion());
        container.setReason(saveReq.getReason());
        container.setResultList(saveReq.getResultList());
        xpdResultCalcService.batchCalcCaliResult(userBasic.getOrgId(), userBasic.getUserId(), caliMeetId, Lists.newArrayList(container));
    }

    /*private void preHandleCaliResult(int resultType, List<CaliUpdateUserResultReq> resultList) {
        IArrayUtils.remove(resultList, item -> StringUtils.isEmpty(item.getCaliVal()));
        resultList.forEach(item -> {
            if (resultType == CaliResultTypeEnum.TYPE_DIM_LEVEL.getType()) {
                item.setGridLevelId(item.getCaliVal());
            } else if (resultType == CaliResultTypeEnum.TYPE_DIM_SCORE.getType()
                       || resultType == CaliResultTypeEnum.TYPE_INDICATOR_SCORE.getType()) {
                item.setScoreValue(new BigDecimal(item.getCaliVal()));
            } else if (resultType == CaliResultTypeEnum.TYPE_QUALIFIED_PTG.getType()) {
                item.setQualifiedPtg(new BigDecimal(item.getCaliVal()));
            } else if (resultType == CaliResultTypeEnum.TYPE_QUALIFIED.getType()) {
                item.setQualified(Integer.valueOf(item.getCaliVal()));
            }
        });
    }*/
}
