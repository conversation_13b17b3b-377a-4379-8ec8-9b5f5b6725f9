package com.yxt.talent.rv.controller.client.bizmgr.dept.command;

import com.yxt.criteria.Command;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "学员端-我的部门-部门项目-添加/取消我的关注")
public class DeptUserFocusClientCmd implements Command {

    @NotBlank(message = ExceptionKeys.SCOPE_DEPT_PRJ_TARGET_ID_NOT_BLANK)
    @Schema(description = "盘点项目id或人岗匹配项目id")
    private String targetId;

    @Range(min = 0, max = 1, message = ExceptionKeys.SCOPE_DEPT_PRJ_TARGET_TYPE_RANGE)
    @Schema(description = "项目类型（0-盘点项目 1-人岗匹配项目）")
    private Integer targetType;

    @Range(min = 0, max = 1, message = ExceptionKeys.SCOPE_DEPT_PRJ_ACTION_RANGE)
    @Schema(description = "操作项（0-取消 1-添加）")
    private Integer action = -1;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
