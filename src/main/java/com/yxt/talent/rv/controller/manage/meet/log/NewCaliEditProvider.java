package com.yxt.talent.rv.controller.manage.meet.log;

import com.yxt.aom.base.entity.common.Activity;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.meet.NewCaliLogCommonService;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.meet.viewobj.NewMeetAddLogDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Component
@RequiredArgsConstructor
public class NewCaliEditProvider implements AuditLogDataProvider<String, NewMeetAddLogDTO> {

    private final NewCaliLogCommonService newCaliLogCommonService;
    private final XpdService xpdService;

    @Override
    public NewMeetAddLogDTO before(String param, AuditLogBasicBean logBasic) {
        return newCaliLogCommonService.getDetail(param, logBasic.getOrgId());
    }

    @Override
    public NewMeetAddLogDTO after(String param, AuditLogBasicBean logBasic) {
        return newCaliLogCommonService.getDetail(param, logBasic.getOrgId());
    }


    @Override
    public Pair<String, String> entityInfo(String param, NewMeetAddLogDTO beforeObj, NewMeetAddLogDTO afterObj,
            AuditLogBasicBean logBasic) {
        String xpdName = "";
        if (Objects.nonNull(afterObj)) {
            String xpdId = afterObj.getXpdId();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(xpdId)) {
                Activity activity = xpdService.findAomPrjByAomId(logBasic.getOrgId(), xpdId);
                xpdName = activity.getActvName();
            }
        }
        return Pair.of(param, String.format("盘点-%s-编辑校准会", xpdName));
    }
}
