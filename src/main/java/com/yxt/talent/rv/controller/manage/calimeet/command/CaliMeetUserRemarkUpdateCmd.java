package com.yxt.talent.rv.controller.manage.calimeet.command;

import com.yxt.criteria.Command;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会：保存被盘点人员的备注与发展建议")
public class CaliMeetUserRemarkUpdateCmd implements Command {

    @NotBlank(message = ExceptionKeys.USER_ID_BLANK)
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "发展建议")
    private String suggestion;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
