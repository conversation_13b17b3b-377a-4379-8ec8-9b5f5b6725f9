package com.yxt.talent.rv.controller.manage.perf.viewobj;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/3 14:59
 **/
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PerfPeriodAddLogDTO {

    @AuditLogField(name = "绩效周期", orderIndex = 1)
    private String periodName;

}
