package com.yxt.talent.rv.controller.manage.dmp;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.annotation.Auth;
import com.yxt.common.annotation.NonDuplicateRequest;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.dmp.msg.legacy.DmpMsgConfAppService;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpMsgConfCreateCmd;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpMsgConfigVO;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.DMP_SETTING;
import static org.springframework.http.HttpStatus.OK;

/**
 * @deprecated since 5.8 老的人岗匹配项目已废弃，现在叫xpd项目
 */
@Slf4j
@RestController
@Deprecated(since = "5.8")
@Tag(name = "项目设置", description = "项目设置")
@RequiredArgsConstructor
@RequestMapping("/mgr/dmp/setting")
public class DmpMsgConfManageController {
    private final AuthService authService;
    private final DmpMsgConfAppService dmpMsgConfAppService;

//    @Auditing
//    @EasyAuditLog(value = RvAuditLogConstants.DMP_UPDATE_MSG_SEND, paramExp = "#param.buildForAudit(#dmpId)")
//    @Operation(summary = "保存消息通知配置")
//    @Parameter(name = "dmpId", description = "人岗动态匹配项目id", in = ParameterIn.PATH)
//    @PostMapping(value = "/{dmpId}", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = {TOKEN}, codes = DMP_SETTING)
//    @NonDuplicateRequest(value = "'saveMsgConfig:'+#dmpId", leaseTime = 2000)
//    public void saveMsgConfig(
//            HttpServletRequest request, @PathVariable("dmpId") String dmpId,
//            @Valid @RequestBody DmpMsgConfCreateCmd param) {
//        UserCacheBasic operator = authService.getUserCacheBasic(request);
//        /*param.setDmpId(dmpId);
//        AuditLogHooker.setLogParam(param);*/
//        dmpMsgConfAppService.saveOrUpdateMsgConfig(
//                dmpId, operator.getOrgId(), operator.getUserId(), param);
//    }
//
//    @Operation(summary = "查询消息通知配置")
//    @Parameter(name = "dmpId", description = "人岗动态匹配项目id", in = ParameterIn.PATH)
//    @GetMapping(value = "/{dmpId}", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = {TOKEN})
//    public DmpMsgConfigVO getMsgConfig(
//            HttpServletRequest request,
//            @PathVariable("dmpId") String dmpId) {
//        UserCacheBasic operator = authService.getUserCacheBasic(request);
//        return dmpMsgConfAppService.getMsgConfig(dmpId, operator.getOrgId());
//    }
}
