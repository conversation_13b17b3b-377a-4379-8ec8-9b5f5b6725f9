package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NKeyPath;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.annotation.L10NValuePath;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会盘点人员列表")
public class CaliMeetUserVO implements L10NContent {
    @Schema(description = "主键")
    private String id;

    @Schema(description = "用户Id")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "用户名称")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    @Schema(description = "用户头像")
    private String imgUrl;

    @Schema(description = "用户部门id")
    private String deptId;

    @L10NKeyPath(ignoreFirstKey = true)
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptIdFullPath;

    @Schema(description = "用户部门")
    @L10NValuePath
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @Schema(description = "岗位id")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;

    @Schema(description = "岗位名称")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionName;

    @Schema(description = "校准前维度等级Map<key,description> key为维度Id，description为维度等级")
    private Map<String, String> originalLevelMap = new HashMap<>(8);

    @Schema(description = "校准后维度等级Map<key,description> key为维度Id，description为维度等级")
    private Map<String, String> calibrationLevelMap = new HashMap<>(8);

    @Schema(description = "校准前维度分值Map<key,description> key为维度Id，description为维度分值")
    private Map<String, BigDecimal> originalScoreMap = new HashMap<>(8);

    @Schema(description = "校准后维度分值Map<key,description> key为维度Id，description为维度分值")
    private Map<String, BigDecimal> calibrationScoreMap = new HashMap<>(8);

    @Schema(description = "发展建议")
    private String suggestion;

    @Schema(description = " 数据来源（0-初始化 1-校准）")
    private Integer initType;

    @Schema(description = "用户账号")
    private String userName;

    @Schema(description = "账号状态(0-启用，1-禁用，2-删除)")
    private int status;
}
