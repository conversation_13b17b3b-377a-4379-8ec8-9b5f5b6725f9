package com.yxt.talent.rv.controller.client.bizmgr.team.query;

import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(name = "学员端-我的团队-人才盘点-项目维度-盘点结果列表")
public class TeamPrjResultScopeAuthClientQuery extends TeamScopeAuthClientQuery {

    @Schema(description = "项目状态(-1 -全部，2-进行中，3-已结束)")
    private Integer prjStatus;

    @Schema(description = "项目名称")
    private String projectName;

    public String getEscapedProjectName() {
        return SqlUtil.escapeSql(projectName);
    }

}
