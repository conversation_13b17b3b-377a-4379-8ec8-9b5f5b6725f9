package com.yxt.talent.rv.controller.manage.dmp;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.dmp.task.legacy.DmpTaskAppService;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpTaskBaseEditCmd;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpTaskCreateUpdateCmd;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpDimJqVO;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpTaskDTO;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpTaskSimpleVO;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditingPlus;
import com.yxt.talent.rv.infrastructure.service.audit.dmp.task.DmpTaskCreateAuditStrategy;
import com.yxt.talent.rv.infrastructure.service.audit.dmp.task.DmpTaskUpdateAuditStrategy;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.DMP_DESIGN;
import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

/**
 * @deprecated since 5.8 老的人岗匹配项目已废弃，现在叫xpd项目
 */
@Slf4j
@RestController
@Deprecated(since = "5.8")
@RequiredArgsConstructor
@Tag(name = "人岗动态匹配项目-方案任务", description = "人岗动态匹配项目-方案任务")
@RequestMapping(value = "/mgr/dmp")
public class DmpTaskManageController {
    private final AuthService authService;
    private final DmpTaskAppService dmpTaskAppService;

//    @Auditing
//    @AuditingPlus(strategyClass = DmpTaskCreateAuditStrategy.class, entityId = "#{result.id}", before = true)
//    @Operation(summary = "任务方案-保存基本信息", description = "动态匹配和表单评价共用这个接口，表单评价必传评估关系和评估权重")
//    @ResponseStatus(CREATED)
//    @PostMapping(value = "/{dmpId}/task/basic", consumes = MEDIATYPE)
//    @Auth(codes = DMP_DESIGN)
//    public DmpTaskSimpleVO createTaskBasic(
//        @PathVariable String dmpId, @Validated @RequestBody DmpTaskCreateUpdateCmd cmd) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        DmpTaskPO dmpTask = dmpTaskAppService.createTaskBasic(orgId, dmpId, cmd, operator);
//        return new DmpTaskSimpleVO(dmpTask.getId(), dmpTask.getEvalFormId(), dmpTask.getTaskName());
//    }
//
//    @Auditing
//    @ResponseStatus(OK)
//    @Auth(codes = DMP_DESIGN)
//    @AuditingPlus(strategyClass = DmpTaskUpdateAuditStrategy.class, entityId = "#taskId")
//    @PutMapping(value = "/{dmpId}/task/{taskId}/basic", consumes = MEDIATYPE, produces = MEDIATYPE)
//    @Operation(summary = "任务方案-更新基本信息", description = "动态匹配和表单评价共用这个接口，表单评价必传评估关系和评估权重")
//    public DmpTaskSimpleVO modifyTaskBasic(
//        @PathVariable String dmpId, @PathVariable String taskId,
//        @Validated @RequestBody DmpTaskCreateUpdateCmd cmd) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        DmpTaskPO dmpTask = dmpTaskAppService.modifyTaskBasic(orgId, dmpId, taskId, cmd, operator);
//        return new DmpTaskSimpleVO(dmpTask.getId(), dmpTask.getEvalFormId(), dmpTask.getTaskName());
//    }
//
//    @Operation(summary = "任务方案-查询详情", description = "包括维度、表单配置、条件组、条件")
//    @ResponseStatus(OK)
//    @GetMapping(value = "/{dmpId}/task/{taskId}", produces = MEDIATYPE)
//    @Auth(codes = AUTH_CODE_ALL)
//    public DmpTaskDTO findTaskInfo(@PathVariable String dmpId, @PathVariable String taskId) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        return dmpTaskAppService.findTaskInfo(orgId, taskId, operator.getUserId());
//    }
//
//    @Operation(summary = "任务方案-动态匹配任务-保存维度设计", description = "接口用于动态匹配任务第二步--维度设计，支持新增和更新")
//    @ResponseStatus(CREATED)
//    @PostMapping(value = "/{dmpId}/task/{taskId}/dim", consumes = MEDIATYPE)
//    @Auth(codes = DMP_DESIGN)
//    public void saveTaskDim(
//        @PathVariable String dmpId, @PathVariable String taskId,
//        @Validated @RequestBody DmpTaskCreateUpdateCmd dmpTaskCreateUpdateCmd) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        dmpTaskAppService.modifyTaskDims(orgId, taskId, dmpId, dmpTaskCreateUpdateCmd, operator);
//    }
//
//    @Operation(summary = "任务方案-维度选择弹窗")
//    @ResponseStatus(OK)
//    @GetMapping(value = "/{dmpId}/jq/dim", produces = MEDIATYPE)
//    @Auth(codes = AUTH_CODE_ALL)
//    public DmpDimJqVO listJqDims(
//        @PathVariable String dmpId, @RequestParam(required = false) String taskId) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        String userId = operator.getUserId();
//        return dmpTaskAppService.listJqDims(orgId, dmpId, taskId, userId);
//    }
//
//    @SwaggerPageQuery
//    @Operation(summary = "任务方案-列表")
//    @ResponseStatus(OK)
//    @GetMapping(value = "/{dmpId}/task/list", produces = MEDIATYPE)
//    @Auth(codes = AUTH_CODE_ALL)
//    public PagingList<DmpTaskDTO> listTask(
//        @PathVariable String dmpId, @RequestParam(required = false) String taskName) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
//        return dmpTaskAppService.getDmpTaskPage(orgId, dmpId, taskName, pageRequest);
//    }
//
//    @Auditing
//    //@AuditingPlus(strategyClass = DmpTaskDeleteAuditStrategy.class)
//    @EasyAuditLog(value = RvAuditLogConstants.DMP_TASK_DELETE, paramExp = "#taskId")
//    @Operation(summary = "任务方案-删除")
//    @ResponseStatus(OK)
//    @DeleteMapping(value = "/{dmpId}/task/{taskId}")
//    @Auth(codes = DMP_DESIGN)
//    public void deleteTask(@PathVariable String dmpId, @PathVariable String taskId) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        dmpTaskAppService.deleteTask(orgId, dmpId, taskId, operator);
//    }
//
//    @Operation(summary = "任务方案-撤回")
//    @Auditing
//    @EasyAuditLog(value = RvAuditLogConstants.DMP_TASK_WITHDRAW, paramExp = "#taskId")
//    @ResponseStatus(OK)
//    @PutMapping(value = "/{dmpId}/task/{taskId}/revoke")
//    @Auth(codes = DMP_DESIGN)
//    public void revokeTask(@PathVariable String dmpId, @PathVariable String taskId) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        dmpTaskAppService.revokeTask(orgId, dmpId, taskId, operator);
//    }
//
//    @Auditing
//    //@AuditingPlus(strategyClass = DmpTaskEnableAuditStrategy.class)
//    @EasyAuditLog(value = RvAuditLogConstants.DMP_TASK_ENABLE, paramExp = "#taskId")
//    @Operation(summary = "任务方案-启用")
//    @ResponseStatus(OK)
//    @PutMapping(value = "/{dmpId}/task/{taskId}/enable")
//    @Auth(codes = DMP_DESIGN)
//    public void enableTask(@PathVariable String dmpId, @PathVariable String taskId) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        String orgId = operator.getOrgId();
//        dmpTaskAppService.enableTask(orgId, dmpId, taskId, operator);
//    }
//
//
//    @Operation(summary = "编辑任务名称描述")
//    @ResponseStatus(OK)
//    @Parameter(name = "dmpId", description = "人岗动态匹配项目id", in = ParameterIn.PATH)
//    @Parameter(name = "taskId", description = "任务id", in = ParameterIn.PATH)
//    @PutMapping(value = "{dmpId}/task/{taskId}/base/info")
//    @Auth(codes = DMP_DESIGN)
//    public void editTaskBaseInfo(@PathVariable String dmpId, @PathVariable String taskId,
//            @Valid @RequestBody DmpTaskBaseEditCmd cmd) {
//        UserCacheDetail operator = authService.getUserCacheDetail();
//        dmpTaskAppService.editTaskName(operator.getOrgId(), dmpId, taskId, operator.getUserId(), cmd);
//    }

}
