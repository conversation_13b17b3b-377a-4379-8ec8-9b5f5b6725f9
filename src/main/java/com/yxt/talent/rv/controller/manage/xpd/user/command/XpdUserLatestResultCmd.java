package com.yxt.talent.rv.controller.manage.xpd.user.command;

import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "我的团队-人才盘点-个人维度-最新盘点结果")
public class XpdUserLatestResultCmd {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "姓名")
    private String fullname;

    @Schema(description = "账号")
    private String username;

    @Schema(description = "部门")
    private String deptName;

    @Schema(description = "岗位")
    private String positionName;

    @Schema(description = "头像")
    private String imgUrl;

    @Schema(description = "人才定义名称")
    private String levelName;

    @Schema(description = "最新盘点结果 5.8")
    private XpdResultUserDimDetailVO userDimDetail;
}
