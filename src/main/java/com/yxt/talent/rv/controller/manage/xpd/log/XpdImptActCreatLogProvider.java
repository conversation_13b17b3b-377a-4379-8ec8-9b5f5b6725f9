package com.yxt.talent.rv.controller.manage.xpd.log;

import com.yxt.aom.base.entity.common.Activity;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.xpd.aom.XpdAomService;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.cmd.XpdImportCreateCmd;
import com.yxt.talent.rv.controller.manage.xpd.grid.command.RvGrid4Create;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdGridCreateLogDTO;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdImptActCreateDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 *
 * @description 创建导入活动
 * @date 2025/4/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XpdImptActCreatLogProvider implements AuditLogDataProvider<XpdImportCreateCmd, XpdImptActCreateDto> {
    private final XpdMapper xpdMapper;
    private final SptalentsdFacade sptalentsdFacade;
    private final XpdService xpdService;

    @Override
    public XpdImptActCreateDto before(XpdImportCreateCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public XpdImptActCreateDto after(XpdImportCreateCmd param, AuditLogBasicBean logBasic) {
        XpdImptActCreateDto res = new XpdImptActCreateDto();
        // 项目名称
        XpdPO xpd = xpdMapper.selectById(param.getXpdId());
        Activity activity = xpdService.findAomPrjByAomId(logBasic.getOrgId(), param.getXpdId());
        res.setName(activity.getActvName());
        List<DimensionList4Get> baseDimDetails =
            sptalentsdFacade.getBaseDimDetail(logBasic.getOrgId(), Lists.newArrayList(param.getDimId()));
        if (CollectionUtils.isNotEmpty(baseDimDetails)) {
            DimensionList4Get dimensionList4Get = baseDimDetails.get(0);
            res.setDimName(dimensionList4Get.getDmName());
        } else {
            return res;
        }
        if (param.getImportType() == 0) {
            res.setImptActDesc("盘点结果-导入数据-指标明细");
        } else {
            res.setImptActDesc("盘点结果-导入数据-维度结果");
        }
        return res;
    }

    @Override
    public Pair<String, String> entityInfo(
        XpdImportCreateCmd param, XpdImptActCreateDto beforeObj, XpdImptActCreateDto afterObj,
        AuditLogBasicBean logBasic) {
        XpdImptActCreateDto res = new XpdImptActCreateDto();
        Activity activity = xpdService.findAomPrjByAomId(logBasic.getOrgId(), param.getXpdId());
        res.setName(activity.getActvName());
        List<DimensionList4Get> baseDimDetails =
            sptalentsdFacade.getBaseDimDetail(logBasic.getOrgId(), Lists.newArrayList(param.getDimId()));
        if (CollectionUtils.isNotEmpty(baseDimDetails)) {
            DimensionList4Get dimensionList4Get = baseDimDetails.get(0);
            res.setDimName(dimensionList4Get.getDmName());
        }
        if (param.getImportType() == 0) {
            return Pair.of("", "盘点-" + res.getName() + "-导入指标明细-" + res.getDimName());
        } else {
            return Pair.of("", "盘点-" + res.getName() + "-导入维度结果-" + res.getDimName());
        }

    }
}
