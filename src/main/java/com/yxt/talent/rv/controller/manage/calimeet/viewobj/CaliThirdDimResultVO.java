package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 校准会第三维度结果VO
 * 
 * <AUTHOR>
 * @date 2025/5/21
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Schema(description = "校准会第三维度结果")
public class CaliThirdDimResultVO {

    @Schema(description = "维度ID")
    private String dimId;

    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "分层ID")
    private String gridLevelId;

    @Schema(description = "分层名称")
    private String gridLevelName;

    @Schema(description = "分层序号")
    private Integer orderIndex;

    @Schema(description = "第三维度边框颜色")
    private String thirdDimColor;
}
