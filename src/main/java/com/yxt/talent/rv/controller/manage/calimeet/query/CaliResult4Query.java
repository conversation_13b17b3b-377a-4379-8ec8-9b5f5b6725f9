package com.yxt.talent.rv.controller.manage.calimeet.query;

import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2025/5/21
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliResult4Query extends SearchUdpScopeAuthQuery {

    @Schema(description = "盘点项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String projectId;

    @Schema(description = "校准会id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String meetingId;

    @Schema(description = "维度组合id")
    private String dimCombId;

    @Schema(description = "x轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisX;

    @Schema(description = "y轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisY;

    @Schema(description = "格子编号")
    private Integer cellIndex;

    @Schema(description = "排序维度id")
    private String sortDimId;

    /* 为了前端切换之后性能考虑，接口采取把用户维度全部返回的方式，前端自行处理，所以这里不需要该字段 */
//    @Schema(description = "第三维度id")
//    private String thirdDimId;

    /*@Schema(description = "x轴维度值（1-3对应低至高）")
    private Integer valueX;

    @Schema(description = "y轴维度值（1-3对应低至高）")
    private Integer valueY;

    private List<String> userIds;

    private List<String> authUserIds;*/

}
