package com.yxt.talent.rv.controller.common;

import com.yxt.common.annotation.Auth;
import com.yxt.talent.rv.application.common.DictQryAppService;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.Constants.SEPARATOR_CHAR_ROLE_ALL;
import static com.yxt.common.enums.AuthType.TOKEN;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "通用-字典表", description = "通用-字典表")
@RequestMapping(value = "")
public class DictCommonController {
    private final DictQryAppService dictQryAppService;

//    @Operation(summary = "(废弃)字典列表")
//    @ResponseStatus(OK)
//    @GetMapping(value = "/client/dict/list", produces = MEDIATYPE)
//    @Auth(type = {TOKEN})
//    public List<DictPO> getDict(@RequestParam("dictType") int dictType) {
//        return dictQryAppService.list(dictType);
//    }
//
//    @Operation(summary = "(废弃)字典列表")
//    @ResponseStatus(OK)
//    @GetMapping(value = "/mgr/dict/list", produces = MEDIATYPE)
//    @Auth(type = {TOKEN}, codes = {SEPARATOR_CHAR_ROLE_ALL})
//    public List<DictPO> getMgrDict(@RequestParam("dictType") int dictType) {
//        return dictQryAppService.list(dictType);
//    }

    @Operation(summary = "查询字典列表")
    @ResponseStatus(OK)
    @GetMapping(value = {"/common/dict/list", "/mgr/dict/list", "/client/dict/list"}, produces = MEDIATYPE)
    public List<DictPO> listDict(@RequestParam("dictType") int dictType) {
        return dictQryAppService.list(dictType);
    }
}
