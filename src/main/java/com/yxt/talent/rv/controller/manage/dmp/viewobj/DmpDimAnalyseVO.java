package com.yxt.talent.rv.controller.manage.dmp.viewobj;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.talent.rv.application.dmp.dto.DimCatDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 维度分析
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpDimAnalyseVO {

    @Schema(description = "根维度分类id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rootCategoryId;

    @Schema(description = "根维度分类名称")
    private String rootCategoryName;

    private List<DimCatDTO> dimCategorys;
}
