package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会创建结果")
public class CaliMeetCreateResultVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "所属盘点项目id")
    private String projectId;
}
