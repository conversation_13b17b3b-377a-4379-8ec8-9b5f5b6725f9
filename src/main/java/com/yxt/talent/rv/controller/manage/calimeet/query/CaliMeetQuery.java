package com.yxt.talent.rv.controller.manage.calimeet.query;

import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会列表搜索")
public class CaliMeetQuery {

    @Schema(description = "关键字查询（目前是会议名称）")
    private String keyword;

    @Schema(description = "校准会状态（0-未开始，1-进行中，2-已结束，-1-全部）")
    private Integer meetStatus;

    @Schema(description = "权限范围（1-我创建的，2-我管辖的）")
    private Integer dataRange = 1;

    private List<String> userIds;

    @Schema(description = "盘点项目id")
    private String projectId;

    private List<String> meetingIds;

    public String getEscapedKeyword() {
        return SqlUtil.escapeSql(keyword);
    }
}
