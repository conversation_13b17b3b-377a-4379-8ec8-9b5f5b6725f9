package com.yxt.talent.rv.controller.manage.prj.user.viewobj;

import com.yxt.talent.rv.application.prj.dim.dto.PrjDimResultDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "盘点个人报告维度数据信息")
public class PrjUserPalacesVO {

    @Schema(description = "盘点结果：取自机构自定义人才标签（默认：0-未知，2-中坚力量, 3-待提升人员, 1-优秀人才）")
    private int result;

    @Schema(description = "人才标签名称")
    private String labelName;

    @Schema(description = "规则内容")
    private String ruleContent;

    @Schema(description = "人才标签url")
    private String logoUrl;

    @Schema(description = "高等级维度个数")
    private int highDimension = 0;

    @Schema(description = "中等级维度个数")
    private int middleDimension = 0;

    @Schema(description = "低等级维度个数")
    private int lowDimension = 0;

    @Schema(description = "占比排名，例如：超过99%的同项目盘点人员", example = "99")
    private BigDecimal percent;

    @Schema(description = "发展建议")
    private String suggestion;

    @Schema(description = "维度结果信息")
    private List<PrjDimResultDTO> dimensionResults = new ArrayList<>();
}
