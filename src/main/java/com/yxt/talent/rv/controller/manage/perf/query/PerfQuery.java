package com.yxt.talent.rv.controller.manage.perf.query;


import com.yxt.criteria.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "绩效查询对象")
public class PerfQuery implements Query {
    @Schema(description = "部门id列表")
    private List<String> deptIds;

    @Schema(description = "绩效周期id列表")
    private List<String> periods;

    @Schema(description = "搜索关键字")
    private String searchKey;

    @Schema(description = "关键字类型 0-账号，1-姓名，-1-账号或姓名")
    private Integer kwType;
}
