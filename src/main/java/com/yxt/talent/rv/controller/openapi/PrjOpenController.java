package com.yxt.talent.rv.controller.openapi;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.controller.openapi.viewobj.PrjUserResultOpenVO;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjAppService;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

import static com.yxt.common.enums.AuthType.CUSTOM;
import static com.yxt.common.enums.AuthType.OAUTH;
import static org.springframework.http.HttpStatus.OK;

/**
 * 开放平台-盘点项目管理
 * @deprecated since 5.8
 */
@Slf4j
@RestController
@Deprecated(since="5.8")
@RequiredArgsConstructor
@Tag(name = "开放平台-盘点项目管理", description = "开放平台-盘点项目管理")
@RequestMapping(value = "/open/project")
public class PrjOpenController {
    private final AuthService authService;
    private final PrjAppService prjAppService;

    @SwaggerPageQuery
    @Operation(summary = "项目盘点-员工盘点结果明细")
    @GetMapping(value = "/user/result/sync")
    @ResponseStatus(OK)
    @Auth(type = {OAUTH, CUSTOM})
    public PagingList<PrjUserResultOpenVO> getUserPrjResults(
            HttpServletRequest request,
            @RequestParam(required = false) String searchStartTime,
            @RequestParam(required = false) String searchEndTime) {
        String orgId = authService.getOrgIdByOauthToken(request);
        if (StringUtils.isBlank(orgId)) {
            orgId = request.getParameter("orgId");
        }
        Validate.isNotBlank(orgId, ExceptionKeys.ORG_ID_BLANK);
        log.info("LOG62060:项目盘点-员工盘点结果明细,orgId={} ", orgId);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        return BeanCopierUtil.toPagingList(
                prjAppService.getUserPrjResults(
                        orgId, searchStartTime, searchEndTime, pageRequest));
    }
}
