package com.yxt.talent.rv.controller.client.general.calimeet;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.controller.client.general.calimeet.query.CaliMeetClientQuery;
import com.yxt.talent.rv.controller.client.general.calimeet.viewobj.CaliMeetClientVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetVO;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static org.springframework.http.HttpStatus.OK;

/**
 * 校准会
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "客户端盘点校准会", description = "客户端盘点校准会")
@RequestMapping(value = "/client/calibration")
public class CaliMeetClientController {
    private final CaliMeetAppService caliMeetAppService;
    private final AuthService authService;

    @SwaggerPageQuery
    @Operation(summary = "校准会列表")
    @PostMapping(value = "/list", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public PagingList<CaliMeetClientVO> clientList(
            HttpServletRequest request,
            @RequestBody CaliMeetClientQuery search) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return caliMeetAppService.findClientPage(ApiUtil.getPageRequest(request), search,
                userCache.getOrgId(), userCache.getUserId());
    }

    @Operation(summary = "校准会议基础信息")
    @GetMapping(value = "/basic/{id}")
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public CaliMeetVO getMeetingDetail(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppService.getMeeting4GetById(
                id, currentUser.getOrgId(), currentUser.getLocale());
    }
}
