package com.yxt.talent.rv.controller.client.general.meet.viewobj;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会列表元素")
public class MeetClientVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "会议名称")
    private String meetName;

    @Schema(description = "所属盘点项目id")
    private String projectId;

    @Schema(description = "所属盘点项目")
    private String projectName;

    @Schema(description = "校准会状态（0-未开始，1-进行中，2-已结束）")
    private Integer meetStatus;

    @Schema(description = "会议时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date meetTime;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date endTime;

    @Schema(description = "会议持续时间，单位：分钟，最短60，最长1440分钟（即1天）")
    private Integer duration;

    @Schema(description = "角色类型（1-我组织的，2-我参与的）")
    private Integer userType;

    @Schema(description = "校准人是否完成校准任务(0-未完成，1-已完成)")
    private Integer caliStatus;
}
