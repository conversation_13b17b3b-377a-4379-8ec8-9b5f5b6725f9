package com.yxt.talent.rv.controller.manage.meet.log;

import com.alibaba.fastjson.JSON;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.meet.command.NewCaliMeetUserAddCmd;
import com.yxt.talent.rv.controller.manage.meet.command.NewCaliMeetUserDelCmd;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetUserAddDelLogVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Component
@RequiredArgsConstructor
public class CaliUserDelProvider implements AuditLogDataProvider<NewCaliMeetUserDelCmd, MeetUserAddDelLogVO> {
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final CalimeetMapper calimeetMapper;
    private final CalimeetUserMapper calimeetUserMapper;

    @Override
    public MeetUserAddDelLogVO before(NewCaliMeetUserDelCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public NewCaliMeetUserDelCmd convertParam(Object param, AuditLogBasicBean logBasic) {
        if (Objects.isNull(param)) {
            return null;
        }
        if (param instanceof NewCaliMeetUserDelCmd item) {
            return item;
        }
        return JSON.parseObject(JSON.toJSONString(param), NewCaliMeetUserDelCmd.class);
    }

    @Override
    public MeetUserAddDelLogVO after(NewCaliMeetUserDelCmd param, AuditLogBasicBean logBasic) {
        if (Objects.isNull(param)) {
            return null;
        }
        List<String> ids = CollectionUtils.isEmpty(param.getIds()) ? new ArrayList<>() : param.getIds();
        String id = param.getId();
        if (CollectionUtils.isEmpty(ids) && StringUtils.isBlank(id)) {
            return null;
        }
        if (StringUtils.isNotBlank(id)) {
            ids.add(id);
        }
        //        List<String> userIds = param.getUserIds();
        //        if (CollectionUtils.isEmpty(userIds)) {
        //            return null;
        //        }
        MeetUserAddDelLogVO res = new MeetUserAddDelLogVO();
        //        res.setUserListStr(
        //                udpLiteUserMapper.selectByUserIds(logBasic.getOrgId(), userIds).stream().map(UdpLiteUserPO::getFullname)
        //                        .collect(Collectors.joining(";")));
        List<CalimeetUserPO> calimeetUsers = calimeetUserMapper.selectByIds(logBasic.getOrgId(), ids);
        if (CollectionUtils.isNotEmpty(calimeetUsers)) {
            res.setUserListStr(udpLiteUserMapper.selectByUserIds(logBasic.getOrgId(),
                            calimeetUsers.stream().map(CalimeetUserPO::getUserId).distinct().collect(Collectors.toList()))
                    .stream().map(UdpLiteUserPO::getFullname).collect(Collectors.joining(";")));
        }
        return res;
    }

    @Override
    public Pair<String, String> entityInfo(NewCaliMeetUserDelCmd param, MeetUserAddDelLogVO beforeObj,
            MeetUserAddDelLogVO afterObj, AuditLogBasicBean logBasic) {
        String caliMeetName = "";
        String caliMeetId = "";
        if (CollectionUtils.isNotEmpty(param.getIds())) {
            CalimeetUserPO calimeetUser = calimeetUserMapper.selectByPrimaryKey(param.getIds().get(0));
            caliMeetId = Objects.nonNull(calimeetUser) ? calimeetUser.getCalimeetId() : "";
        }
        if (StringUtils.isNotBlank(caliMeetId)) {
            CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, logBasic.getOrgId());
            if (Objects.nonNull(calimeetPO)) {
                caliMeetName = calimeetPO.getCalimeetName();
            }
        }
        return Pair.of(caliMeetId, String.format("盘点-%s-添加校准人员", caliMeetName));
    }
}
