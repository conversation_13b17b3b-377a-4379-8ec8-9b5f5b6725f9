package com.yxt.talent.rv.controller.manage.perf.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "清空绩效数据", description = "清空绩效数据")
public class PerfClearCmd implements Command {

    @Schema(description = "是否强制清空", requiredMode = Schema.RequiredMode.REQUIRED)
    private boolean force;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tranId;

}
