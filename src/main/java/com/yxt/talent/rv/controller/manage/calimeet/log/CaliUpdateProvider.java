package com.yxt.talent.rv.controller.manage.calimeet.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetCreateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliAddLogDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/2 18:35
 **/
@Slf4j
@AllArgsConstructor
@Component
public class CaliUpdateProvider implements AuditLogDataProvider<CaliMeetCreateCmd, CaliAddLogDTO> {


    @Override
    public CaliAddLogDTO before(CaliMeetCreateCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public CaliAddLogDTO after(CaliMeetCreateCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public Pair<String, String> entityInfo(
            CaliMeetCreateCmd param, CaliAddLogDTO beforeObj, CaliAddLogDTO afterObj,
            AuditLogBasicBean logBasic) {


            return null;
    }
}
