package com.yxt.talent.rv.controller.manage.calimeet;

import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.calimeet.CaliMeetOverviewAppService;
import com.yxt.talent.rv.application.common.CommonAppService;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetTaskRecordUpdateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetLargeChangePersonnelQry;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGeneralOverviewVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGridItemVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetLargeChangePersonnelVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetTaskRecordVO;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.service.auth.AuthenticateService;
import com.yxt.talent.rv.infrastructure.service.auth.AuthorizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.talent.rv.application.xpd.result.XpdResultAppService.NAV_CODE_XPD;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT;
import static org.springframework.http.HttpStatus.NO_CONTENT;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/mgr/calimeet")
@Tag(name = "校准会概览管理", description = "管理端-校准会概览相关接口")
public class CaliMeetOverviewManageController {

    private final CaliMeetOverviewAppService caliMeetOverviewAppService;
    private final AuthService authService;
    private final AuthenticateService authenticateService;
    private final AuthorizationService authorizationService;
    private final CommonAppService commonAppService;

    @Auth(type = {AuthType.TOKEN})
    @Operation(summary = "单个校准会概览-校准概览统计")
    @GetMapping("/{caliMeetId}/overview/summary")
    public CaliMeetGeneralOverviewVO getCalibrationGeneralOverview(
        @Parameter(description = "校准会ID", required = true) @PathVariable @NotNull String caliMeetId) {
        String orgId = authService.getUserCacheDetail().getOrgId();
        return caliMeetOverviewAppService.getGeneralOverview(caliMeetId, orgId);
    }

    @Auth(type = {AuthType.TOKEN})
    @Operation(summary = "单个校准会概览-校准前后对比")
    @GetMapping("/{caliMeetId}/overview/comparison")
    public List<CaliMeetGridItemVO> getCalibrationComparison(
        @Parameter(description = "校准会ID", required = true) @PathVariable @NotNull String caliMeetId,
        @Parameter(description = "维度组合ID", required = true) @RequestParam @NotBlank String dimCombId) {
        String orgId = authService.getUserCacheDetail().getOrgId();
        return caliMeetOverviewAppService.getComparisonData(caliMeetId, dimCombId, orgId);
    }

    @Auth(type = {AuthType.TOKEN})
    @Operation(summary = "单个校准会概览-校准幅度较大人员列表")
    @GetMapping("/{caliMeetId}/overview/large-change-personnel")
    public PagingList<CaliMeetLargeChangePersonnelVO> listLargeChangePersonnel(
        @Parameter(description = "校准会ID", required = true) @PathVariable @NotNull String caliMeetId,
        @Validated @Valid CaliMeetLargeChangePersonnelQry query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        String orgId = userCacheDetail.getOrgId();
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        commonAppService.fillAuthInfo(userCacheDetail.getOrgId(), query, NAV_CODE_XPD, SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT);
        return caliMeetOverviewAppService.listLargeChangePersonnel(caliMeetId, orgId, query, pageRequest);
    }

    @Auth(type = {AuthType.TOKEN})
    @Operation(summary = "单个校准会概览-校准任务记录-展示")
    @GetMapping("/{caliMeetId}/overview/record")
    public CaliMeetTaskRecordVO getCaliMeetRecord(
        @Parameter(description = "校准会ID", required = true) @PathVariable @NotNull String caliMeetId) {
        String orgId = authService.getUserCacheDetail().getOrgId();
        return caliMeetOverviewAppService.getCaliMeetRecord(caliMeetId, orgId);
    }

    @Operation(summary = "单个校准会概览-校准任务记录-编辑")
    @PutMapping("/{caliMeetId}/overview/record")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = {AUTH_CODE_ALL})
    @EasyAuditLog(value = RvAuditLogConstants.CALI_TASK_RECORD_UPDATE, paramExp = "#caliMeetId")
    public void updateCaliMeetRecord(
        HttpServletRequest request,
        @Parameter(description = "校准会ID", required = true) @PathVariable @NotNull String caliMeetId,
        @RequestBody @Valid CaliMeetTaskRecordUpdateCmd cmd) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetOverviewAppService.updateCaliMeetRecord(caliMeetId, cmd, operator.getUserId(), operator.getOrgId());
    }

    @Auth(type = {AuthType.TOKEN})
    @Operation(summary = "盘点项目校准会概览-校准概览统计")
    @GetMapping("/xpd/{xpdId}/overview/summary")
    public CaliMeetGeneralOverviewVO getXpdCalibrationGeneralOverview(
        @Parameter(description = "盘点项目ID", required = true) @PathVariable @NotNull String xpdId) {
        String orgId = authService.getUserCacheDetail().getOrgId();
        return caliMeetOverviewAppService.getXpdGeneralOverview(xpdId, orgId);
    }

    @Auth(type = {AuthType.TOKEN})
    @Operation(summary = "盘点项目校准会概览-校准前后对比")
    @GetMapping("/xpd/{xpdId}/overview/comparison")
    public List<CaliMeetGridItemVO> getXpdCalibrationComparison(
        @Parameter(description = "盘点项目ID", required = true) @PathVariable @NotNull String xpdId,
        @Parameter(description = "维度组合ID", required = true) @RequestParam @NotBlank String dimCombId) {
        String orgId = authService.getUserCacheDetail().getOrgId();
        return caliMeetOverviewAppService.getXpdComparisonData(xpdId, dimCombId, orgId);
    }

    @Auth(type = {AuthType.TOKEN})
    @Operation(summary = "盘点项目校准会概览-校准幅度较大人员列表")
    @GetMapping("/xpd/{xpdId}/overview/large-change-personnel")
    public PagingList<CaliMeetLargeChangePersonnelVO> listXpdLargeChangePersonnel(
        @Parameter(description = "盘点项目ID", required = true) @PathVariable @NotNull String xpdId,
        @Validated @Valid CaliMeetLargeChangePersonnelQry query) {
        String orgId = authService.getUserCacheDetail().getOrgId();
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        return caliMeetOverviewAppService.listXpdLargeChangePersonnel(xpdId, orgId, query, pageRequest);
    }

} 