package com.yxt.talent.rv.controller.manage.org.viewobj;

import com.yxt.talent.rv.application.org.profile.dto.OrgProfileManageSeriesRvDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class OrgProfileManageSeriesRvVO {

    @Schema(description = "年度")
    private Integer yearly;

    @Schema(description = "干部胜任人数")
    private Integer manageCompCount;

    @Schema(description = "干部未胜任人数")
    private Integer manageUnCompCount;

    @Schema(description = "序列人才质量")
    private List<OrgProfileManageSeriesRvDTO> seriesrvs;
}
