package com.yxt.talent.rv.controller.manage.calimeet;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.talent.rv.application.calimeet.CaliGridService;
import com.yxt.talent.rv.application.common.CommonAppService;
import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultResp;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetMoveCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliGridCell4Query;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliResult4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliCellUserVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliGridViewDataVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliGridViewVO;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetResultDetailVO;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetUserDimResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.application.xpd.result.XpdResultAppService.NAV_CODE_XPD;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT;
import static org.springframework.http.HttpStatus.OK;

/**
 * <AUTHOR>
 * @description 宫格校准
 *
 * @date 2025/5/21
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "宫格校准", description = "宫格校准")
@RequestMapping(value = "/mgr/cali/grid")
public class CaliGridController {
    private final AuthService authService;
    private final CaliGridService caliGridService;
    private final CommonAppService commonAppService;

    @PostMapping("/view")
    @Operation(summary = "宫格校准概览")
    @Auth(codes = {AUTH_CODE_ALL})
    public CaliGridViewVO caliGridView(
        @RequestBody CaliResult4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        commonAppService.fillAuthInfo(userCacheDetail.getOrgId(), query, NAV_CODE_XPD, SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT);
        return caliGridService.caliGridView(userCacheDetail.getOrgId(), query);
    }

    @PostMapping("/palaces")
    @Operation(summary = "九宫格人员落位列表")
    @Auth(codes = {AUTH_CODE_ALL})
    public MeetResultDetailVO findGridUser(
        @RequestBody CaliResult4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        commonAppService.fillAuthInfo(userCacheDetail.getOrgId(), query, NAV_CODE_XPD, SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT);
        return caliGridService.findGridUser(userCacheDetail.getOrgId(), query);
    }

    @PostMapping("/user/list")
    @Operation(summary = "校准九宫格，单格内人员列表")
    @Auth(codes = {AUTH_CODE_ALL})
    public PagingList<CaliCellUserVO> findGridUserPage(
        @RequestBody CaliGridCell4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return caliGridService.findGridUserPage(userCacheDetail.getOrgId(), query);
    }

    @PostMapping("/move/view")
    @Operation(summary = "校准会拖动预览弹窗")
    @Auth(codes = {AUTH_CODE_ALL})
    public CaliDimResultResp moveView(
        @RequestBody CaliMeetMoveCmd cmd) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return caliGridService.moveView(userCacheDetail.getOrgId(), cmd);
    }

    @Operation(summary = "查询项目下人员的在各个维度下盘点校准等级结果")
    @GetMapping(value = "/{xpdId}/{calimeetId}/{userId}", produces = MEDIATYPE)
    @Parameters({@Parameter(name = "xpdId", description = "盘点项目id", in = ParameterIn.PATH),
                 @Parameter(name = "calimeetId", description = "校准会id", in = ParameterIn.PATH),
                 @Parameter(name = "userId", description = "人员id", in = ParameterIn.PATH)
    })
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public MeetUserDimResultVO getUserResult(
        HttpServletRequest request,
        @PathVariable String xpdId, @PathVariable String userId,
        @PathVariable String calimeetId) {
        // 当前用户信息
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliGridService.getUserResult(currentUser.getOrgId(), xpdId, userId, calimeetId, currentUser.getLocale());
    }

}
