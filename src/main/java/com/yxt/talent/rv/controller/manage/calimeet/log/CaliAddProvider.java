package com.yxt.talent.rv.controller.manage.calimeet.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetCreateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliAddLogDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/2 16:41
 **/
@Slf4j
@AllArgsConstructor
@Component
public class CaliAddProvider implements AuditLogDataProvider<CaliMeetCreateCmd, CaliAddLogDTO> {

    private final UdpLiteUserMapper udpLiteUserMapper;

    private final PrjMapper prjMapper;

    @Override
    public CaliAddLogDTO before(CaliMeetCreateCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public CaliAddLogDTO after(CaliMeetCreateCmd param, AuditLogBasicBean logBasic) {
        CaliAddLogDTO dto = new CaliAddLogDTO();
        dto.setMeetName(param.getMeetName());
        String format = FastDateFormat.getInstance("yyyy-MM-dd HH:mm").format(param.getMeetTime());
        dto.setMeetTime(format);
        if (CollectionUtils.isNotEmpty(param.getTalentCommitteeList())) {
            dto.setTalentCommitteeList(getUserString(logBasic.getOrgId(), param.getTalentCommitteeList()));
        }

        if (CollectionUtils.isNotEmpty(param.getOrganizerList())) {
            dto.setOrganizerList(getUserString(logBasic.getOrgId(), param.getOrganizerList()));
        }

        PrjPO prjPO = prjMapper.selectByOrgIdAndId(logBasic.getOrgId(), param.getProjectId());
        if (prjPO != null) {
            dto.setProjectName(prjPO.getProjectName());
        }
        return dto;
    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
                .append("（")
                .append(a.getUsername())
                .append("）")
                .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }

    @Override
    public Pair<String, String> entityInfo(
            CaliMeetCreateCmd param, CaliAddLogDTO beforeObj, CaliAddLogDTO afterObj,
            AuditLogBasicBean logBasic) {

        PrjPO prjPO = prjMapper.selectByOrgIdAndId(logBasic.getOrgId(), param.getProjectId());
        if (prjPO == null) {
            return null;
        }
        String name = AuditLogHelper.Module.CALIBRATION.getName() + "-" + param.getMeetName();
        return Pair.of(param.getProjectId(), name);
    }
}
