package com.yxt.talent.rv.controller.manage.xpd.user.command;

import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户端盘点结果【人员维度】->人员查看界面列表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class XpdUserResultSubCmd {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "账号")
    private String userName;

    @Schema(description = "岗位")
    private String position;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "头像")
    private String imgUrl;

    @Schema(description = "最新盘点结果")
    private XpdResultUserDimDetailVO userDimDetail;
}
