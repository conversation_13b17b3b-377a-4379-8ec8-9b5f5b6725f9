package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import com.yxt.talent.rv.controller.manage.common.viewobj.AttachmentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会浏览|跟踪基本信息页")
public class CaliMeetVO {

    @Schema(description = "主键Id")
    private String id;

    @Schema(description = "会议名称")
    private String meetName;

    @Schema(description = "所属盘点项目id")
    private String projectId;

    @Schema(description = "所属盘点项目")
    private String projectName;

    @Schema(description = "校准会状态（0-未开始，1-进行中，2-已结束）")
    private Integer meetStatus;

    @Schema(description = "会议时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private String meetTime;

    @Schema(description = "人才委员会人员Id列表")
    private List<CaliMeetUserSimpleVO> talentCommitteeList = new ArrayList<>();

    @Schema(description = "会议组织者人员Id列表")
    private List<CaliMeetUserSimpleVO> organizerList = new ArrayList<>();

    @Schema(description = "会议记录")
    private String meetMinutes;

    @Schema(description = "会议持续时间，用于绚星工作场会议 (单位分钟，最少60分钟，最长1440分钟（即1天）)")
    private Integer duration;

    @Schema(description = "附件列表")
    private List<AttachmentVO> appendixList = new ArrayList<>();
}
