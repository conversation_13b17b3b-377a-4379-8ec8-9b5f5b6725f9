package com.yxt.talent.rv.controller.manage.xpd.rule.command;

import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
@Getter
@Setter
@Schema(description = "快捷配置规则更新请求")
public class QuickCalcRule4Update implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

//    @Schema(description = "指标")
//    private List<AmSlDrawer4ReqDTO> indicatorid;

    @Schema(description = "指标")
    private List<QuickCalcRuleIndicator4Update> indicatorid;

    @Schema(description = "维度")
    private List<AmSlDrawer4ReqDTO> dimid;

    @Schema(description = "数据来源")
    private List<AmSlDrawer4ReqDTO> refactivities;

    @Schema(description = "计算逻辑[分值] 0-无 1-求平均 2-求和")
    private String scorecalcmethod;

    @Schema(description = "计算逻辑[达标率] 0-无 3-全部来源中达标 4-任一来源中达标")
    private String compliancecalcmethod;

    @Schema(description = "权重")
    private Integer weight;

    @Schema(description = "分值")
    private BigDecimal score;
}
