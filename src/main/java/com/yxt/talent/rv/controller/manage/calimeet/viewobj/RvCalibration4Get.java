package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import java.lang.String;
import java.util.Date;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import java.lang.Long;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
@Getter
@Setter
@Schema(description = "校准详情返回")
public class RvCalibration4Get implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="主键;id主键")
    private String id;

    @Schema(description="机构号;机构id")
    private String orgId;

    @Schema(description="校准会名称;名称")
    private String name;

    @Schema(description="创建时间")
    private Date createTime;

    @Schema(description="更新时间")
    private Date updateTime;

    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;

    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;

    @Schema(description="会议时间")
    private Date meettime;

    @Schema(description="会议组织者;按照业务需求,返回应用的实体字段")
    @JsonProperty("@meetorganizator")
    private AmUser4DTO meetorganizator;

    @Schema(description="人才委员会;按照业务需求,返回应用的实体字段")
    @JsonProperty("@meetcalibrator")
    private AmSlDrawer4RespDTO meetcalibrator;

    @Schema(description="状态")
    private String meetstatus;

    @Schema(description="校准人数")
    private Long calibrationnumbers;

    @Schema(description="盘点项目;按照业务需求,返回应用的实体字段")
    @JsonProperty("@rvprojectid")
    private AmSlDrawer4RespDTO rvprojectid;

}
