package com.yxt.talent.rv.controller.manage.xpd.log;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.o2ofacade.bean.response.talent.SpProjectInfoResp;
import com.yxt.o2ofacade.service.ProjectFacade;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdTrainingLogDTO;
import com.yxt.talent.rv.controller.manage.xpd.user.command.XpdUserAddActionPlanCmd;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class XpdTrainingLogProvider implements AuditLogDataProvider<XpdUserAddActionPlanCmd, XpdTrainingLogDTO> {

    private final UdpLiteUserMapper udpLiteUserMapper;
    private final ProjectFacade projectFacade;

    @Override
    public XpdTrainingLogDTO before(XpdUserAddActionPlanCmd bean, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public XpdTrainingLogDTO after(XpdUserAddActionPlanCmd bean, AuditLogBasicBean logBasic) {
        log.info("XpdTrainingLogProvider after={}", JSON.toJSONString(bean));
        XpdTrainingLogDTO xpdTrainingLogDTO = new XpdTrainingLogDTO();
        xpdTrainingLogDTO.setUsers(getUserString(logBasic.getOrgId(), bean.getUserIds()));

        List<Long> trainingIds = CommonUtil.str2Long(bean.getTargetIds());
        String orgId = logBasic.getOrgId();
        List<SpProjectInfoResp> spProjectInfoResps = projectFacade.listSpProjects(orgId, trainingIds);
        if (CollectionUtils.isNotEmpty(spProjectInfoResps)) {
            xpdTrainingLogDTO.setTrainingName(spProjectInfoResps.get(0).getName());
        }


        return xpdTrainingLogDTO;
    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
            .append("（")
            .append(a.getUsername())
            .append("）")
            .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }

    @Override
    public Pair<String, String> entityInfo(
        XpdUserAddActionPlanCmd bean, XpdTrainingLogDTO beforeObj, XpdTrainingLogDTO afterObj,
            AuditLogBasicBean logBasic) {
        String name = String.format("盘点-%s-发起培训", afterObj.getTrainingName());
        return Pair.of(bean.getXpdId(), name);

    }
}
