package com.yxt.talent.rv.controller.manage.dmp.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Setter
@Getter
@Schema(name = "项目发布或者设置定时发布的请求参数")
public class DmpPublishOrTimedCmd implements Command {

    @Schema(description = "发布方式（0-手动发布 1-定时发布）")
    private Integer launchType;

    @Schema(description = "定时发布时间类型（0-按开始时间 1-自定义时间）", title = "可以不传，默认自定义时间")
    private Integer launchTimeType = 1;

    @Schema(description = "定时发布时设置的自定义发布时间")
    private LocalDateTime launchTime;

    @Schema(description = "是否发送消息通知（0-不发送，1-发送）")
    private Integer notification = 1;

    @Schema(description = "不发送给学员， 0-发送 1-不发送")
    private Integer studentDisabled;

    @Schema(description = "不发送给负责人， 0-发送 1-不发送")
    private Integer directorDisabled;

    @Schema(description = "不发送给项目查看人， 0-发送 1-不发送")
    private Integer projectViewDisabled;

    @Schema(description = "不发送给项目管理人， 0-发送 1-不发送")
    private Integer projectMgrDisabled;

    @Schema(description = "不发送给评估人， 0-发送 1-不发送")
    private Integer evaluatorDisabled;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
