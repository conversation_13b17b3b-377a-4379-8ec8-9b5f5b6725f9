package com.yxt.talent.rv.controller.manage.calimeet.log;

import com.yxt.enums.DeleteEnum;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * @Description 关闭校准
 *
 * <AUTHOR>
 * @Date 2024/9/3 11:19
 **/
@Slf4j
@AllArgsConstructor
@Component
public class CaliCloseProvider implements AuditLogDataProvider<String, String> {

    private final CaliMeetMapper caliMeetMapper;

    @Override
    public String before(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public String after(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public Pair<String, String> entityInfo(
            String param, String beforeObj, String afterObj, AuditLogBasicBean logBasic) {

        CaliMeetPO cm =
                caliMeetMapper.selectByIdAndOrgIdAndDeleted(
                        param, logBasic.getOrgId(), DeleteEnum.NOT_DELETED.getCode());

        if (cm == null) {
            return null;
        }

        String name = AuditLogHelper.Module.CALIBRATION.getName() + "-" + cm.getMeetName() + "-结束";

        return Pair.of(param, name);

    }
}
