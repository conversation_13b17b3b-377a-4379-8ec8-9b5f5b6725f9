package com.yxt.talent.rv.controller.manage.meet.log;

import com.alibaba.fastjson.JSON;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.meet.legacy.MeetAppService;
import com.yxt.talent.rv.controller.manage.meet.command.MeetUserUpdateCmd;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetEditLogVO;
import com.yxt.talent.rv.domain.prj.entity.user.PrjUserResult;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description 人员校准日志
 *
 * <AUTHOR>
 * @Date 2024/8/12 15:41
 **/
@Slf4j
@AllArgsConstructor
@Component
public class CaliUserResultLogProvider implements AuditLogDataProvider<MeetUserUpdateCmd, MeetEditLogVO> {

    private final MeetUserResultMapper meetUserResultMapper;

    private final MeetAppService caliMeetAppService;

    private final MeetMapper meetMapper;

    private final UdpLiteUserMapper udpLiteUserMapper;

    private final PrjDimMapper prjDimMapper;



    @Override
    public MeetEditLogVO before(MeetUserUpdateCmd param, AuditLogBasicBean logBasic) {
        log.info("CaliUserResultLogProvider before param:{}", JSON.toJSONString(param));
        MeetEditLogVO res = new MeetEditLogVO();
        res.setUserCali(getResMsg(param, logBasic, 1));
        return res;
    }

    @DbHintMaster
    private String getResMsg(MeetUserUpdateCmd param, AuditLogBasicBean logBasic , int flag) {
        MeetUserPO calMeetUser =
                caliMeetAppService.getById(param.getId(), logBasic.getOrgId());
        if (calMeetUser == null) {
            return "";
        }
        List<MeetUserResultPO> userResults =
                meetUserResultMapper.listByOrgIdAndMeetIdAndUserIdIn(
                        logBasic.getOrgId(),
                        calMeetUser.getMeetingId(),
                        Lists.newArrayList(calMeetUser.getUserId()));
        if (userResults.isEmpty()) {
            return "";
        }
        List<String> dimIds = userResults.stream().map(MeetUserResultPO::getDimensionId).toList();
        List<PrjDimPO> prjDims =
                prjDimMapper.selectByOrgIdAndIfDimIdsOrderByCreateTime(logBasic.getOrgId(), dimIds);

        Map<String, PrjDimPO> prjDimMap = StreamUtil.list2map(prjDims, PrjDimPO::getId);
        List<String> res = new ArrayList<>();
        for (MeetUserResultPO userResult : userResults) {
            String dimensionId = userResult.getDimensionId();
            PrjDimPO prjDimPO = prjDimMap.get(dimensionId);
            res.add(prjDimPO.getDimensionName() + ":" + findStrLevel(userResult, flag));
        }
        return String.join(";", res);
    }

    @Override
    public MeetEditLogVO after(MeetUserUpdateCmd param, AuditLogBasicBean logBasic) {
        log.info("CaliUserResultLogProvider after param:{}", JSON.toJSONString(param));
        MeetEditLogVO res = new MeetEditLogVO();
        res.setUserCali(getResMsg(param, logBasic, 2));
        return res;
    }

    /**
     * 获取维度名称
     *
     * @param userResult
     * @param param 1 前， 2 后
     * @return
     */
    private String findStrLevel(MeetUserResultPO userResult, int param) {
        if (param == 1) {
            return PrjUserResult.Level.getNameByCode(userResult.getOriginalLevel());
        } else {
            return userResult.getCalibrationLevel() == 0 ?
                    PrjUserResult.Level.getNameByCode(userResult.getOriginalLevel()) :
                    PrjUserResult.Level.getNameByCode(userResult.getCalibrationLevel());
        }
    }

    @Override
    public Pair<String, String> entityInfo(
            MeetUserUpdateCmd param, MeetEditLogVO beforeObj, MeetEditLogVO afterObj,
            AuditLogBasicBean logBasic) {
        MeetUserPO calMeetUser =
                caliMeetAppService.getById(param.getId(), logBasic.getOrgId());

        if (calMeetUser == null) {
            return Pair.of(param.getId(), "");
        }

        MeetPO meetPO = meetMapper.selectByIdAndOrgId(calMeetUser.getMeetingId(),
                calMeetUser.getOrgId());
        if (meetPO == null) {
            return Pair.of(param.getId(), "");
        }
        UdpLiteUserPO udpLiteUserPO =
                udpLiteUserMapper.selectByUserId(logBasic.getOrgId(), calMeetUser.getUserId());
        if (udpLiteUserPO == null) {
            return Pair.of(param.getId(), "");
        }
        log.info("CaliUserResultLogProvider entityInfo id={}", calMeetUser.getId());
        return Pair.of(param.getId(), String.format("盘点校准会-%s-人员校准-%s", meetPO.getMeetName()
                , udpLiteUserPO.getFullname()));
    }
}
