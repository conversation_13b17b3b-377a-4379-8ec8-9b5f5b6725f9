package com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "维度组合对象")
public class XpdDimCombListVO {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    @Schema(description = "机构ID, 全局模板存00000000-0000-0000-0000-000000000000")
    private String orgId;

    /**
     * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
     */
    @Schema(description = "项目ID, 机构模板存00000000-0000-0000-0000-000000000000")
    private String xpdId;

    /**
     * 组合名称
     */
    @Schema(description = "组合名称")
    private String combName;

    /**
     * 组合名称国际化
     */
    @Schema(description = "组合名称国际化")
    private String combNameI18n;

    /**
     * x轴维度id
     */
    @JsonProperty("xSdDimId")
    @Schema(description = "x轴维度id")
    private String xSdDimId;

    /**
     * y轴维度id
     */
    @JsonProperty("ySdDimId")
    @Schema(description = "y轴维度id")
    private String ySdDimId;

    /**
     * x轴维度名称
     */
    @JsonProperty("xSdDimName")
    @Schema(description = "x轴维度名称")
    private String xSdDimName;

    /**
     * y轴维度名称
     */
    @JsonProperty("ySdDimName")
    @Schema(description = "y轴维度名称")
    private String ySdDimName;

    /**
     * 类型:0-内置,1-自建
     */
    @Schema(description = "类型:0-内置,1-自建")
    private Integer combType;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String combDesc;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private String createUserId;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createUserName;


}
