package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会检查结果")
public class CaliMeetQtyVO {
    @Schema(description = "关联的校准会数量")
    private int rfCount;

    @Schema(description = "进行中的校准会数量")
    private int underwayCount;
}
