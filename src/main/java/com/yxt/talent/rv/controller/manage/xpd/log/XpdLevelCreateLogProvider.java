package com.yxt.talent.rv.controller.manage.xpd.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.xpd.grid.command.XpdLevelCreateCmd;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdLevelCreateLogDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2025/4/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XpdLevelCreateLogProvider implements AuditLogDataProvider<XpdLevelCreateCmd, XpdLevelCreateLogDto> {
    private final XpdGridMapper gridMapper;
    private final XpdLevelMapper levelMapper;
    @Override
    public XpdLevelCreateLogDto before(XpdLevelCreateCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public XpdLevelCreateLogDto after(XpdLevelCreateCmd param, AuditLogBasicBean logBasic) {
        XpdLevelCreateLogDto dto = new XpdLevelCreateLogDto();

        String gridId = param.getGridId();
        List<XpdLevelPO> xpdLevels = levelMapper.listByGridId(logBasic.getOrgId(), gridId);
        if (CollectionUtils.isEmpty(xpdLevels)) {
            return dto;
        }
        dto.setOrderIndex(String.valueOf(xpdLevels.size()));

        dto.setLevelName(param.getLevelName());
        XpdLevelPO xpdLevel = xpdLevels.get(xpdLevels.size() - 1);
        dto.setLevelRule(xpdLevel.getFormulaDisplay());
        return dto;
    }

    @Override
    public Pair<String, String> entityInfo(
        XpdLevelCreateCmd param, XpdLevelCreateLogDto beforeObj, XpdLevelCreateLogDto afterObj,
        AuditLogBasicBean logBasic) {
        String gridId = param.getGridId();
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(gridId);
        return Pair.of(gridId, "宫格模板-" + xpdGrid.getGridName() + "-人才分层");
    }
}
