package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.yxt.talent.rv.controller.manage.prj.user.viewobj.CliMeetRemarkVO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetDimDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "人员盘点校准维度成绩")
public class CaliMeetUserDimResultVO {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "岗位")
    private String position;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "头像")
    private String imgUrl;

    @Schema(description = "盘点维度结果")
    private List<CaliMeetDimDTO> result;

    @Schema(description = "当前盘点项目中的校准备注")
    private String currRemark;

    @Schema(description = "历史校准备注（即在其他校准会中获得的校准备注）")
    private List<CliMeetRemarkVO> remarks = new ArrayList<>(8);

    @Schema(description = "发展建议")
    private String suggestion;
}
