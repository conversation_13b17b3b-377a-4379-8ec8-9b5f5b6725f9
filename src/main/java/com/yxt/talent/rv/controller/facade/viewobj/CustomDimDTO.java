package com.yxt.talent.rv.controller.facade.viewobj;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 客户自定义维度
 * <AUTHOR>
 * @Date 2024/5/9 17:49
 **/
@Data
public class CustomDimDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long relationType;

    private String dimensionName;

    @Schema(name = "权重")
    private BigDecimal weight;
}
