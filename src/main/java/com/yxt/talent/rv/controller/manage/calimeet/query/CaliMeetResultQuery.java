package com.yxt.talent.rv.controller.manage.calimeet.query;

import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetResultQuery {

    @Schema(description = "盘点项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String projectId;

    @Schema(description = "校准会id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String meetingId;

    @Schema(description = "x轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisX;

    @Schema(description = "y轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisY;

    @Schema(description = "查询参数")
    private String searchKey;

    @Schema(description = "部门ids")
    private List<String> deptIds;

    @Schema(description = "岗位ids")
    private List<String> positionIds;

    @Schema(description = "职级ids")
    private List<String> gradeIds;

    @Schema(description = "员工状态 0：禁用 1：启用，2:删除，-1：查询所有（默认）")
    private int status = -1;

    @Schema(description = "x轴维度值（1-3对应低至高）")
    private Integer valueX;

    @Schema(description = "y轴维度值（1-3对应低至高）")
    private Integer valueY;

    private List<String> userIds;

    private List<String> authUserIds;

    public String getEscapedKeyword() {
        return SqlUtil.escapeSql(searchKey);
    }
}
