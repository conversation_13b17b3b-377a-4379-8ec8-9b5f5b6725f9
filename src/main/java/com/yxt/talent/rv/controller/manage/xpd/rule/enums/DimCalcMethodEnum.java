package com.yxt.talent.rv.controller.manage.xpd.rule.enums;

import lombok.Getter;

/**
 * 盘点维度的计算类型
 *
 * <AUTHOR>
 */
@Getter
public enum DimCalcMethodEnum {

    /**
     * 计算逻辑:0-无 1-求平均 2-求和 3-全部来源中达标 4-任一来源中达标
     */
    NONE(0, "无"),
    AVG(1, "求平均"),
    SUM(2, "求和"),
    QUALIFIED_ALL(3, "全部来源中达标"),
    QUALIFIED_ONE(4, "任一来源中达标"),
    ;

    private final int code;
    private final String name;

    DimCalcMethodEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(int code) {
        for (DimCalcMethodEnum value : DimCalcMethodEnum.values()) {
            if(code == value.code) {
                return value.name;
            }
        }
        return "";
    }

    public static boolean byScore(Integer code) {
        return NONE.getCode() == code || AVG.getCode() == code || SUM.getCode() == code;
    }

    public static boolean byRatio(Integer code) {
        return NONE.getCode() == code || QUALIFIED_ALL.getCode() == code || QUALIFIED_ONE.getCode() == code;
    }
}
