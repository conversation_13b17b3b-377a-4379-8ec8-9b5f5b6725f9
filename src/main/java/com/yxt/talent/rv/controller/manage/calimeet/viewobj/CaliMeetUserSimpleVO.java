package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会参会人员|会议主持人")
public class CaliMeetUserSimpleVO implements L10NContent {

    @Schema(description = "会议id")
    private String meetingId;

    @Schema(description = "用户Id")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "姓名")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String userName;

    @Schema(description = "类型（1-组织者，2-参会人）")
    private Integer userType;

    @Schema(description = "头像")
    private String imgUrl;
}
