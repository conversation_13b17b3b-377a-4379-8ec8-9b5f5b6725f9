package com.yxt.talent.rv.controller.manage.prj.dim.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class SkillDetailVO {
    @Schema(description = "能力id")
    private String skillId;

    @Schema(description = "能力名称")
    private String skillName;

    @Schema(description = "能力等级")
    private Integer skillLevel;
}
