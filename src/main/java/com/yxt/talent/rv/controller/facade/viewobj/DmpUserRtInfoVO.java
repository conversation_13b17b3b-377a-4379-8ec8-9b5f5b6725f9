package com.yxt.talent.rv.controller.facade.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 学员人岗匹配职责任务信息
 *
 * <AUTHOR>
 * @Date 2024/4/23 17:54
 **/
@Data
public class DmpUserRtInfoVO {

    private String dmpId;

    private String userId;


    @Schema(description = "任职资格模型id")
    private String rtModelId;

    @Schema(description = "任职资格id")
    private String rtId;


    @Schema(description = "任职资格名称")
    private String rtName;

    @Schema(description = "是否达标：0-不达标 1-达标 2-异常不达标")
    private Integer matched;

}
