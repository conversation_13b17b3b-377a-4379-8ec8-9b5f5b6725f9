package com.yxt.talent.rv.controller.client.bizmgr.team.viewobj;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.OrgProfileBasicClientVO;
import com.yxt.talent.rv.controller.manage.org.viewobj.OrgProfileBaseInfoVO;
import com.yxt.talent.rv.application.org.profile.dto.OrgProfileBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Setter
@Getter
@ToString
@NoArgsConstructor
@Schema(name = "学员端-我的团队-团队看板-部门基础信息统计接口（职级 司龄 岗位 部门 性别 学历 年龄 序列）")
public class TeamOrgProfileBasicClientVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -1668704431101283088L;

    @Schema(description = "职级列表")
    private List<OrgProfileBaseInfoDTO> jobGrades;

    @Schema(description = "司龄列表")
    private List<OrgProfileBaseInfoDTO> empdurs;

    @Schema(description = "岗位列表")
    private List<OrgProfileBaseInfoDTO> positions;

    @Schema(description = "部门列表")
    private List<OrgProfileBaseInfoDTO> depts;

    @Schema(description = "性别占比列表")
    private OrgProfileSexInfoDTO genders;

    @Schema(description = "学历列表")
    private List<OrgProfileBaseInfoDTO> educations;

    @Schema(description = "年龄列表")
    private List<OrgProfileBaseInfoDTO> ages;

    @Schema(description = "序列人数分布列表")
    private List<OrgProfileBaseInfoDTO> seriesList;

    @Schema(description = "部门总人数")
    private long deptUserCnt;

    // 格式化为保留两位小数
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#.##")
    @Schema(description = "部门平均年龄")
    private BigDecimal avgAge;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#.00")
    @Schema(description = "部门平均司龄")
    private BigDecimal avgServiceYears;

    @Nullable
    public BigDecimal getAvgAge() {
        // 格式化avgAge为保留两位小数
        if (avgAge != null) {
            avgAge = avgAge.setScale(2, RoundingMode.HALF_UP);
        }
        return avgAge;
    }

    @Nullable
    public BigDecimal getAvgServiceYears() {
        // 格式化avgServiceYears为保留两位小数
        if (avgServiceYears != null) {
            avgServiceYears = avgServiceYears.setScale(2, RoundingMode.HALF_UP);
        }
        return avgServiceYears;
    }

    public TeamOrgProfileBasicClientVO(
            OrgProfileBaseInfoVO orgProfileBaseInfoVO,
            OrgProfileBasicClientVO orgProfileBasicClientVO,
            OrgProfileSexInfoDTO orgProfileSexInfoDTO) {
        if (orgProfileBaseInfoVO != null) {
            this.setEmpdurs(orgProfileBaseInfoVO.getEmpdurs());
            this.setJobGrades(orgProfileBaseInfoVO.getJobGrades());
            this.setPositions(orgProfileBaseInfoVO.getPositions());
            this.setDepts(orgProfileBaseInfoVO.getDepts());
        }
        if (orgProfileBasicClientVO != null) {
            this.setEducations(orgProfileBasicClientVO.getEducations());
            this.setAges(orgProfileBasicClientVO.getAges());
            this.setSeriesList(orgProfileBasicClientVO.getSeriesList());
        }
        if (orgProfileSexInfoDTO != null) {
            this.setGenders(orgProfileSexInfoDTO);
        }
    }

}
