package com.yxt.talent.rv.controller.root;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.common.bean.UserBasicInfo;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.spsdk.udpbase.QueryUdpUtils;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.ubiz.tree.domain.converter.UTreeNodeConverter;
import com.yxt.ubiz.tree.domain.entity.UTreeNode;
import com.yxt.ubiz.tree.domain.model.req.UNodeTreeGenericQueryReq;
import com.yxt.ubiz.tree.domain.model.req.UNodeTreeWithDataScopeQueryReq;
import com.yxt.ubiz.tree.domain.model.resp.UNodeTreeResp;
import com.yxt.ubiz.tree.service.UTreeNodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Locale;

@Slf4j
@Tag(name = "树组件")
@Tag(name = "树节点(管理端)")
@RequestMapping("/proxy/api/mgr/utree/node")
@RestController
public class UTreeNodeMgrProxyController {
    @Autowired(required = false)
    private UTreeNodeService uTreeNodeService;
    @Autowired
    private AuthService authService;

    @Operation(summary = "查询子节点(没有数据权限)")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/list/by-pnode-id", produces = MediaType.APPLICATION_JSON_VALUE)
    @Auth(type = {AuthType.TOKEN})
    public UNodeTreeResp queryTreeNodes(
        HttpServletRequest request, @RequestBody UNodeTreeWithDataScopeQueryReq queryReq) {
        String pnodeId = queryReq.getPnodeId();
        UserBasicInfo userInfo = YxtBasicUtils.userInfo();
        if ( "0".equals(pnodeId) || StringUtils.isEmpty(pnodeId)) {
            queryReq.setPnodeId(null);
            String orgId = userInfo.getOrgId();
            String treeId = queryReq.getTreeId();
            if (UTreeEnum.getByTreeId(treeId) != null
                && uTreeNodeService.findRootNode(orgId, treeId).orElse(null) == null) {
                //没有根目录生成一条
                String adminUserId = QueryUdpUtils.adminUserId(orgId);
                UTreeNode utreeNode = new UTreeNode();
                utreeNode.setId(IdWorker.getId());
                utreeNode.setOrgId(orgId);
                utreeNode.setTreeId(treeId);
                utreeNode.setNid("1");
                utreeNode.setNodeName("默认分类");
                utreeNode.setCreatedAt(LocalDateTime.now());
                utreeNode.setUpdatedAt(LocalDateTime.now());
                utreeNode.setDeleted(YesOrNo.NO.getValue());
                utreeNode.setCreatedBy(adminUserId);
                utreeNode.setUpdatedBy(adminUserId);
                uTreeNodeService.updateNode(utreeNode, null, Locale.CHINESE);
            }
        }
        UNodeTreeGenericQueryReq req = UTreeNodeConverter.INSTANCE.fromQuery(queryReq);
        makeGenericQueryReq(request, userInfo, req);
        UNodeTreeResp resp = uTreeNodeService.queryChildNodesWithoutDataScope(req);
        resp.setSortable(YesOrNo.YES.getValue());
        return resp;
    }

    private void makeGenericQueryReq(HttpServletRequest request, UserBasicInfo userInfo, UNodeTreeGenericQueryReq req) {
        req.setUseDataScope(true);
        req.setProductCode(ApiUtil.getProductCode(request));
        req.setOrgId(userInfo.getOrgId());
        req.setOperator(userInfo.getUserId());
        req.setLocale(authService.getLocale());
        req.setNeedRootNode(uTreeNodeService.needRootNode(req.getTreeId()));
    }
}
