package com.yxt.talent.rv.controller.manage.meet;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.controller.manage.meet.command.MeetPrjUserResultAddCmd;
import com.yxt.talent.rv.controller.manage.meet.command.MeetUserRemarkUpdateCmd;
import com.yxt.talent.rv.controller.manage.meet.query.MeetResultQuery;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetResultDetailVO;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetUserDimResultVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.XpdUserGridResultVO;
import com.yxt.talent.rv.application.meet.legacy.MeetAppService;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;


@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "盘点校准结果", description = "盘点校准结果")
@RequestMapping(value = "/mgr/calibrationresult")
public class MeetResultManageController {
    private final AuthService authService;
    private final MeetAppService caliMeetAppService;

    @SwaggerPageQuery
    @Operation(summary = "盘点校准结果查询（九宫格坐标）")
    @PostMapping(value = "/page", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public PagingList<XpdUserGridResultVO> findPage(
            HttpServletRequest request,
            @RequestBody MeetResultQuery search) {
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        // 当前用户信息
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppService.findByXYDimensionPage(search, currentUser.getOrgId(),
                search.getValueX(), search.getValueY(), pageRequest, currentUser.getLocale(), currentUser);
    }

    @Operation(summary = "盘点结果查询（九宫格初始化）2")
    @PostMapping(value = "/palaces", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public MeetResultDetailVO palaces(
            HttpServletRequest request,
            @RequestBody MeetResultQuery search) {
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        // 当前用户信息
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppService.getCheckPrjCalcList(
                search, currentUser.getOrgId(), pageRequest, currentUser.getLocale(), currentUser);
    }

    @Operation(summary = "保存前端传入的盘点校准数据，同时也用于绚星会议工作场中校准结果回传")
    @PostMapping(value = "/{meetingId}/{projectId}", consumes = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public void projectUserCalResultCheck(
            HttpServletRequest request,
            @PathVariable String projectId, @PathVariable String meetingId,
            @RequestBody List<MeetPrjUserResultAddCmd> prjUserCalcResult) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.checkPrjCalcResult(operator.getUserId(), operator.getOrgId(),
                projectId, meetingId, prjUserCalcResult);
    }

    @Operation(summary = "保存工作场回传的人员备注和发展建议")
    @PostMapping(value = "/{meetingId}/{projectId}/remark", consumes = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public void updateRemarkAndSuggestion(
            HttpServletRequest request,
            @PathVariable String projectId, @PathVariable String meetingId,
            @RequestBody MeetUserRemarkUpdateCmd update) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.updateRemarkAndSuggestion(operator.getOrgId(), projectId, meetingId,
                update, operator.getUserId());
    }

    @Operation(summary = "查询项目下人员的在各个维度下盘点校准等级结果")
    @GetMapping(value = "/{projectId}/{meetingId}/{userId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public MeetUserDimResultVO getUserResult(
            HttpServletRequest request,
            @PathVariable String projectId, @PathVariable String userId,
            @PathVariable String meetingId) {
        // 当前用户信息
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppService.getCalibrationMeetingResultUserDetail(
                userId, meetingId, currentUser.getOrgId(), projectId, currentUser.getLocale());
    }
}
