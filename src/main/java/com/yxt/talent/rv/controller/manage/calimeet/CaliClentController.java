package com.yxt.talent.rv.controller.manage.calimeet;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.application.calimeet.CaliClientService;
import com.yxt.talent.rv.application.calimeet.CaliGridService;
import com.yxt.talent.rv.application.calimeet.CaliMeetUserAppService;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserVO;
import com.yxt.talent.rv.application.common.CommonAppService;
import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultResp;
import com.yxt.talent.rv.application.xpd.dimcomb.XpdDimCombAppService;
import com.yxt.talent.rv.application.xpd.grid.XpdGridAppService;
import com.yxt.talent.rv.controller.client.general.meet.query.MeetClientQuery;
import com.yxt.talent.rv.controller.client.general.meet.viewobj.MeetClientVO;
import com.yxt.talent.rv.controller.common.viewobj.PrjLabelVO;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliClentUserCmd;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetMoveCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliGridCell4Query;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliResult4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliCellUserVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliGridViewDataVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliGridViewVO;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetResultDetailVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombInfoVO;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.application.xpd.result.XpdResultAppService.NAV_CODE_XPD;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT;
import static org.springframework.http.HttpStatus.OK;

/**
 * <AUTHOR>
 * @description 校准会学员端
 * @date 2025/5/23
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "校准会学员端", description = "校准会学员端")
@RequestMapping(value = "/client/cali")
public class CaliClentController {
    private final AuthService authService;
    private final CaliClientService caliClientService;
    private final CaliGridService caliGridService;
    private final CaliMeetUserAppService caliMeetUserAppService;
    private final CommonAppService commonAppService;
    private final XpdDimCombAppService xpdDimCombAppService;
    private final XpdGridAppService xpdGridAppService;

    @SwaggerPageQuery
    @Operation(summary = "校准会列表")
    @PostMapping(value = "/list", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public PagingList<MeetClientVO> clientList(
        HttpServletRequest request,
        @RequestBody MeetClientQuery search) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return caliClientService.clientList(
            ApiUtil.getPageRequest(request), search,
            userCache.getOrgId(), userCache.getUserId());
    }

    @SwaggerPageQuery
    @Operation(summary = "校准会基本信息")
    @Parameter(name = "calimeetId", description = "校准会id", in = ParameterIn.PATH)
    @GetMapping(value = "/msg/{calimeetId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public MeetClientVO getCaliMsg(
        HttpServletRequest request, @PathVariable String calimeetId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return caliClientService.getCaliMsg(userCache.getOrgId(), calimeetId, userCache.getUserId());
    }

    @PostMapping("/view")
    @Operation(summary = "宫格校准概览")
    @Auth(codes = {AUTH_CODE_ALL})
    public CaliGridViewVO caliGridView(
        @RequestBody CaliResult4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        commonAppService.fillAuthInfo(userCacheDetail.getOrgId(), query, NAV_CODE_XPD, SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT);
        return caliGridService.caliGridView(userCacheDetail.getOrgId(), query);
    }

    @PostMapping("/palaces")
    @Operation(summary = "学员端-九宫格人员落位列表")
    @Auth(codes = {AUTH_CODE_ALL})
    public MeetResultDetailVO findGridUser(
        @RequestBody CaliResult4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        commonAppService.fillAuthInfo(userCacheDetail.getOrgId(), query, NAV_CODE_XPD, SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT);
        return caliGridService.findGridUser(userCacheDetail.getOrgId(), query);
    }

    @PostMapping("/user/list")
    @Operation(summary = "校准九宫格，单格内人员列表")
    @Auth(codes = {AUTH_CODE_ALL})
    public PagingList<CaliCellUserVO> findGridUserPage(
        @RequestBody CaliGridCell4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return caliGridService.findGridUserPage(userCacheDetail.getOrgId(), query);
    }

    @PostMapping("/move/view")
    @Operation(summary = "校准会拖动预览弹窗")
    @Auth(codes = {AUTH_CODE_ALL})
    public CaliDimResultResp moveView(
        @RequestBody CaliMeetMoveCmd cmd) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return caliGridService.moveView(userCacheDetail.getOrgId(), cmd);
    }

    @PostMapping("/{caliMeetId}/user/list")
    @Parameter(name = "calimeetId", description = "校准会id", in = ParameterIn.PATH)
    @Operation(summary = "获取校准人员列表")
    @Auth(codes = {AUTH_CODE_ALL})
    public PagingList<CaliMeetUserVO> listCaliMeetUsers(
        @PathVariable @Parameter(description = "校准会ID") String caliMeetId,
        @RequestBody CaliMeetUserQuery queryParam) {
        queryParam.setOpenAuth(false);
        return caliMeetUserAppService.pageCaliMeetUsers(caliMeetId, queryParam);
    }

    @PutMapping("/user/cali/status")
    @Operation(summary = "校准完成")
    @Auth(codes = {AUTH_CODE_ALL})
    public void updateUserCaliStatus(
        HttpServletRequest request, @RequestBody CaliClentUserCmd cmd) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        caliClientService.updateUserCaliStatus(userCacheDetail.getOrgId(), cmd, userCacheDetail.getUserId());
    }

    @ResponseStatus(OK)
    @Auth(codes = {AUTH_CODE_ALL})
    @Operation(summary = "学员端-根据项目维度组列表")
    @GetMapping(value = "/dimcomb/{xpdId}", produces = MEDIATYPE)
    public List<XpdDimCombInfoVO> getDimCombList(HttpServletRequest request, @PathVariable String xpdId) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return xpdDimCombAppService.getDimCombList(currentUser, xpdId);
    }


    @Operation(summary = "新盘点-查询盘点九宫格标签")
    @Parameters(value = {
        @Parameter(name = "projectId", description = "项目id", in = ParameterIn.QUERY),
        @Parameter(name = "dimCombId", description = "维度组合id", in = ParameterIn.QUERY)})
    @GetMapping(value = "/project/labels/{projectId}/{dimCombId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public CommonList<PrjLabelVO> getGridLabels(
        HttpServletRequest request, @PathVariable String projectId,
        @PathVariable String dimCombId) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        return new CommonList<>(xpdGridAppService.getGridLabels(operator.getOrgId(), projectId, dimCombId));
    }

}
