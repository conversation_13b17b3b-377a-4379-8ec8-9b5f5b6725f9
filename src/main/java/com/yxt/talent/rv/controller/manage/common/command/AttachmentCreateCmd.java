package com.yxt.talent.rv.controller.manage.common.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "附件")
@ToString(callSuper = true)
public class AttachmentCreateCmd {
    @Schema(description = "id")
    private String id;

    // 附件名称
    @Schema(description = "附件名称")
    private String appName = "";

    // 附件链接
    @Schema(description = "附件链接")
    private String appUrl = "";
}
