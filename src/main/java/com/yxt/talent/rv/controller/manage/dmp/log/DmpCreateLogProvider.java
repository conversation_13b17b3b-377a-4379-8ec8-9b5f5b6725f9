package com.yxt.talent.rv.controller.manage.dmp.log;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpCreateCmd;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpPosCreateCmd;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpCreateLogDTO;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpUserMatchRateVO;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 动态人岗匹配，创建
 */
@Slf4j
@AllArgsConstructor
@Component
public class DmpCreateLogProvider implements AuditLogDataProvider<DmpCreateCmd, DmpCreateLogDTO> {
    @Nullable
    @Override
    public DmpCreateLogDTO before(DmpCreateCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public DmpCreateLogDTO after(DmpCreateCmd param, AuditLogBasicBean logBasic) {
        log.info("DmpCreateLogProvider after={}", JSON.toJSONString(param));
        DmpCreateLogDTO  res = new DmpCreateLogDTO();
        res.setDmpName(param.getDmpName());
        List<DmpPosCreateCmd> posInfos = param.getPosInfos();
        if (CollectionUtils.isNotEmpty(posInfos)){
            String posNames = posInfos.stream()
                    .map(DmpPosCreateCmd::getPositionName)
                    .collect(Collectors.joining(","));
            res.setPosNames(posNames);
        }
        res.setJqName(param.getJqName());

        res.setStartTime(param.getStartTime().toString());
        res.setEndTime(param.getEndTime().toString());
        List<DmpUserMatchRateVO> leaders = param.getLeaders();
        List<String> names = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(leaders)){
            for (DmpUserMatchRateVO leader : leaders) {
                names.add(leader.getFullname() + "(" + leader.getUsername() + ")");
            }
            String leaderNames = String.join(",", names);
            res.setLeaderNames(leaderNames);
        }

        res.setRemark(param.getRemark());
        res.setStopType(param.getStopType() == 0 ?  "否" : "是");
        res.setDisableEval(param.getDisableEval() == 0 ? "否" : "是");
        res.setShowType(param.getShowType() == 0 ? "关闭" : "开启");
        res.setGroupAutoExit(param.getGroupAutoExit() == 0 ? "关闭" : "开启");
        log.info("DmpCreateLogProvider res={}", JSON.toJSONString(res));
        return res;
    }

    @Override
    public Pair<String, String> entityInfo(
            DmpCreateCmd param, DmpCreateLogDTO beforeObj, DmpCreateLogDTO afterObj,
            AuditLogBasicBean logBasic) {
        String name = String.format("人岗匹配项目-%s", param.getDmpName());
        return Pair.of("", name);
    }
}
