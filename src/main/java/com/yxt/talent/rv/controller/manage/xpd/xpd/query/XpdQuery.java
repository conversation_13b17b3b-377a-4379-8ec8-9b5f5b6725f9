package com.yxt.talent.rv.controller.manage.xpd.xpd.query;


import com.yxt.talent.rv.controller.common.query.SearchKeyQuery;
import com.yxt.talent.rv.controller.manage.prj.prj.query.PrjQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "项目查询参数")
public class XpdQuery extends SearchKeyQuery {

    @Schema(description = "项目id")
    private String xpdId;

    @Schema(description = "项目名称, 模糊查询")
    private String searchKey;

    @Schema(description = "项目的aomid, 指向rv_activity.id")
    private String aomPrjId;

    @Schema(description = "模型id")
    private String modelId;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "盘点状态, 状态(1-未发布, 2-进行中, 3-已结束, -1-查询所有)")
    private Integer status;

    @Schema(description = "盘点状态, 状态(1-未发布, 2-进行中, 3-已结束, -1-查询所有)")
    private List<Integer> statusIn;

    public XpdQuery(PrjQuery prjQuery) {
        this.orgId = prjQuery.getOrgId();
        this.status = prjQuery.getProjectStatus();
        this.searchKey = prjQuery.getEscapedProjectName();
    }

}
