package com.yxt.talent.rv.controller.facade.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description  个人分层详情
 *
 * <AUTHOR>
 * @Date 2024/4/23 10:39
 **/
@Data
public class DmpUserLayerFacadeVO {

    @Schema(description = "分层id")
    private String layerId;

    @Schema(description = "人员id")
    private String userId;

    @Schema(description = "分层名称")
    private String layerName;

    @Schema(description = "是否胜任, 0-不胜任（低于胜任分层） 1-胜任（位于胜任及以上的分层）")
    private Integer competent;

}
