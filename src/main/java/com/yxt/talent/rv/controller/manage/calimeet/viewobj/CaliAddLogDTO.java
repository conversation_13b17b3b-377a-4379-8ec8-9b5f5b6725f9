package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import com.yxt.spsdk.audit.base.AuditLogDynamicBean;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/2 16:42
 **/
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliAddLogDTO implements AuditLogDynamicBean {

    @AuditLogField(name = "会议名称", orderIndex = 1)
    private String meetName;

    @AuditLogField(name = "会议时间", orderIndex = 2)
    private String meetTime;

    @AuditLogField(name = "人才委员会", orderIndex = 3)
    private String talentCommitteeList;

    @AuditLogField(name = "会议组织者", orderIndex = 4)
    private String organizerList;

    @AuditLogField(name = "所属盘点项目", orderIndex = 5)
    private String projectName;

    @Override
    public Set<String> logFieldKeys() {
        return Set.of();
    }
}
