package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2025/6/3
 */
@Data
public class CaliGridViewVO {

    @Schema(description = "校准方式(0-维度分层结果，1-维度结果，2-指标结果), 维度分层可以拖动校准")
    private Integer calimeetType;

    private List<CaliGridViewDataVO> gridViewDataVOList = new ArrayList<>();

}
