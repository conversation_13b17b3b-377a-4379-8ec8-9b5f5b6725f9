package com.yxt.talent.rv.controller.manage.org;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.talent.rv.application.dept.DeptQryAppService;
import com.yxt.talent.rv.application.org.OrgQryAppService;
import com.yxt.talent.rv.application.org.profile.legacy.OrgProfileAppService;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjAppService;
import com.yxt.talent.rv.application.user.UserQryAppService;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.OrgProfileBasicClientVO;
import com.yxt.talent.rv.controller.manage.org.query.OrgProfileUserDimGridQuery;
import com.yxt.talent.rv.controller.manage.org.viewobj.*;
import com.yxt.talent.rv.controller.manage.user.viewobj.UserProfilePrjUserGridVO;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talentrvfacade.bean.UserDimGrid4Info;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "管理端-组织画像", description = "管理端-组织画像")
@RequestMapping("/mgr/orgportrait")
public class OrgProfileManageController {
    private final AuthService authService;
    private final OrgProfileAppService orgProfileAppService;

    @Operation(summary = "人才结构-职级&司龄&岗位&部门")
    @GetMapping(value = "/deptbase", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public OrgProfileBaseInfoVO getDeptBaseInfo(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return orgProfileAppService.getDeptBaseInfo(userCache.getOrgId(), yearly, deptId);
    }

    private String initParamYear(String yearly) {
        if (StringUtils.isNotBlank(yearly)) {
            return yearly;
        }

        return DateTimeUtil.getNowYear();
    }

    @Operation(summary = "（干部结构-整体干部数量）&（干部结构-性别）")
    @GetMapping(value = "/manage/deptgroup", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public OrgProfileDeptGroupVO getDeptGroup(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return orgProfileAppService.getDeptGroup(userCache.getOrgId(), yearly, deptId);
    }

    @Operation(summary = "干部结构-管理者人数分布")
    @GetMapping(value = "/manage/position/jobgrade", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public OrgProfileManagePosJobGradeVO getPositionJobgrade(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return orgProfileAppService.getPositionJobgrade(userCache.getOrgId(), yearly, deptId);
    }

    @Operation(summary = "干部质量（整体队伍水平&序列人才质量）")
    @GetMapping(value = "/manage/seriesrv", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public OrgProfileManageSeriesRvVO getManageSeriesrv(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return orgProfileAppService.getManageSeriesRv(userCache.getOrgId(), yearly, deptId);
    }

    @Operation(summary = "（干部结构-学历&年龄&司龄&性别）&（干部结构-序列人数分布）")
    @GetMapping(value = "/manage/deptbase", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public OrgProfileBasicClientVO getDeptManageBaseInfo(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        // 业务管理者(部门经理)查看部门的组织画像时,需要鉴权
        yearly = initParamYear(yearly);
        return orgProfileAppService.getManageDeptBaseInfo(
                userCache.getOrgId(), yearly, deptId, true);
    }

    @Operation(summary = "岗位胜任-组织胜任率")
    @GetMapping(value = "/rv/dept", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public CommonList<OrgProfileDeptRvVO> getDeptrv(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return new CommonList<>(
                orgProfileAppService.getDeptRv(userCache.getOrgId(), yearly, deptId));
    }

    @Operation(summary = "岗位胜任-序列列表")
    @GetMapping(value = "/rv/series", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public CommonList<OrgProfileRvSeriesVO> getRvSeries(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return new CommonList<>(
                orgProfileAppService.getRvSeries(userCache.getOrgId(), yearly, deptId));
    }

    @Operation(summary = "岗位胜任-胜任人数&达标率")
    @GetMapping(value = "/rv/series/detail", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public OrgProfileRvSeriesDmpVO getRvSeriesDetail(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String seriesId,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return orgProfileAppService.getRvSeriesDetail(
                userCache.getOrgId(), seriesId, deptId, yearly);
    }

//    /**
//     * @deprecated
//     * @param request
//     * @return
//     */
//    @Nullable
//    @Deprecated(since = "5.8")
//    @Operation(summary = "组织盘点-盘点项目涉及的维度信息")
//    @GetMapping(value = "/dims", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    public UserProfilePrjUserGridVO getPrjDim(HttpServletRequest request) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//        String orgId = userCache.getOrgId();
//        String queryOrgId = orgQryAppService.demoCopyOrgId(orgId);
//        log.info("LOG61950:组织画像-项目盘点-机构维度明细,orgId={}, queryOrgId={}", orgId,
//                queryOrgId);
//        return prjAppService.getPrjDim(queryOrgId);
//    }

//    /**
//     * @deprecated
//     * @param request
//     * @param query
//     * @return
//     */
//    @Deprecated(since = "5.8")
//    @Operation(summary = "组织盘点-盘点九宫格员工坐标信息")
//    @PostMapping(value = "/user/grids", consumes = MEDIATYPE, produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = TOKEN)
//    public List<UserDimGrid4Info> getPrjDimGrids(
//            HttpServletRequest request, @Validated @RequestBody OrgProfileUserDimGridQuery query) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//        String orgId = userCache.getOrgId();
//
//        // 演示机构的数据全部从模版机构中获取，所以这里做个替换，直接去查模版机构
//        String queryOrgId = orgQryAppService.demoCopyOrgId(orgId);
//        log.info("LOG61960:组织画像-项目盘点-员工维度九宫格,orgId={}, queryOrgId={}", orgId,
//                queryOrgId);
//        // 替换scopeDeptIds和scopeUserIds
//        if (!Objects.equals(orgId, queryOrgId)) {
//            List<String> queryScopeDeptIds =
//                    deptQryAppService.demoCopyDeptIds(orgId, queryOrgId, query.getScopeDeptIds());
//            List<String> queryScopeUserIds =
//                    userQryAppService.demoCopyUserIds(orgId, queryOrgId, query.getScopeUserIds());
//            log.debug("LOG65000:queryScopeDeptIds={}, queryScopeDeptIds={},", queryScopeDeptIds,
//                    queryScopeUserIds);
//            query.setScopeDeptIds(queryScopeDeptIds);
//            query.setScopeUserIds(queryScopeUserIds);
//        }
//
//        return orgProfileAppService.getUserDimGrids(queryOrgId, query);
//    }

    @Operation(summary = "首页-综合数据")
    @GetMapping(value = "/summary", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public OrgProfileSummaryVO summary(
            HttpServletRequest request,
            @RequestParam(defaultValue = "", required = false) String yearly,
            @RequestParam(defaultValue = "", required = false) String deptId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        yearly = initParamYear(yearly);
        return orgProfileAppService.getSummary(userCache.getOrgId(), yearly, deptId);
    }
}
