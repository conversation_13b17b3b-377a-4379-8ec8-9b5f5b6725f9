package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.yxt.talent.rv.controller.common.viewobj.BigDecimalRangeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会盘点人员详情")
public class CaliMeetUserResultVO {
    @Schema(description = "主键")
    private String id;

    @Schema(description = "用户Id")
    private String userId;

    @Schema(description = "用户名称")
    private String fullname;

    @Schema(description = "用户头像")
    private String imgUrl;

    @Schema(description = "用户部门")
    private String deptName;

    @Schema(description = "岗位名称")
    private String positionName;

    @Schema(description = "校准前维度等级Map<key,description> key为维度Id，description为维度等级")
    private Map<String, String> originalLevelMap = new HashMap<>(8);

    @Schema(description = "校准前维度分值Map<key,description> key为维度Id，description为维度分值")
    private Map<String, BigDecimal> originalScoreMap = new HashMap<>(8);

    @Schema(description = "校准后维度等级Map<key,description> key为维度Id，description为维度等级")
    private Map<String, String> calibrationLevelMap = new HashMap<>(8);

    @Schema(description = "校准后维度分值Map<key,description> key为维度Id，description为维度分值")
    private Map<String, BigDecimal> calibrationScoreMap = new HashMap<>(8);

    @Schema(description = "分值范围 Map<key,description> key为维度Id，description为分值范围（maxValue、minValue）")
    private Map<String, BigDecimalRangeVO> scoreRangeMap = new HashMap<>(8);

    @Schema(description = "工具类型 Map<key,description> key为维度Id，description为工具类型（1-绩效，2-测评，3-盘点数据导入）")
    private Map<String, Integer> toolTypeMap = new HashMap<>(8);

    @Schema(description = "发展建议")
    private String suggestion;

    private String userName;

    @Schema(description = "分级类型（0-枚举，1-百分比，2-绝对值）")
    private Integer classType;
}
