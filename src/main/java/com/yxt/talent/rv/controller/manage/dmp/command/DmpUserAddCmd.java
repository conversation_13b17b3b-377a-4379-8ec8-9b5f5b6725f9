package com.yxt.talent.rv.controller.manage.dmp.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import jakarta.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpUserAddCmd implements Command {

    @Schema(description = "用户id数组")
    @NotEmpty(message = "apis.sptalentrv.dmp.dmpUser.ids.notEmpty")
    private List<String> userIds;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

    @Schema(description = "动态匹配项目id")
    private String dmpId;

    public DmpUserAddCmd buildForAudit(String dmpId) {
        this.dmpId = dmpId;
        return this;
    }

}
