package com.yxt.talent.rv.controller.manage.calimeet;


import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.application.prj.dim.legacy.PrjDimConfAppService;
import com.yxt.talent.rv.application.prj.user.legacy.PrjUserAppService;
import com.yxt.talent.rv.application.xpd.grid.XpdGridAppManage;
import com.yxt.talent.rv.application.xpd.user.XpdUserAppService;
import com.yxt.talent.rv.controller.manage.prj.dim.viewobj.PrjUserDimConfVO;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjUserQuery;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserVO;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "建准会中涉及xpd的相关接口", description = "建准会中涉及xpd的相关接口")
@RequestMapping(value = "")
public class CaliMeetXpdController {

    private final AuthService authService;
    private final PrjUserAppService prjUserAppService;
    private final XpdGridAppManage xpdGridAppManage;
    private final XpdUserAppService xpdUserAppService;

    @Operation(summary = "查询盘点人员列表-维度动态表头")
    @Parameters({@Parameter(name = "projectId", description = "盘点项目id", required = true, in = ParameterIn.PATH)})
    @GetMapping(value = "/mgr/prjuser/{projectId}/dimension", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public CommonList<PrjUserDimConfVO> getProjectDimensionConfig(
        HttpServletRequest request,
        @PathVariable String projectId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return new CommonList<>(xpdGridAppManage.findDimensionHeader4Xpd(userCache.getOrgId(), projectId));
    }

    @SwaggerPageQuery
    @Operation(summary = "新盘点-查询盘点人员列表")
    @Parameters({@Parameter(name = "id", description = "校准会id", required = true, in = ParameterIn.PATH)})
    @PostMapping(value = "/mgr/prjuser/xpd/{projectId}/list", consumes = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public PagingList<PrjUserVO> queryXpdPrjUserPage(
        HttpServletRequest request, @PathVariable String projectId,
        @RequestBody PrjUserQuery search) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        return xpdUserAppService.findXpdPrjUserPage(userCache, pageRequest, projectId, search);
    }

}
