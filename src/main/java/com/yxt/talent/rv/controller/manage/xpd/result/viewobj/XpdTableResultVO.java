package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslate;
import com.yxt.talent.rv.infrastructure.service.remote.enums.SdDimName;
import com.yxt.talent.rv.infrastructure.service.remote.enums.SdIndicatorName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "表格视图(维度分层/人才分层)")
public class XpdTableResultVO extends UserBaseInfo {

    @Schema(description = "维度id")
    private String sdDimId;

    @SdDimName(id = "sdDimId", i18n = "sdDimNameI18n")
    @I18nTranslate(codeField = "sdDimNameI18n")
    @Schema(description = "维度名称")
    private String sdDimName;

    @Schema(description = "维度名称国际化code", hidden = true)
    private String sdDimNameI18n;

    @Schema(description = "是否为内置的绩效维度")
    private boolean perfDim;

    @Schema(description = "维度/人才分层id")
    private String levelId;

    @I18nTranslate(codeField = "levelNameI18n")
    @Schema(description = "维度/人才分层名称")
    private String levelName;

    @Schema(description = "维度/人才分层名称国际化code", hidden = true)
    private String levelNameI18n;

    @Schema(description = "结果类型:0-分值 1-达标率")
    private Integer resultType;

    @Schema(description = "维度/项目结果, 可能是字符串，也可能是浮点数值，前端渲染时需注意")
    private Object result;

    @Schema(description = "维度/指标id")
    private String subId;

    @SdIndicatorName(id = "subId", i18n = "subNameI18n")
    @I18nTranslate(codeField = "subNameI18n")
    @Schema(description = "维度/指标名称")
    private String subName;

    @Schema(description = "维度/指标名称国际化code", hidden = true)
    private String subNameI18n;

    @Schema(description = "维度/项目结果")
    private String subResult;

}
