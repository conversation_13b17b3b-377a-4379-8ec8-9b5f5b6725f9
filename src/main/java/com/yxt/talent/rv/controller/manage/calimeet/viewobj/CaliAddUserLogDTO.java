package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/4 16:59
 **/
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliAddUserLogDTO {

    @AuditLogField(name = "盘点人员", orderIndex = 1)
    private String user;

}
