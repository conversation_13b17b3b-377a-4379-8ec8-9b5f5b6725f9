package com.yxt.talent.rv.controller.manage.calimeet;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.modelhub.api.bean.dto.ImportRespDTO;
import com.yxt.talent.rv.application.calimeet.CaliMeetImportService;
import com.yxt.talent.rv.application.calimeet.dto.CaliDimAndLevelImportResultVO;
import com.yxt.talent.rv.application.calimeet.dto.ImportUserDTO;
import com.yxt.talent.rv.application.calimeet.dto.RvCalibrationPerson4Import;
import com.yxt.talent.rv.application.calimeet.impt.CaliDimImporter;
import com.yxt.talent.rv.application.calimeet.impt.CaliDimLevelImporter;
import com.yxt.talent.rv.application.calimeet.impt.CaliIndicatorImporter;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.CaliMeetTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequestMapping("/mgr/calimport")
@RequiredArgsConstructor
@Tag(name = "导入校准结果", description = "新盘点项目-管理端-导入校准结果")
public class CalImportController {

    private final AuthService authService;
    private final CaliMeetImportService caliMeetImportService;
    private final CaliIndicatorImporter caliIndicatorImporter;
    private final CaliDimImporter caliDimImporter;
    private final CaliDimLevelImporter caliDimLevelImporter;
    private final CalimeetMapper calimeetMapper;

    @Operation(summary = "校准人员导入")
    @PostMapping(value = "/import", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public CaliDimAndLevelImportResultVO importData(
        HttpServletRequest request, @RequestBody FileImportCmd bean,
        @RequestParam(value = "file", required = false) MultipartFile file) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(bean.getTargetId(), userCache.getOrgId());
        if (calimeetPO == null){
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXIST);
        }
        if (calimeetPO.getCalimeetType() == CaliMeetTypeEnum.LEVEL.getCode()){
           return caliDimLevelImporter.toImport(bean, file, userCache);
        }
        if (calimeetPO.getCalimeetType() == CaliMeetTypeEnum.DIM.getCode()){
            return caliDimImporter.toImport(bean, file, userCache);
        }
        if (calimeetPO.getCalimeetType() == CaliMeetTypeEnum.INDICATOR.getCode()){
            return caliIndicatorImporter.toImport(bean, file, userCache);
        }
        return new CaliDimAndLevelImportResultVO();
    }

    @ResponseStatus(OK)
    @Auth(codes = {AUTH_CODE_ALL})
    @Operation(summary = "导入校准结果-导入模板")
    @PostMapping(value = "/tem/{calId}")
    public void downloadTem(HttpServletRequest request, @PathVariable String calId, @RequestBody ImportUserDTO importUsers) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(calId, currentUser.getOrgId());
        if (calimeetPO == null){
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXIST);
        }
        if (calimeetPO.getCalimeetType() == CaliMeetTypeEnum.LEVEL.getCode()){
            caliMeetImportService.downloadTemDimLevel(currentUser, calId, importUsers);
        }
        if (calimeetPO.getCalimeetType() == CaliMeetTypeEnum.DIM.getCode()){
            caliMeetImportService.downloadTemDim(currentUser, calId, importUsers);
        }
        if (calimeetPO.getCalimeetType() == CaliMeetTypeEnum.INDICATOR.getCode()){
            caliMeetImportService.downloadTemIndicator(currentUser, calId, importUsers);
        }
    }

//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    @Operation(summary = "导入校准结果-维度结果-维度结果-导入模板")
//    @GetMapping(value = "/tem/{calId}/dim")
//    public void downloadTemDim(HttpServletRequest request, @PathVariable String calId) {
//        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
//        caliMeetImportService.downloadTemDim(currentUser, calId);
//    }
//
//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    @Operation(summary = "导入校准结果-维度结果-维度分层结果-导入模板")
//    @GetMapping(value = "/tem/{calId}/dimlevel")
//    public void downloadTemDimLevel(HttpServletRequest request, @PathVariable String calId) {
//        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
//        caliMeetImportService.downloadTemDimLevel(currentUser, calId);
//    }
//
//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    @Operation(summary = "导入校准结果-维度结果-维度指标结果-导入模板")
//    @GetMapping(value = "/tem/{calId}/dimindicator")
//    public void downloadTemIndicator(HttpServletRequest request, @PathVariable String calId) {
//        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
//        caliMeetImportService.downloadTemIndicator(currentUser, calId);
//    }
//
//    @Nullable
//    @Operation(summary = "导入校准结果-维度结果-维度结果")
//    @PostMapping(value = "/import/dim")
//    @Auth(codes = {AUTH_CODE_ALL})
//    @ResponseStatus(OK)
//    public CaliDimAndLevelImportResultVO importDim(
//        HttpServletRequest request, @RequestBody FileImportCmd bean,
//        @RequestParam(value = "file", required = false) MultipartFile file) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//        return caliDimImporter.toImport(bean, file, userCache);
//    }
//
//    @Nullable
//    @Operation(summary = "导入校准结果-维度结果-维度分层结果")
//    @PostMapping(value = "/import/dimlevel")
//    @Auth(codes = {AUTH_CODE_ALL})
//    @ResponseStatus(OK)
//    public CaliDimAndLevelImportResultVO importDimLevel(
//        HttpServletRequest request, @RequestBody  FileImportCmd bean,
//        @RequestParam(value = "file", required = false) MultipartFile file) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//        return caliDimLevelImporter.toImport(bean, file, userCache);
//    }
//
//    @Nullable
//    @Operation(summary = "导入校准结果-维度结果-维度指标结果")
//    @PostMapping(value = "/import/dimindicator")
//    @Auth(codes = {AUTH_CODE_ALL})
//    @ResponseStatus(OK)
//    public CaliDimAndLevelImportResultVO importDimIndicator(
//        HttpServletRequest request, @RequestBody FileImportCmd bean,
//        @RequestParam(value = "file", required = false) MultipartFile file) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//        return caliIndicatorImporter.toImport(bean, file, userCache);
//    }

}
