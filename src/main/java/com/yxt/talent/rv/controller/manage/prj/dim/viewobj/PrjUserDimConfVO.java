package com.yxt.talent.rv.controller.manage.prj.dim.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PrjUserDimConfVO {

    @Schema(description = "盘点维度ID")
    private String dimensionId;

    @Schema(description = "盘点维度名称")
    private String dimensionName;
}
