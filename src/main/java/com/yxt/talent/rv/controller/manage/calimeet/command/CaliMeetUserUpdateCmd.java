package com.yxt.talent.rv.controller.manage.calimeet.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "盘点人员校准")
public class CaliMeetUserUpdateCmd implements Command {

    @Schema(description = "主键Id")
    private String id;

    @Schema(description = "盘点维度(1-维度等级,2-维度评分)")
    private int dimensionType;

    @Schema(description = "校准后Map<key,description> key为维度Id，description为维度等级(1-低，2-中，3-高)")
    private Map<String, Integer> calibrationLevel = new HashMap<>(8);

    @Schema(description = "校准后Map<key,description> key为维度Id，description为维度分值或绩效枚举值(如 S+ = 1、S = 2、S- = 3 等等，具体参考枚举表)")
    private Map<String, BigDecimal> calibrationScore = new HashMap<>(8);

    @Size(max = 800, message = "apis.sptalentrv.calimeet.user.suggestion.size")
    @Schema(description = "发展建议")
    private String suggestion;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
