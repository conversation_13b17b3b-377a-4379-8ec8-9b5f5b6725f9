package com.yxt.talent.rv.controller.manage.xpd.log.viewobj;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class XpdPoolLogDTO {

    @AuditLogField(name = "人才池", orderIndex = 1)
    private String poolName;
    @AuditLogField(name = "盘点人员", orderIndex = 2)
    private String users;
}
