package com.yxt.talent.rv.controller.manage.xpd.user.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 个人报告，维度指标结果
 *
 * @date 2024/12/18
 */
@Data
public class XpdDimIndicator4ViewVO {

    private String dimId;

    @Schema(description = "维度名称,包含当前维度和之前")
    private String dimName;

    @Schema(description = "项目选择的维度id")
    private String headDimId;

    @Schema(description = "id")
    private String id;
    @Schema(description = "维度id")
    private String dmId;
    @Schema(description = "维度名称")
    private String dmName;
    @Nullable
    @Schema(description = "自定义维度名称")
    private String customName;
    @Schema(description = "父维度id")
    private String parentId;
    @Schema(description = "级别类型，0-父级，1-末级")
    private Integer levelType;

    @Schema(description = "类型，0-普通、1-能力、2-技能、3-知识、4-任务")
    private Integer indicatorType;
    /**
     * 维度类型:0-普通/1-能力/2-技能/3-知识/4-任务/5-绩效
     */
    /*@Schema(description = "维度类型:0-普通/1-能力/2-技能/3-知识/4-任务/5-绩效")
    private Integer dimType;*/

    private Integer dmType;
    private Integer requireType;

    @Schema(description = "0 非导入维度，1 维度结果导入， 2维度指标导入")
    private Integer importType = 0;

    @Schema(description = "子维度信息")
    @JsonProperty("children")
    private List<XpdDimIndicator4ViewVO> childs;

    @Schema(description = "指标以及打点信息", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RadarChartPoint4MutiDimGet> details;

}
