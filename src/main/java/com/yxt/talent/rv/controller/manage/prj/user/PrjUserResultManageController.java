package com.yxt.talent.rv.controller.manage.prj.user;

import com.yxt.common.service.AuthService;
import com.yxt.talent.rv.application.prj.user.expt.PrjUserResultExporter;
import com.yxt.talent.rv.application.prj.user.legacy.PrjUserResultAppService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @deprecated since 5.8 老的人盘点项目已废弃，现在叫xpd项目
 */
@Slf4j
@RestController
@Deprecated(since = "5.8")
@RequiredArgsConstructor
@Tag(name = "盘点项目跟踪", description = "盘点项目跟踪")
@RequestMapping(value = "/mgr/prjresult")
public class PrjUserResultManageController {
    private final AuthService authService;
    private final PrjUserResultAppService prjUserResultAppService;
    private final PrjUserResultExporter prjUserResultExporter;

//    @Operation(summary = "盘点人员结果导出")
//    @GetMapping(value = "/{projectId}/list/export", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = PRJ_RESULT_EXPORT)
//    @Auditing
//    @AuditingPlus(strategyClass = PrjCalcResultExportAuditLogStrategy.class)
//    public GenericFileExportVO calcResultExport(
//        HttpServletRequest request, @PathVariable String projectId) {
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        return prjUserResultExporter.export(projectId, operator);
//    }
//
//    @SwaggerPageQuery
//    @Operation(summary = "盘点结果查询（九宫格坐标）")
//    @PostMapping(value = "/page", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = TOKEN)
//    public PagingList<PrjUserResultVO> findPage(
//        HttpServletRequest request, @RequestBody PrjGridScopeAuthQuery search) {
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        // 当前用户信息
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        Page<PrjUserResultVO> requestPage =
//            new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
//        search.setOpenAuth(false);
//        return prjUserResultAppService.findByXyAxis(operator.getOrgId(), search, requestPage, operator.getLocale());
//    }
//
//    @Operation(summary = "盘点结果查询（九宫格初始化）3")
//    @PostMapping(value = "/palaces", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public List<PrjUserResultVO> palaces(
//        HttpServletRequest request, @RequestBody PrjGridScopeAuthQuery search) {
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        // 当前用户信息
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        // 将palaces 写死的 50 外移至此处
//        pageRequest.setSize(50);
//        search.setOpenAuth(false);
//        return prjUserResultAppService.palaces(operator.getOrgId(), search, pageRequest, operator.getLocale());
//    }
//
//    @Operation(summary = "查询项目下人员的在各个维度下初始盘点等级结果")
//    @GetMapping(value = "/{projectId}/{userId}/result", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = TOKEN)
//    public PrjUserDimResultVO getUserResult(
//        HttpServletRequest request, @PathVariable String projectId, @PathVariable String userId) {
//        // 当前用户信息
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        return prjUserResultAppService.getUserResult(operator.getOrgId(), projectId, userId, operator.getLocale());
//    }
}
