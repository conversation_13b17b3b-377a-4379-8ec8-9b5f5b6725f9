package com.yxt.talent.rv.controller.manage.dmp.query;

import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpUser4Query {
    @Schema(description = "账户状态: 0-禁用 1-启用 2-已删除")
    private Integer udpStatus = -1;

    public String getEscapedSearchKey() {
        return SqlUtil.escapeSql(searchKey);
    }

    @Schema(description = "搜索关键字，支持姓名和账号")
    private String searchKey;

    private List<String> userIds;
}
