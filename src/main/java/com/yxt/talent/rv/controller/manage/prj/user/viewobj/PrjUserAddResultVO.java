package com.yxt.talent.rv.controller.manage.prj.user.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 盘点人员新增结果
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PrjUserAddResultVO {
    @Schema(description = "成功新增人员数量")
    private long count = 0;

    @Schema(description = "总人员数量")
    private long totalCount = 0;

    @Schema(description = "如果包含他评方案，则返回名称，页面弹框展示，请设置评估关系")
    private List<String> peerEvalNames = new ArrayList<>();

    @Schema(description = "如果包含收费问卷，且问卷余额不足，返回收费问卷名称")
    private String chargeEvalName;

    @Schema(description = "如果包含收费问卷，且问卷余额不足，返回收费问卷添加前余额度")
    private Integer marginCount;

    private List<String> successUserIds;
}
