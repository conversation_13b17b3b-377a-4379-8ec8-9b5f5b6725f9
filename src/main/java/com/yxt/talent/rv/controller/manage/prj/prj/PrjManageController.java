package com.yxt.talent.rv.controller.manage.prj.prj;

import com.yxt.common.service.AuthService;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjAppManage;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjAppService;
import com.yxt.talent.rv.application.xpd.xpd.XpdAppService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @deprecated since 5.8 老的人盘点项目已废弃，现在叫xpd项目
 */
@Slf4j
@RestController
@Deprecated(since = "5.8")
@RequiredArgsConstructor
@Tag(name = "盘点项目管理", description = "盘点项目管理")
@RequestMapping(value = "/mgr/project")
public class PrjManageController {

//    @SwaggerPageQuery
//    @Operation(summary = "盘点项目列表列表")
//    @PostMapping(value = "/search", consumes = MEDIATYPE, produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    public PagingList<PrjListVO> search(
//            HttpServletRequest request, @RequestBody PrjQuery search) {
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        search.setOrgId(operator.getOrgId());
//        search.setUserId(operator.getUserId());
//        return prjAppService.search(pageRequest, search, operator.getLocale());
//    }

//    迁移到 com.yxt.talent.rv.controller.manage.xpd.user.XpdUserManageController.findAllList
//    @Operation(summary = "盘点项目列表-无分页")
//    @PostMapping(value = "/list/search", consumes = MEDIATYPE, produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    public CommonList<PrjListVO> findAllList(
//            HttpServletRequest request, @RequestBody PrjQuery search) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
////        List<PrjListVO> allList = prjAppService.findAllList(userCache.getOrgId(), search, userCache.getLocale());
//        List<XpdVO> xpds = xpdAppService.search(userCache.getOrgId(), new XpdQuery(search));
//        return new CommonList<>(toXpdListVO(xpds));
//    }
//
//    private List<PrjListVO> toXpdListVO(List<XpdVO> xpds) {
//        return xpds.stream().map(xpd -> {
//            PrjListVO prjListVO = new PrjListVO();
//            prjListVO.setId(xpd.getId());
//            prjListVO.setProjectName(xpd.getXpdName());
//            return prjListVO;
//        }).toList();
//    }

//    @Operation(summary = "创建盘点项目-基本信息")
//    @PostMapping(value = "", consumes = MEDIATYPE)
//    @ResponseStatus(CREATED)
//    @Auth(codes = PRJ_LIST_ADD)
//    @NonDuplicateRequest("'createproject:'+#prjCreateCmd.projectName")
//    @Auditing
//    @EasyAuditLog(value = RvAuditLogConstants.PRJ_CREATE, paramExp = "#prjCreateCmd")
//    //@AuditingPlus(strategyClass = PrjBasicAuditLogStrategy.class)
//    public ResponseEntity<String> create(
//            HttpServletRequest request, @Valid @RequestBody PrjCreateCmd prjCreateCmd) {
//        UserCacheBasic userCache = authService.getUserCacheBasic();
//        String orgId = userCache.getOrgId();
//        String userId = userCache.getUserId();
////        if (!factorService.isCreatePrj(orgId)) {
////            throw new ApiException("global.no.privilege");
////        }
//        String id = prjAppService.createPrj(prjCreateCmd, orgId, userId);
//        if (StringUtils.isEmpty(prjCreateCmd.getCloundId())) {
//            prjAppManage.initAndSavePrjDims(id, userId, orgId);
//        }
//        return ApiUtil.createdResponse(request, id);
//    }
//
//    @Operation(summary = "修改盘点项目-基本信息")
//    @PutMapping(value = "", consumes = MEDIATYPE)
//    @ResponseStatus(NO_CONTENT)
//    @Auth(type = TOKEN, codes = PRJ_LIST_EDIT)
//    @NonDuplicateRequest("'updateproject:'+#prjUpdateCmd.projectName")
//    @Auditing
//    //@AuditingPlus(strategyClass = PrjBasicAuditLogStrategy.class)
//    @EasyAuditLog(value = RvAuditLogConstants.PRJ_UPDATE, paramExp = "#prjUpdateCmd")
//    public void create(
//            HttpServletRequest request, @Valid @RequestBody PrjUpdateCmd prjUpdateCmd) {
//        UserCacheBasic userCache = authService.getUserCacheBasic();
//        prjAppService.updateProject(prjUpdateCmd, userCache.getOrgId(), userCache.getUserId());
//    }
//
//    @Operation(summary = "盘点项目名称校验")
//    @PostMapping(value = "/validate", consumes = MEDIATYPE)
//    @ResponseStatus(NO_CONTENT)
//    @Auth(codes = PRJ_LIST_EDIT)
//    public void validate(
//            HttpServletRequest request,
//            @Valid @RequestBody ProjectValidateQuery projectValidateQuery) {
//        UserCacheBasic userCache = authService.getUserCacheBasic();
//        prjAppService.checkProjectName(userCache.getOrgId(), projectValidateQuery.getProjectId(),
//                projectValidateQuery.getProjectName());
//    }
//
//    @Operation(summary = "盘点项目-基本信息")
//    @GetMapping(value = "/{projectId}", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public PrjDetailVO projectInfo(HttpServletRequest request, @PathVariable String projectId) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//        return prjAppManage.getProjectDetail(userCache.getOrgId(), projectId, userCache.getLocale());
//    }
//
//    @Operation(summary = "修改项目状态")
//    @Parameter(name = "status", description = "项目状态（0-未发布草稿，1-未开始，2-进行中，3-已结束）", in = ParameterIn.QUERY)
//    @PutMapping(value = "/{projectId}/status")
//    @ResponseStatus(OK)
//    @Auth(codes = {PRJ_LIST_START, PRJ_LIST_END, PRJ_START, PRJ_END})
//    @Auditing
//    //@AuditingPlus(strategyClass = PrjStatusAuditLogStrategy.class)
//    @EasyAuditLog(value = RvAuditLogConstants.PRJ_STATUS_UPDATE, paramExp = "#projectId")
//    public PrjUpdateStatusVO projectStatus(
//            HttpServletRequest request, @PathVariable String projectId, @RequestParam int status) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//
//        return prjAppManage.projectStatus(
//                userCache, ApiUtil.getToken(request), projectId, status, request);
//    }
//
//    @Operation(summary = "删除项目")
//    @DeleteMapping(value = "/{projectId}")
//    @ResponseStatus(NO_CONTENT)
//    @Auth(codes = PRJ_LIST_DEL)
//    @Auditing
//    //@AuditingPlus(strategyClass = PrjDelAuditLogStrategy.class)
//    @EasyAuditLog(value = RvAuditLogConstants.PRJ_DELETE, paramExp = "#projectId")
//    public void deleteProject(HttpServletRequest request, @PathVariable String projectId) {
//        UserCacheDetail userCache = authService.getUserCacheDetail(request);
//        prjAppService.deleteProject(userCache.getOrgId(), projectId, userCache.getUserId());
//    }
//
//    @Operation(summary = "盘点项目-盘点详情信息(九宫格)")
//    @GetMapping(value = "/info/{id}", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public PrjDetailInfoVO projectDetail(HttpServletRequest request, @PathVariable String id) {
//        UserCacheBasic userCache = authService.getUserCacheBasic();
//        return prjAppManage.getProjectDetailInfo(userCache.getOrgId(), id);
//    }
//
//    @Operation(summary = "盘点项目-项目基本信息")
//    @GetMapping(value = "/{projectId}/info", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public PrjListVO getProjectInfo(HttpServletRequest request, @PathVariable String projectId) {
//        UserCacheBasic userCache = authService.getUserCacheBasic();
//        PrjPO prj = prjAppService.getProjectById(projectId, userCache.getOrgId());
//        PrjListVO singleProject = new PrjListVO();
//        BeanHelper.copyProperties(prj, singleProject);
//        if (1 == singleProject.getProjectType()) {
//            // 返回云端方案id和名称
//            CloudPrjPO cloudPrj =
//                    prjAppService.getCloudProject(userCache.getOrgId(), singleProject.getId());
//            if (null != cloudPrj) {
//                singleProject.setCloundId(cloudPrj.getCloundId());
//                singleProject.setCloundName(cloudPrj.getProjectName());
//            }
//        }
//        return singleProject;
//    }
//
//    @Operation(summary = "盘点项目-查询云端方案列表信息")
//    @GetMapping(value = "/projects", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public CommonList<RvProject4GetFacade> getCloudPro(HttpServletRequest request) {
//        UserCacheBasic operator = authService.getUserCacheBasic(request);
//        return new CommonList<>(prjAppManage.getCloudProjectList(operator.getOrgId()));
//    }
//
//    @Operation(summary = "获取余额信息")
//    @PostMapping(value = "/evaltool", consumes = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public CommonList<Package4Get> evalToolList(
//            HttpServletRequest request, @RequestBody PrjDimRuleTool4Query prjDimRuleTool4Query) {
//        UserCacheBasic operator = authService.getUserCacheBasic(request);
//        return new CommonList<>(
//                prjAppManage.getAvailableQuantity(operator.getOrgId(), prjDimRuleTool4Query.getToolIds()));
//    }
//
//    @Nullable
//    @Operation(summary = "查看云端方案介绍")
//    @Parameter(name = "cloundId", description = "云端项目id", in = ParameterIn.PATH)
//    @GetMapping(value = "/view/{cloudId}")
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public RvProjectDetail4Facade view(HttpServletRequest request, @PathVariable String cloudId) {
//        return prjAppManage.getCloudProjectDetail(cloudId);
//    }
//
//    @Operation(summary = "查看云端方案介绍(机构侧)")
//    @Parameter(name = "projectId", description = "机构侧盘点项目id", in = ParameterIn.PATH)
//    @GetMapping(value = "/view/local/{projectId}")
//    @ResponseStatus(OK)
//    @Auth(codes = AUTH_CODE_ALL)
//    public RvProjectDetail4Facade viewLocal(
//            HttpServletRequest request, @PathVariable String projectId) {
//        UserCacheBasic operator = authService.getUserCacheBasic(request);
//        return prjAppManage.viewLocalDetail(operator.getOrgId(), projectId);
//    }
//
//
//    @Operation(summary = "批量更新盘点项目九宫格标签")
//    @PostMapping(value = "/update/labels", consumes = MEDIATYPE)
//    @ResponseStatus(NO_CONTENT)
//    @Auth(codes = PRJ_LIST_EDIT)
//    @Auditing
//    //@AuditingPlus(strategyClass = PrjGridAuditLogStrategy.class)
//    @EasyAuditLog(value = RvAuditLogConstants.PRJ_EDIT_LABELS, paramExp = "#labels4Update")
//    public void updateLabels(
//            HttpServletRequest request, @Valid @RequestBody PrjLabelUpdateCmd labels4Update) {
//        UserCacheBasic userCache = authService.getUserCacheBasic(request);
//        prjAppManage.updateLabels(
//                userCache.getOrgId(), userCache.getUserId(), labels4Update.getLabels());
//    }
//
//    @Operation(summary = "用户盘点轨迹")
//    @GetMapping(value = "/{userId}/trajectory", produces = MEDIATYPE)
//    @Parameter(name = "userId", description = "用户id", in = ParameterIn.PATH)
//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    public CommonList<PrjUserResultTrackVO> userProjectTrajectory(
//            HttpServletRequest request, @PathVariable String userId) {
//        UserCacheBasic userCache = authService.getUserCacheBasic(request);
//        List<PrjUserResultTrackVO> list =
//                prjAppService.userProjectTrajectories(userCache.getOrgId(), userId);
//        return new CommonList<>(list);
//    }
//
//    @Operation(summary = "用户盘点轨迹详情")
//    @GetMapping(value = "/{userId}/trajectory/detail", produces = MEDIATYPE)
//    @Parameters({@Parameter(name = "userId", description = "用户id", in = ParameterIn.PATH, required = true), @Parameter(name = "projectId", description = "项目id", in = ParameterIn.QUERY, required = true)})
//    @ResponseStatus(OK)
//    @Auth(codes = {AUTH_CODE_ALL})
//    public CommonList<PrjUserDimLevelVO> userProjectTrajectoryDetail(
//            HttpServletRequest request, @PathVariable String userId,
//            @RequestParam String projectId) {
//        UserCacheBasic userCache = authService.getUserCacheBasic(request);
//        List<PrjUserDimLevelVO> list =
//                prjAppService.userProjectTrajectoryDetail(userCache.getOrgId(), userId, projectId);
//        return new CommonList<>(list);
//    }
}
