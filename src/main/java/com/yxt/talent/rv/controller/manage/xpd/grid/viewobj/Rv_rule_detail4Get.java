package com.yxt.talent.rv.controller.manage.xpd.grid.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
@Getter
@Setter
@Schema(description = "项目详细规则引用")
public class Rv_rule_detail4Get implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="计算方式")
    private String calctype;
    @Schema(description="分层优先级")
    private String levelpriority;
    @Schema(description="结果类型")
    private String resulttype;
    @Schema(description="分层方式")
    private String leveltype;
    @Schema(description="规则说明")
    private String ruledesc;
    @Schema(description="计算规则")
    private String calcrule;
    @Schema(description="父实体id;子实体选择的父实体id或者引用对象的实体id")
    private String rvRulesId;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="名称")
    private String name;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="父实体id展示信息")
    @JsonProperty("@rvRulesId")
    private Rv_rules4Get rvRulesId__Record;
}
