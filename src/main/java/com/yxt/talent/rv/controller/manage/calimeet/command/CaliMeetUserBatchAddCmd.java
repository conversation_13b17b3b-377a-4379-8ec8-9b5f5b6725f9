package com.yxt.talent.rv.controller.manage.calimeet.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "校准会人员列表-添加盘点人员")
public class CaliMeetUserBatchAddCmd implements Command {

    @Schema(description = "校准会Id")
    private String meetingId;

    @Schema(description = "用户Id列表")
    @NotNull(message = "apis.sptalentrv.calimeet.user.userIdList.notNull")
    private List<String> userIdList;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
