package com.yxt.talent.rv.application.xpd.aom.slot;

import com.yxt.aom.base.bean.md.AomActivityPartMember4Get;
import com.yxt.aom.base.bean.md.AomDrawer4RespDTO;
import com.yxt.aom.base.bean.md.AomUserStatisticsBean;
import com.yxt.aom.base.custom.CustomActivityPartMemberCompo;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.application.xpd.common.dto.PartMemberList4Get;
import com.yxt.talent.rv.application.xpd.common.dto.XpdResultUserLevelDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component("customActivityPartMemberCompo4ProjRcpd")
public class XpdActivityPartMemberCompo implements CustomActivityPartMemberCompo {

    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdService xpdService;

    @Override
    public List<AomActivityPartMember4Get> listActvPartMember(List<AomActivityPartMember4Get> resultList) {

        if (CollectionUtils.isEmpty(resultList)){
            return resultList;
        }

        String actvId = resultList.get(0).getActvId();
        String orgId = resultList.get(0).getOrgId();
        XpdPO xpdPO = xpdService.findXpdByAomId(orgId, actvId);
        if (xpdPO == null){
            return resultList;
        }

        List<AomDrawer4RespDTO> userDtos = resultList.stream().map(AomActivityPartMember4Get::getUserId).collect(
            Collectors.toList());
        List<String> userIds = new ArrayList<>();
        userDtos.forEach(userDto -> {
            if (CollectionUtils.isNotEmpty(userDto.getDatas())){
                for (Object data : userDto.getDatas()) {
                    AmUser4DTO.UserInfo userData = (AmUser4DTO.UserInfo) data;
                    userIds.add(userData.getId());
                }
            }
        });

        Map<String, XpdResultUserLevelDTO> userLevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)){
            List<XpdResultUserLevelDTO> list = xpdResultUserMapper.findLevelByUserId(orgId, xpdPO.getId(), userIds);
            userLevelMap = StreamUtil.list2map(list, XpdResultUserLevelDTO::getUserId);
        }

        List<AomActivityPartMember4Get> convertList = new ArrayList<>(resultList.size());
        Map<String, XpdResultUserLevelDTO> finalUserLevelMap = userLevelMap;
        resultList.forEach(actvPartMember4Get -> {
            PartMemberList4Get uinfo = new PartMemberList4Get();
            BeanCopierUtil.copy(actvPartMember4Get, uinfo);
            AomDrawer4RespDTO user = uinfo.getUserId();

            // 提取学习
            if (actvPartMember4Get.getMemberStatistics() != null && CollectionUtils.isNotEmpty(actvPartMember4Get.getMemberStatistics().getDatas())){
                AomUserStatisticsBean statisticsBean = (AomUserStatisticsBean) actvPartMember4Get.getMemberStatistics().getDatas().get(0);
                BigDecimal actCompletedRate = statisticsBean.getActCompletedRate();
                uinfo.setRvCompleteRate(Optional.of(actCompletedRate)
                    .orElse(BigDecimal.ZERO)
                    .setScale(0, BigDecimal.ROUND_HALF_UP)
                    .intValue());
            }

            if (user != null && CollectionUtils.isNotEmpty(user.getDatas())){
                AmUser4DTO.UserInfo userData = (AmUser4DTO.UserInfo)user.getDatas().get(0);
                String userId = userData.getId();
                if (finalUserLevelMap.containsKey(userId)){
                    XpdResultUserLevelDTO xpdResultUserLevelDTO = finalUserLevelMap.get(userId);
                    uinfo.setRvResult(xpdResultUserLevelDTO.getLevelName());
                }
            }
            convertList.add(uinfo);
        });

        return convertList;
    }
}
