package com.yxt.talent.rv.application.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.ApplicationCommandService;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.xpd.common.enums.GridTypeEnum;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetTaskRecordUpdateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetLargeChangePersonnelQry;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGeneralOverviewVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetLargeChangePersonnelVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGridItemVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetTaskRecordVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.AttachmentMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserInfoFiller;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil.null2zero;

@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class CaliMeetOverviewAppService {

    private final CalimeetMapper calimeetMapper;
    private final AttachmentMapper attachmentMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final XpdGridMapper xpdGridMapper;
    private final UserInfoFiller userInfoFiller;
    private final I18nTranslator i18nTranslator;
    private final AuthService authService;
    private final XpdMapper xpdMapper;

    /**
     * 获取校准会概览统计信息
     *
     * @param caliMeetId 校准会ID
     * @param orgId
     * @return 校准会概览统计信息
     */
    public CaliMeetGeneralOverviewVO getGeneralOverview(@NotNull String caliMeetId, String orgId) {
        log.info("获取校准会概览统计信息, caliMeetId:{}", caliMeetId);
        // 1. 获取校准会信息
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeetPO, ExceptionKeys.CALI_MEET_NOT_EXISTED);

        // 2. 查询需校准人数（从校准会用户表获取总人数）
        CaliMeetGeneralOverviewVO result = calimeetUserMapper.selectOverviewStatistic(orgId, caliMeetId);

        // 3. 获取校准幅度较大人数：校准幅度较大定义：横向校准幅度和纵向校准幅度相加>2的人数，根据宫格类型展示不同信息：四宫格为>1，十六宫格为>3
        // 查询校准会关联的xpd项目的宫格配置，决定校准幅度较大人数的计算方式
        XpdGridPO xpdGridPO = xpdGridMapper.selectByXpdId(orgId, calimeetPO.getXpdId());
        Validate.isNotNull(xpdGridPO, ExceptionKeys.XPD_GRID_NOT_FOUND);
        int threshold = resolveThreshold(xpdGridPO);
        int largeChangePeopleNumber = calimeetUserMapper.countLargeChangePeopleNumber(orgId, caliMeetId, threshold);
        result.setLargeChangePeopleNumber(largeChangePeopleNumber);
        return result;
    }

    private static int resolveThreshold(XpdGridPO xpdGridPO) {
        int threshold = 2; // 九宫格
        if (GridTypeEnum.four(xpdGridPO.getGridType())) { // 四宫格
            threshold = 1;
        } else if (GridTypeEnum.sixteen(xpdGridPO.getGridType())) { // 十六宫格
            threshold = 3;
        }
        return threshold;
    }

    /**
     * 获取校准前后对比数据
     *
     * @param caliMeetId 校准会ID
     * @param dimCombId  维度组合Key
     * @param orgId
     * @return 校准前后对比数据
     */
    public List<CaliMeetGridItemVO> getComparisonData(@NotNull String caliMeetId, String dimCombId, String orgId) {
        log.info("获取校准前后对比数据, caliMeetId:{}, dimCombId:{}, orgId:{}", caliMeetId, dimCombId, orgId);
        
        // 1. 校验校准会是否存在
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeetPO, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        
        // 校验维度组合ID是否为空
        Validate.isNotBlank(dimCombId, ExceptionKeys.XPD_DIM_COMB_ID_EMPTY, "维度组合ID不能为空");
        
        try {
            // 2. 获取校准前九宫格分布数据
            List<CaliMeetGridItemVO> beforeGrids =
                calimeetUserMapper.getBeforeCalibrationDistribution(orgId, caliMeetId, calimeetPO.getXpdId(), dimCombId);

            // 3. 获取校准后九宫格分布数据
            List<CaliMeetGridItemVO> afterGrids =
                calimeetUserMapper.getAfterCalibrationDistribution(orgId, caliMeetId, calimeetPO.getXpdId(), dimCombId);

            // 4. 计算格子人数变化 和 格子人数变化占比
            for (CaliMeetGridItemVO beforeGrid : beforeGrids) {
                for (CaliMeetGridItemVO afterGrid : afterGrids) {
                    if (Objects.equals(beforeGrid.getCellIndex(), afterGrid.getCellIndex())) {
                        int beforeCount = null2zero(beforeGrid.getUserCount());
                        int afterCount = null2zero(afterGrid.getUserCount());
                        afterGrid.setChangeCount(afterCount - beforeCount);

                        // 计算变化占比
                        BigDecimal beforeUserRatio = null2zero(beforeGrid.getUserRatio());
                        BigDecimal afterUserRatio = null2zero(afterGrid.getUserRatio());
                        afterGrid.setChangeRatio(afterUserRatio.subtract(beforeUserRatio));
                    }
                }
            }

            
            log.info("校准前后对比数据获取成功, caliMeetId:{}", caliMeetId);
            return afterGrids;
        } catch (Exception e) {
            log.error("获取校准前后对比数据失败, caliMeetId:{}, dimCombId:{}, error:{}", caliMeetId, dimCombId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取校准幅度较大人员列表
     *
     * @param caliMeetId  校准会ID
     * @param orgId
     * @param query       查询条件
     * @param pageRequest
     * @return 校准幅度较大人员列表
     */
    public PagingList<CaliMeetLargeChangePersonnelVO> listLargeChangePersonnel(
        @NotNull String caliMeetId, String orgId, CaliMeetLargeChangePersonnelQry query, PageRequest pageRequest) {
        log.info("获取校准幅度较大人员列表, caliMeetId:{}, query:{}", caliMeetId, query);

        // 1. 校验校准会是否存在
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeetPO, ExceptionKeys.CALI_MEET_NOT_EXISTED);

        // 2. 获取宫格类型，以确定校准幅度阈值
        XpdGridPO xpdGridPO = xpdGridMapper.selectByXpdId(orgId, calimeetPO.getXpdId());
        Validate.isNotNull(xpdGridPO, ExceptionKeys.XPD_GRID_NOT_FOUND);
        int threshold = resolveThreshold(xpdGridPO);

        // 3. 查询校准幅度较大人员列表
        IPage<CaliMeetLargeChangePersonnelVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<CaliMeetLargeChangePersonnelVO> records = calimeetUserMapper.listLargeChangePersonnel(
            page, orgId, caliMeetId, calimeetPO.getXpdId(), query, threshold);

        // 4.填充用户的基础数据：UserBaseInfo类已经包含了用户基础信息字段并添加了对应注解
        if (CollectionUtils.isNotEmpty(records.getRecords())) {
            List<CaliMeetLargeChangePersonnelVO> dataList = records.getRecords();
            // 使用通用填充服务填充字段
            userInfoFiller.fill(orgId, dataList);

            // 国际化翻译
            UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
            i18nTranslator.translate(orgId, userCacheDetail.getLocale(), dataList);
            log.debug("用户信息填充完成，记录数: {}", dataList.size());
        }

        return BeanCopierUtil.toPagingList(records);
    }

    /**
     * 获取校准任务记录
     *
     * @param caliMeetId 校准会ID
     * @param orgId
     * @return 校准任务记录
     */
    public CaliMeetTaskRecordVO getCaliMeetRecord(@NotNull String caliMeetId, String orgId) {
        log.info("获取校准任务记录, caliMeetId:{}", caliMeetId);
        
        // 1. 获取校准会基本信息
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeetPO, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        
        // 2. 获取附件列表
        List<AttachmentPO> attachmentList = attachmentMapper.listByAppSourceId(orgId, caliMeetId);
        List<CaliMeetTaskRecordVO.AttachmentVO> attachments = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (AttachmentPO attachmentPO : attachmentList) {
                CaliMeetTaskRecordVO.AttachmentVO attachment = CaliMeetTaskRecordVO.AttachmentVO.builder()
                        .id(attachmentPO.getId())
                        .name(attachmentPO.getAppName())
                        .fullUrl(attachmentPO.getAppUrl())
                        .build();
                attachments.add(attachment);
            }
        }
        
        return CaliMeetTaskRecordVO.builder()
                .record(calimeetPO.getRecord())
                .attachments(attachments)
                .build();
    }

    /**
     * 更新校准任务记录
     *
     * @param caliMeetId 校准会ID
     * @param cmd        更新命令
     * @param userId     操作用户ID
     * @param orgId      组织ID
     */
    @Transactional
    public void updateCaliMeetRecord(
        @NotNull String caliMeetId, CaliMeetTaskRecordUpdateCmd cmd, String userId, String orgId) {
        log.info("更新校准任务记录, caliMeetId:{}, userId:{}, orgId:{}", caliMeetId, userId, orgId);
        
        // 1. 校验校准会是否存在及权限校验
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeetPO, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        
        // 2. 转换并保存会议内容
        calimeetPO.setRecord(cmd.getRecord());
        EntityUtil.setUpdate(calimeetPO);
        calimeetMapper.updateById(calimeetPO);
        
        // 3. 处理附件
        // 3.1 删除旧附件
        attachmentMapper.deleteByAppSourceId(orgId, caliMeetId);
        
        // 3.2 保存新附件
        if (CollectionUtils.isNotEmpty(cmd.getAttachments())) {
            List<AttachmentPO> attachments = new ArrayList<>();
            for (CaliMeetTaskRecordUpdateCmd.AttachmentUpdateCmd attachment : cmd.getAttachments()) {
                AttachmentPO attachmentPO = new AttachmentPO();
                attachmentPO.setOrgId(orgId);
                attachmentPO.setAppName(attachment.getName());
                attachmentPO.setAppUrl(attachment.getFullUrl());
                attachmentPO.setAppSource(1); // 附件来源（1-校准会）
                attachmentPO.setAppSourceId(caliMeetId);
                EntityUtil.setAuditFields(attachmentPO);
                attachments.add(attachmentPO);
            }
            if (CollectionUtils.isNotEmpty(attachments)) {
                attachmentMapper.insertOrUpdateBatch(attachments);
            }
        }
        
        log.info("LOG21053:校准任务记录更新成功, caliMeetId:{}", caliMeetId);
    }

    /**
     * 获取盘点项目校准概览统计信息
     *
     * @param xpdId 盘点项目ID
     * @param orgId 组织ID
     * @return 盘点项目校准概览统计信息
     */
    public CaliMeetGeneralOverviewVO getXpdGeneralOverview(@NotNull String xpdId, String orgId) {
        // 1. 验证盘点项目是否存在
        XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, xpdId);
        Validate.isNotNull(xpdPO, ExceptionKeys.XPD_NOT_EXIST);

        // 4. 查询综合统计数据
        CaliMeetGeneralOverviewVO result = calimeetUserMapper.selectXpdOverviewStatistic(orgId, xpdId);

        // 5. 获取校准幅度较大人数
        // 查询盘点项目的宫格配置，决定校准幅度较大人数的计算方式
        XpdGridPO xpdGridPO = xpdGridMapper.selectByXpdId(orgId, xpdId);
        Validate.isNotNull(xpdGridPO, ExceptionKeys.XPD_GRID_NOT_FOUND);
        int threshold = resolveThreshold(xpdGridPO);

        // 查询校准幅度较大人数
        int largeChangeCount = calimeetUserMapper.countXpdLargeChangePersonnel(orgId, xpdId, threshold);
        result.setLargeChangePeopleNumber(largeChangeCount);

        return result;
    }

    /**
     * 获取盘点项目校准前后对比数据
     * 如果一个员工在多个校准任务中，则以最近一次校准记录所在校准任务的数据计算校准前后对比数据
     *
     * @param xpdId    盘点项目ID
     * @param dimCombId 维度组合Key
     * @param orgId    组织ID
     * @return 校准前后对比数据
     */
    public List<CaliMeetGridItemVO> getXpdComparisonData(@NotNull String xpdId, String dimCombId, String orgId) {
        log.info("获取盘点项目校准前后对比数据, xpdId:{}, dimCombId:{}", xpdId, dimCombId);

        // 1. 验证盘点项目是否存在
        XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, xpdId);
        Validate.isNotNull(xpdPO, "xpd.not.existed");

        // 2. 获取宫格配置
        XpdGridPO xpdGridPO = xpdGridMapper.selectByXpdId(orgId, xpdId);
        Validate.isNotNull(xpdGridPO, ExceptionKeys.XPD_GRID_NOT_FOUND);

        // 3. 获取校准前后对比数据
        // 注意：SQL查询已经确保对于每个员工只统计其最近一次校准记录所在的校准任务数据

        // 4.1 获取校准前的九宫格分布
        List<CaliMeetGridItemVO> beforeItems = calimeetUserMapper.selectXpdGridBeforeCalibration(
                orgId, xpdId, dimCombId, xpdGridPO.getGridType());

        // 4.2 获取校准后的九宫格分布
        List<CaliMeetGridItemVO> afterItems = calimeetUserMapper.selectXpdGridAfterCalibration(
                orgId, xpdId, dimCombId, xpdGridPO.getGridType());

        // 4.3 计算格子人数变化和格子人数变化占比
        for (CaliMeetGridItemVO beforeGrid : beforeItems) {
            for (CaliMeetGridItemVO afterGrid : afterItems) {
                if (Objects.equals(beforeGrid.getCellIndex(), afterGrid.getCellIndex())) {
                    int beforeCount = null2zero(beforeGrid.getUserCount());
                    int afterCount = null2zero(afterGrid.getUserCount());
                    afterGrid.setChangeCount(afterCount - beforeCount);

                    // 计算变化占比
                    BigDecimal beforeUserRatio = null2zero(beforeGrid.getUserRatio());
                    BigDecimal afterUserRatio = null2zero(afterGrid.getUserRatio());
                    afterGrid.setChangeRatio(afterUserRatio.subtract(beforeUserRatio));
                }
            }
        }

        return afterItems;
    }

    /**
     * 获取盘点项目校准幅度较大人员列表
     *
     * @param xpdId      盘点项目ID
     * @param orgId      组织ID
     * @param query      查询条件
     * @param pageRequest 分页请求
     * @return 校准幅度较大人员列表
     */
    public PagingList<CaliMeetLargeChangePersonnelVO> listXpdLargeChangePersonnel(
            @NotNull String xpdId, String orgId, CaliMeetLargeChangePersonnelQry query, PageRequest pageRequest) {
        log.info("获取盘点项目校准幅度较大人员列表, xpdId:{}", xpdId);

        // 1. 验证盘点项目是否存在
        XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, xpdId);
        Validate.isNotNull(xpdPO, "xpd.not.existed");

        // 2. 获取宫格配置
        XpdGridPO xpdGridPO = xpdGridMapper.selectByXpdId(orgId, xpdId);
        Validate.isNotNull(xpdGridPO, ExceptionKeys.XPD_GRID_NOT_FOUND);
        int threshold = resolveThreshold(xpdGridPO);

        // 3. 查询校准幅度较大人员列表
        Page<CaliMeetLargeChangePersonnelVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<CaliMeetLargeChangePersonnelVO> records = calimeetUserMapper.listXpdLargeChangePersonnelPage(
                page, orgId, xpdId, threshold);

        // 4.填充用户的基础数据：UserBaseInfo类已经包含了用户基础信息字段并添加了对应注解
        if (CollectionUtils.isNotEmpty(records.getRecords())) {
            List<CaliMeetLargeChangePersonnelVO> dataList = records.getRecords();
            // 使用通用填充服务填充字段
            userInfoFiller.fill(orgId, dataList);

            // 国际化翻译
            UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
            i18nTranslator.translate(orgId, userCacheDetail.getLocale(), dataList);
            log.debug("LOG21143:用户信息填充完成，记录数: {}", dataList.size());
        }

        return BeanCopierUtil.toPagingList(records);
    }

}
