package com.yxt.talent.rv.application.calimeet;

import com.yxt.event.EventListener;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.domain.user.event.UserTransferResourceMessageEvent;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.calimeet.event.CaliMeetHoursTimeWaitTaskEvent;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.calimeet.event.CaliMeetMinutesTimeWaitTaskEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class CaliMeetEventListener implements EventListener {

    private final CaliMeetAppService caliMeetAppService;
    private final CaliMeetCmdService caliMeetCmdService;

    @org.springframework.context.event.EventListener
    public void handleCaliMeetHoursTimeWaitTaskEvent(CaliMeetHoursTimeWaitTaskEvent event) {
        log.info("LOG13715:{}", event);
        // 将当前小时内开始的校准会放入到缓存中
        caliMeetAppService.putCache();
    }

    @org.springframework.context.event.EventListener
    public void handleCaliMeetMinutesTimeWaitJobHandler(CaliMeetMinutesTimeWaitTaskEvent event) {
        log.info("LOG13735:{}", event);
        caliMeetAppService.changeMeetingStatus();
    }

    /**
     * 处理用户资源迁移事件
     * @param calimeetTransferEvent
     */
    @org.springframework.context.event.EventListener
    public void handleUserTransferResourceMessageEvent(
            UserTransferResourceMessageEvent calimeetTransferEvent) {
        String resourceCode = calimeetTransferEvent.getResourceCode();
        if (!Objects.equals(resourceCode, AppConstants.TRANSFERABLE_RESOURCES_CODE_CALI_MEET)) {
            return;
        }

        log.debug("LOG67540:");
        String orgId = calimeetTransferEvent.getOrgId();
        String fromUserId = calimeetTransferEvent.getFrom();
        String toUserId = calimeetTransferEvent.getTo();

        caliMeetCmdService.transferResource(orgId, fromUserId, toUserId);
    }

}
