package com.yxt.talent.rv.application.xpd.result.strategy;

import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 从业务库表填充用户维度结果的策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessUserDimResultFillStrategy implements UserDimResultFillStrategy {

    private final XpdResultUserDimMapper xpdResultUserDimMapper;

    @Override
    public void fillUserDimResults(String orgId, String xpdId, List<String> userIds,
                                  XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        // 从业务库获取用户维度结果
        List<XpdResultUserDimPO> resultUserDims =
            xpdResultUserDimMapper.findByXpdIdAndUserIds(orgId, xpdId, userIds);

        if (CollectionUtils.isEmpty(resultUserDims)) {
            return;
        }

        // 按用户ID分组维度数据
        Map<String, List<XpdResultUserDimPO>> userDimMap =
            resultUserDims.stream().collect(Collectors.groupingBy(XpdResultUserDimPO::getUserId));

        // 为每个用户填充维度结果
        userDimMap.forEach((userId, xpdResultUserDims) -> {
            XpdUserDimResultsVO userInfo = userInfoMap.get(userId);
            if (userInfo != null && CollectionUtils.isNotEmpty(xpdResultUserDims)) {
                List<XpdUserDimResultVO> vos = xpdResultUserDims.stream()
                    .map(XpdUserDimResultVO::new)
                    .collect(Collectors.toList());
                userInfo.setUserDimResults(vos);
            }
        });
    }

    @Override
    public UserDimResultFillStrategyType getStrategyType() {
        return UserDimResultFillStrategyType.BUSINESS;
    }
}
