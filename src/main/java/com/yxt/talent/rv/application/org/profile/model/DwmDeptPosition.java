package com.yxt.talent.rv.application.org.profile.model;

import com.yxt.talent.rv.application.org.profile.SpmodelTableConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.SqlTable;
import lombok.*;

/**
 * 部门岗位分布表
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@SqlTable(SpmodelTableConstants.DWM_DEPT_POSITION)
public class DwmDeptPosition {

    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 部门id
     */
    private String thirdDeptId;

    private String deptId;

    /**
     * 年度
     */
    private Integer yearly;

    /**
     * 岗位id
     */
    private String thirdPositionId;

    private String positionId;

    /**
     * 岗位名称
     */
    private String thirdPositionName;

    /**
     * 员工人数
     */
    private Integer userCount;

    /**
     * 管理者人数
     */
    private Integer manageCount;

    /**
     * 排序顺序
     */
    private Integer orderIndex;

    /**
     * 是否删除(0-否,1-是)
     */
    private Integer deleted;
}
