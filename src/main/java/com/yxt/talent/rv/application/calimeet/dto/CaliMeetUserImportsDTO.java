package com.yxt.talent.rv.application.calimeet.dto;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetUserImportsDTO {
    /**
     * 文件流读取的初始数据 *
     */
    private List<CaliMeetUserImportDTO> importDataList = new ArrayList<>();

    /**
     * 验证后有问题的数据 *
     */
    private List<CaliMeetUserImportDTO> failedList = new ArrayList<>();

    /**
     * 无问题的就绪数据 *
     */
    private List<CaliMeetUserImportDTO> prepareList = new ArrayList<>();

    /**
     * 待更新的校准维度项 *
     */
    private List<CaliMeetUserResultPO> cmrList = new ArrayList<>();

    /**
     * 待更新的校准用户 *
     */
    private List<CaliMeetUserPO> muList = new ArrayList<>();

    /**
     * 待更新用户 udp信息 *
     */
    private List<UdpLiteUserPO> udpLiteUsers = new ArrayList<>();

    private List<XpdDimPO> dimList = new ArrayList<>();

    /**
     * 维度配置 *
     */
    private List<PrjDimConfPO> prjDimConfs = new ArrayList<>();
}
