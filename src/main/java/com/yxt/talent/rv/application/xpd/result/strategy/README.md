# 用户维度结果填充策略模式

## 概述

这个包实现了用户维度结果填充的策略模式，支持从不同数据源填充用户维度结果数据。

## 核心组件

### 1. 策略接口
- `UserDimResultFillStrategy` - 定义填充策略的接口

### 2. 策略类型枚举
- `UserDimResultFillStrategyType` - 定义支持的策略类型
  - `DATASET` - 从数据集表（宽表）填充
  - `BUSINESS` - 从业务库表填充

### 3. 具体策略实现
- `DatasetUserDimResultFillStrategy` - 从数据集表填充的策略实现
- `BusinessUserDimResultFillStrategy` - 从业务库表填充的策略实现

### 4. 策略上下文
- `UserDimResultFillContext` - 管理策略选择和执行的上下文类

## 使用方式

### 基本用法

```java
@Autowired
private UserDimResultFillContext userDimResultFillContext;

// 从数据集表填充
userDimResultFillContext.fillFromDataset(orgId, xpdId, userIds, xpdGrid, userInfoMap);

// 从业务库填充
userDimResultFillContext.fillFromBusiness(orgId, xpdId, userIds, xpdGrid, userInfoMap);

// 使用指定策略类型填充
userDimResultFillContext.fillUserDimResults(
    UserDimResultFillStrategyType.DATASET, 
    orgId, xpdId, userIds, xpdGrid, userInfoMap
);
```

### 数据准备

```java
// 准备用户信息映射
Map<String, XpdUserDimResultsVO> userInfoMap = userIds.stream()
    .collect(Collectors.toMap(
        userId -> userId,
        userId -> {
            XpdUserDimResultsVO userInfo = new XpdUserDimResultsVO();
            userInfo.setUserId(userId);
            // 设置其他用户基本信息...
            return userInfo;
        }
    ));
```

## 扩展新策略

如需添加新的填充策略：

1. 在 `UserDimResultFillStrategyType` 枚举中添加新的策略类型
2. 实现 `UserDimResultFillStrategy` 接口
3. 使用 `@Component` 注解标记实现类
4. Spring会自动注册新策略到上下文中

```java
@Component
@RequiredArgsConstructor
public class CustomUserDimResultFillStrategy implements UserDimResultFillStrategy {
    
    @Override
    public void fillUserDimResults(String orgId, String xpdId, List<String> userIds,
                                  XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap) {
        // 自定义填充逻辑
    }
    
    @Override
    public UserDimResultFillStrategyType getStrategyType() {
        return UserDimResultFillStrategyType.CUSTOM; // 需要先在枚举中添加
    }
}
```

## 优势

1. **可扩展性** - 易于添加新的数据源填充策略
2. **可维护性** - 每种策略独立实现，职责清晰
3. **可测试性** - 可以独立测试每种策略
4. **向后兼容** - 保留了原有的服务接口，标记为 `@Deprecated`
5. **统一接口** - 通过策略上下文提供统一的调用方式
