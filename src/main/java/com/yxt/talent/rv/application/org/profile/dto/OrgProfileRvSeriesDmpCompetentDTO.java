package com.yxt.talent.rv.application.org.profile.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class OrgProfileRvSeriesDmpCompetentDTO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "人数")
    private Integer userCount;

    @Schema(description = "排序")
    private Integer orderIndex;

    @Schema(description = "胜任人数")
    private Integer competentUserCount;

    @Schema(description = "胜任率")
    private Double rvRate;
}
