package com.yxt.talent.rv.application.org;

import com.yxt.event.EventListener;
import com.yxt.event.EventPublisher;
import com.yxt.talent.rv.application.democopy.DemoCopyService;
import com.yxt.talent.rv.domain.org.event.DemoOrgInitStep1Event;
import com.yxt.talent.rv.domain.org.event.SprvOrgCopyStep1Event;
import com.yxt.talent.rv.domain.org.event.SprvOrgCopyStep2Event;
import com.yxt.talent.rv.domain.perf.event.PerfGradeInitEvent;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import com.yxt.udpfacade.bean.demo.InitStatus4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PE_C_UDP_ORG_INITIALIZE_STATUS;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrgEventListener implements EventListener {

    private final EventPublisher eventPublisher;
    private final DemoCopyService demoCopyService;
    private final RocketMqAclSender mqAclSender;

    /**
     * demo机构初始化第一步，sprv对应的操作
     *
     * @param event
     */
    @org.springframework.context.event.EventListener
    public void handleOrgInitEvent(DemoOrgInitStep1Event event) {
        log.info("LOG13875:{}", event);
        String targetOrgId = event.getTargetOrgId();
        // 发送绩效初始化事件
        eventPublisher.publish(new PerfGradeInitEvent(targetOrgId));
        // 盘点项目人才定义初始化
//        eventPublisher.publish(new PrjResultRuleInitEvent(targetOrgId));

        // 发送回执
        InitStatus4Mq initStatusBean = new InitStatus4Mq();
        initStatusBean.setModule("talentrv");
        initStatusBean.setOrgId(targetOrgId);
        initStatusBean.setStatus(1);
        mqAclSender.asyncSend(TOPIC_PE_C_UDP_ORG_INITIALIZE_STATUS, bean2Json(initStatusBean, ALWAYS));
    }

    /**
     * 奇点机构复制第一步
     * @param event
     */
    @org.springframework.context.event.EventListener
    public void handleSprvOrgCopyStep1Event(SprvOrgCopyStep1Event event) {
        log.info("LOG10022:{}", event);
        demoCopyService.preGenIdMap(event.getOrgInit4Mq());
    }

    /**
     * 奇点机构复制第二步
     * @param event
     */
    @org.springframework.context.event.EventListener
    public void handleSprvOrgCopyStep2Event(SprvOrgCopyStep2Event event) {
        log.info("LOG10042:{}", event);
        demoCopyService.demoCopy(event.getOrgInit4Mq());
    }

}
