package com.yxt.talent.rv.application.org.profile.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class OrgProfileRvSeriesDetailRateDTO {

    @Schema(description = "分类id")
    private String cataId;

    @Schema(description = "分类名称")
    private String cataName;

    @Schema(description = "维度id")
    private String dimId;

    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "人数")
    private Integer userCount;

    @Schema(description = "达标人数")
    private Integer achievedUserCount;

    @Schema(description = "达标率")
    private Double achievedRate;
}
