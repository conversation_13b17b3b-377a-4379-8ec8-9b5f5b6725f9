package com.yxt.talent.rv.application.calimeet.dto;

import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetUserImportDTO implements ImportContent {

    // 用户Id
    private String userId;
    // 用户姓名
    private String fullName;
    // 用户账号
    private String userName;
    // 用户部门
    private String deptName;
    // 岗位名称
    private String positionName;
    // 维度数据Map<key,value> key为维度名称，value为维度结果
    private Map<String, String> dimensionMap = new LinkedHashMap<>(8);
    // 维度数据Map<key,value> key为维度名称，value为维度结果 【如：绩效、校准后绩效】
    private Map<String, String> originalMap = new LinkedHashMap<>(8);
    // 发展建议
    private String suggestion;
    // 类型：1-维度等级，2-维度评分
    private int type;
    // 错误消息
    private String errorMsg;
    private List<String> headNames;
}
