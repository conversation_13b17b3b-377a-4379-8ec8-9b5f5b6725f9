package com.yxt.talent.rv.application.perf.lagecy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.rv.application.common.CommonAppService;
import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import com.yxt.talent.rv.controller.manage.perf.query.PerfQuery;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfScoreVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfUserInfoVO;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PerfAppService {

    public static final String NAV_CODE_GWNL_TALENTRV_ACHIEVEMENTS = "sp_gwnl_talentrv_achievements";
    public static final String DATA_PERM_CODE_TALENTRV_ACHIEVEMENTS_GET_EXTENT = "sp_talentrv__achievementsget_extent";
    private final PerfMapper perfMapper;
    private final PerfPeriodMapper perfPeriodMapper;
    private final PerfGradeMapper perfGradeMapper;
    private final CommonAppService commonAppService;

    /**
     * 绩效管理分页查询
     *
     * @param pageRequest 分页参数
     */
    public PagingList<PerfUserInfoVO> findPage(
            UserCacheDetail userCache, PageRequest pageRequest, PerfQuery perfQuery) {
        List<String> deptIds = perfQuery.getDeptIds();
        String searchKey = perfQuery.getSearchKey();
        List<String> periods = perfQuery.getPeriods();

        //List<String> authUserIds;
        // 2.0 数据权限
        //authUserIds = getAuthUserIds(userCache);
        List<String> authDeptIds = getAuthDeptIds(userCache);
        SearchUdpScopeAuthQuery query = new SearchUdpScopeAuthQuery();
        query.setKwType(perfQuery.getKwType());
        query.setSearchKey(perfQuery.getSearchKey());
        commonAppService.fillAuthInfo(userCache.getOrgId(), query,
            NAV_CODE_GWNL_TALENTRV_ACHIEVEMENTS, DATA_PERM_CODE_TALENTRV_ACHIEVEMENTS_GET_EXTENT);
        log.info("findPage query={}", query);
        if (query.isAdmin()) {
            userCache.setAdmin("1");
        }
        List<String> scopeUserIds = query.getScopeUserIds();
        List<String> scopeDeptIds = query.getScopeDeptIds();
        if (CollectionUtils.isEmpty(scopeDeptIds) && CollectionUtils.isEmpty(scopeUserIds) && !"1".equalsIgnoreCase(userCache.getAdmin())) {
            log.info("LOG10790:userId={}", userCache.getUserId());
            return new PagingList<>();
        }
        if (CollectionUtils.isNotEmpty(deptIds)) {
            query.setScopeDeptIds(deptIds);

        }

        Page<PerfUserInfoVO> pageable =
                new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        // 如果前端不传周期字段信息则默认查询所有数据
        String orgId = userCache.getOrgId();
        if (CollectionUtils.isEmpty(periods)) {
            List<PerfPeriodPO> periodList = perfPeriodMapper.selectByOrgId(orgId);
            if (CollectionUtils.isNotEmpty(periodList)) {
                periods = periodList.stream()
                        .map(PerfPeriodPO::getId)
                        .collect(Collectors.toList());
            }
        }

        IPage<PerfUserInfoVO> pageList =
                perfMapper.selectUserPage(
                        pageable, orgId, deptIds, authDeptIds, SqlUtil.escapeSql(searchKey), scopeUserIds, query);
        // 根据查询出来的人员列表分页数据加上动态绩效周期列补齐数据信息
        if (CollectionUtils.isNotEmpty(pageList.getRecords()) &&
            CollectionUtils.isNotEmpty(periods)) {
            List<String> userIds = pageList.getRecords()
                    .stream()
                    .map(PerfUserInfoVO::getUserId)
                    .collect(Collectors.toList());
            // 查询出阶段内容数据
            List<PerfPO> perfs = perfMapper.selectByPeriodIdsAndUserIds(orgId, periods, userIds);
            // 整理数据按照人员拆分到map
            Map<String, List<PerfPO>> map =
                    perfs.stream().collect(Collectors.groupingBy(PerfPO::getUserId));
            // 由于自定义绩效等级的加入，做兼容逻辑处理，将可能出现的绩效等级转为map
            //Map<Integer, String> gradeMap = PerfGrade.DefaultLevel.findCodeNameMap();
            // 查询自定义的绩效等级
            List<PerfGradePO> gradeList = perfGradeMapper.selectByOrgIdIncludeDeleted(orgId);
            Map<Integer, String> gradeMap = StreamUtil.list2map(gradeList, PerfGradePO::getGradeValue,
                    PerfGradePO::getGradeName);
            /*if (CollectionUtils.isNotEmpty(gradeList)) {
                Map<Integer, String> orgGradeMap =
                        StreamUtil.list2map(gradeList, PerfGradePO::getGradeValue,
                                PerfGradePO::getGradeName);
                gradeMap.putAll(orgGradeMap);
            }*/
            pageList.getRecords().forEach(user -> {
                List<PerfPO> perf = map.get(user.getUserId());
                if (CollectionUtils.isNotEmpty(perf)) {
                    // 转换成等级显示
                    Map<String, PerfPO> userMap =
                            StreamUtil.list2map(perf, PerfPO::getPeriodId);
                    Map<String, PerfScoreVO> userMapStr = new HashMap<>(8);
                    for (Map.Entry<String, PerfPO> entry : userMap.entrySet()) {
                        PerfPO perfPO = entry.getValue();
                        PerfScoreVO perfScoreVO = new PerfScoreVO();
                        perfScoreVO.setPerfPoint(perfPO.getPerfPoint());
                        if (perfPO.getPeriodLevel() == null) {
                            perfScoreVO.setPerfLevel(null);
                        } else {
                            perfScoreVO.setPerfLevel(gradeMap.get(perfPO.getPeriodLevel()));
                        }
                        userMapStr.put(entry.getKey(), perfScoreVO);
                    }
                    user.setPerfMap(userMapStr);
                }
            });
        }
        return BeanCopierUtil.toPagingList(pageList);
    }

    private List<String> getAuthDeptIds(UserCacheDetail userCache) {
        List<String> resUserIds = new ArrayList<>();
        SearchUdpScopeAuthQuery query = new SearchUdpScopeAuthQuery();
        commonAppService.fillAuthInfo(userCache.getOrgId(), query,
            NAV_CODE_GWNL_TALENTRV_ACHIEVEMENTS, DATA_PERM_CODE_TALENTRV_ACHIEVEMENTS_GET_EXTENT);
        List<String> scopeDeptIds = query.getScopeDeptIds();
        if (CollectionUtils.isEmpty(scopeDeptIds)) {
            return resUserIds;
        }

        // 获取表内所有的userIds
        List<String> deptIds = perfMapper.selectDeptIdsByOrgId(userCache.getOrgId());
        return CommonUtil.getIntersectionOrList(scopeDeptIds, deptIds);
        /*

        return authorizationService.getAuthUserIds(userCache, NAV_CODE_GWNL_TALENTRV_ACHIEVEMENTS,
                DATA_PERM_CODE_TALENTRV_ACHIEVEMENTS_GET_EXTENT);*/
    }

    public List<PerfPO> getByUserAndPeriod(String orgId, String period, List<String> userIds) {
        return perfMapper.selectByPeriodIdAndUserIds(orgId, period, userIds);
    }
}
