package com.yxt.talent.rv.application.dmp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 人才分布
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpLayerNumDTO {

    @Schema(description = "分布名称")
    private String layerName;

    @Schema(description = "分布id")
    private String layerId;

    @Schema(description = "分布人员数量")
    private int layerUserNum;

    @Schema(description = "人员占比")
    private BigDecimal userRate;
}
