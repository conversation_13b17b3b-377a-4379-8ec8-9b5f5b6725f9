package com.yxt.talent.rv.application.dmp.task.legacy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.util.*;
import com.yxt.export.I18nComponent;
import com.yxt.spevalfacade.bean.form.*;
import com.yxt.spevalfacade.service.SpEvalApiFacade;
import com.yxt.spmodel.facade.bean.rule.LabelConditionInfo;
import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import com.yxt.spmodel.facade.bean.rule.LabelOperatorInfo;
import com.yxt.spmodel.facade.bean.rule.LabelRuleInfo;
import com.yxt.sptalentapifacade.bean.spjq.JqReq;
import com.yxt.sptalentapifacade.bean.spjq.SpJqDetail4Get;
import com.yxt.sptalentapifacade.bean.spjq.SpJqDim4Get;
import com.yxt.sptalentapifacade.bean.spjq.SpJqItem4Get;
import com.yxt.sptalentapifacade.enums.DmpDimType;
import com.yxt.talent.rv.application.dmp.calc.DmpCalculator;
import com.yxt.talent.rv.application.dmp.calc.dto.DmpRuleValueDTO;
import com.yxt.talent.rv.application.dmp.task.dto.DmpTaskDimInfoDTO;
import com.yxt.talent.rv.application.dmp.user.dto.DmpUserDimDetailDTO;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpTaskBaseEditCmd;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpTaskCreateUpdateCmd;
import com.yxt.talent.rv.controller.manage.dmp.query.DmpTaskDimQuery;
import com.yxt.talent.rv.controller.manage.dmp.query.DmpUser4Query;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.*;
import com.yxt.talent.rv.domain.dmp.Dmp;
import com.yxt.talent.rv.domain.dmp.entity.task.DmpTask;
import com.yxt.talent.rv.domain.dmp.entity.user.DmpUserDimResult;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.NumberEnum;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleScoreDetailMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleScoreMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserDimDetailMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserDimResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserTaskResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRuleScoreDetailPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRuleScorePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimDetailPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.remote.*;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class DmpTaskAppService {

    public static final int FORM_SOURCE_TYPE = 101;

    private static final String HEADER_PREFIX = "apis.sptalentrv.dmp.taskuser.export.header.";
    private static final String[] HEADER2_KEYS = {"fullUserName", "statusDesc", "deptName"};
    private static final String[] HEADER3_KEYS = { "matchedDimCount", "unMatchedDimCount"};
    private static final String SHEET = "sheet1";

    private final I18nComponent i18nComponent;

    private final DmpTaskMapper dmpTaskMapper;
    private final SpevalAclService spevalAclService;
    private final DmpMapper dmpMapper;
    private final DmpTaskDimMapper dmpTaskDimMapper;
    private final SptalentAclService sptalentAclService;
    private final DmpUserMapper dmpUserMapper;
    private final DmpUserDimResultMapper dmpUserDimResultMapper;

    private final UdpLiteUserMapper udpLiteUserMapper;
    private final DmpUserDimDetailMapper dmpUserDimDetailMapper;
    private final DmpRuleScoreMapper dmpRuleScoreMapper;
    private final DmpRuleScoreDetailMapper dmpRuleScoreDetailMapper;
    private final RedisRepository talentRedisRepository;
    private final DmpUserTaskResultMapper dmpUserTaskResultMapper;
    private final Executor wafTaskExecutor;

    private final SpEvalApiFacade spEvalApiFacade;
    private final L10nAclService l10nAclService;
    private final UdpAclService udpAclService;

    private final SpmodelAclService spmodelAclService;

    @Nonnull
    private static DmpTaskDimPO buildDmpTaskDim(
            String orgId, String dmpId, String taskId, DmpTaskDimInfoDTO taskDimInfo,
            Map<String, DmpTaskDimPO> existDimMap, UserCacheDetail operator) {
        DmpTaskDimPO taskDim = new DmpTaskDimPO();
        DmpTaskDimPO existDmpTaskDim = existDimMap.get(taskDimInfo.getJqDimId());
        taskDim.setId(existDmpTaskDim == null ? ApiUtil.getUuid() : existDmpTaskDim.getId());
        taskDim.setOrgId(orgId);
        taskDim.setDmpId(dmpId);
        taskDim.setTaskId(taskId);
        taskDim.setDimType(taskDimInfo.getDimType());
        taskDim.setJqSkillModelId(taskDimInfo.getJqSkillModelId());
        taskDim.setJqRtModelId(taskDimInfo.getJqRtModelId());
        taskDim.setJqDimId(taskDimInfo.getJqDimId());
        taskDim.setJqDimName(taskDimInfo.getDimName());
        taskDim.setJqCataId(String.valueOf(taskDimInfo.getRootCategoryId()));
        taskDim.setJqCataName(taskDimInfo.getRootCategoryName());
        taskDim.setJqTplCatId(taskDimInfo.getJqTplCatId());
        taskDim.setJqTplCatName(taskDimInfo.getJqTplCatName());
        taskDim.setDeleted(YesOrNo.NO.getValue());
        taskDim.setRuleId(taskDimInfo.getRuleId());
        EntityUtil.setAuditFields(taskDim, operator.getUserId());
        return taskDim;
    }

    private static void fillLinked(FormFacade form, DmpTaskDTO result) {
        List<FormDimFacade> formDimFacadeList = form.getFormDimFacadeList();
        if (CollectionUtils.isEmpty(formDimFacadeList)) {
            return;
        }

        List<DmpTaskDimInfoDTO> dimInfos = result.getDimInfos();
        for (DmpTaskDimInfoDTO dimInfo : dimInfos) {
            for (FormDimFacade formDimFacade : formDimFacadeList) {
                // 填充linked字段
                if (StringUtils.equals(dimInfo.getJqDimId(), formDimFacade.getDimId())) {
                    dimInfo.setLinked(formDimFacade.getLinked());
                    break;
                }
            }
        }
    }

    // 检查ruleId是否相等，如果都为空或null，也认为相等
    private static boolean isRuleEquals(String ruleId1, String ruleId2) {
        return StringUtils.equals(ruleId1, ruleId2) ||
               (StringUtils.isBlank(ruleId1) && StringUtils.isBlank(ruleId2));
    }

    private static boolean isDmpTaskDeleted(DmpTaskPO currDmpTask) {
        return currDmpTask == null || currDmpTask.getDeleted() == YesOrNo.YES.getValue();
    }

    /**
     * 获取项目下的任务列表
     *
     * @param orgId       机构id
     * @param dmpId       项目id
     * @param taskName
     * @param pageRequest
     * @return 任务列表
     */
    public PagingList<DmpTaskDTO> getDmpTaskPage(
            String orgId, String dmpId, String taskName, PageRequest pageRequest) {
        IPage<DmpTaskPO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        // 获取项目信息
        DmpPO dmp = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
        Validate.isNotNull(dmp, ExceptionKeys.DMP_NOT_EXISTS);

        assert dmp != null;
        Integer dmpStatus = dmp.getDmpStatus();
        LocalDateTime dmpWithdrawTime = dmp.getWithdrawTime();
        // 异步获取任务列表
        CompletableFuture<IPage<DmpTaskPO>> dmpTasksFuture = CompletableFuture.supplyAsync(
                () -> dmpTaskMapper.searchByPage(page, orgId, dmpId, SqlUtil.escapeSql(taskName)));

        // 异步获取表单任务列表
        CompletableFuture<List<FormBasicInfo>> formTasksFuture = CompletableFuture.supplyAsync(
                () -> spevalAclService.getFormListByProjectId(dmpId, orgId));

        // 异步获取任务维度列表
        CompletableFuture<List<DmpTaskDimPO>> dmpTaskDimsFuture =
                CompletableFuture.supplyAsync(() -> dmpTaskDimMapper.selectByDmpId(orgId, dmpId));

        // 等待所有异步任务完成
        CompletableFuture.allOf(dmpTasksFuture, formTasksFuture, dmpTaskDimsFuture).join();

        // 获取任务列表
        PagingList<DmpTaskDTO> dmpTaskPage = dmpTasksFuture.thenApply(dmpTasks -> {
            PagingList<DmpTaskDTO> convertedTasks =
                    BeanCopierUtil.toPagingList(dmpTasks, DmpTaskPO.class, DmpTaskDTO.class);
            convertedTasks.getDatas().forEach(dmpTask -> {
                if (DmpTask.Type.isDynamicMatch(dmpTask.getTaskType())) {
                    // 动态匹配类型的项目：如果项目已发布，则进度100%，如果项目为发布进度0%
                    // 如果任务是未发布状态，进度0%
                    if (Dmp.Status.isDraft(dmpStatus) || Dmp.Status.isTimedPublish(dmpStatus)
                        || dmpTask.getTaskStatus() == 0 || dmpTask.getTaskStatus() == 5) {
                        dmpTask.setTaskProgress(BigDecimal.valueOf(0));
                    } else {
                        dmpTask.setTaskProgress(BigDecimal.valueOf(100));
                    }
                }
            });
            return convertedTasks;
        }).join();

        // 获取任务维度映射
        Map<String, List<DmpTaskDimPO>> dmpTaskDimMap = dmpTaskDimsFuture.join()
                .stream()
                .collect(Collectors.groupingBy(DmpTaskDimPO::getTaskId));

        // 获取表单任务映射
        Map<String, FormBasicInfo> formBasicInfoMap = formTasksFuture.join()
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getId()))
                .collect(Collectors.toMap(FormBasicInfo::getId, formTask -> formTask));

        // 更新任务信息
        // 获取任务列表
        List<String> taskIds =
                dmpTaskPage.getDatas().stream().filter(x-> x.getTaskType() == 1).map(DmpTaskDTO::getId).collect(Collectors.toList());

        //
        Map<String, DmpTaskResultNumDTO> taskResultNumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(taskIds)) {
            List<DmpTaskResultNumDTO> resultNum =
                    dmpUserDimResultMapper.findResultNum(orgId, dmpId, taskIds);
            taskResultNumMap =
                    StreamUtil.list2map(resultNum, DmpTaskResultNumDTO::getTaskId);
        }


        Map<String, DmpTaskResultNumDTO> finalTaskResultNumMap = taskResultNumMap;
        dmpTaskPage.getDatas().forEach(dmpTask -> {
            List<DmpTaskDimPO> taskDims = dmpTaskDimMap.get(dmpTask.getId());
            if (CollectionUtils.isNotEmpty(taskDims)) {
                dmpTask.setBindDimCount(taskDims.size());
            }

            if (formBasicInfoMap.containsKey(dmpTask.getEvalFormId())) {
                FormBasicInfo formBasicInfo = formBasicInfoMap.get(dmpTask.getEvalFormId());
                if (dmpTask.getTaskStatus() != 6) {
                    dmpTask.setTaskStatus(formBasicInfo.getProjectStatus());
                }
                dmpTask.setActiveStatus(formBasicInfo.getFormStatus());

                dealProgress(dmpTask, formBasicInfo);
            }
            // 根据项目状态如果是撤回，比较任务创建时间设置isNewTask标识，判断项目状态是撤回状态并且任务的创建时间在撤回时间后
            dealIsNewWork(dmpStatus, dmpWithdrawTime, dmpTask);

            dealHasRecord(finalTaskResultNumMap, dmpTask);

        });

        return dmpTaskPage;
    }

    private void dealIsNewWork(
            Integer dmpStatus, LocalDateTime dmpWithdrawTime, DmpTaskDTO dmpTask) {
        if ((Objects.nonNull(dmpStatus) && dmpStatus == 5) &&
            (Objects.nonNull(dmpWithdrawTime) &&
             dmpTask.getCreateTime().isAfter(dmpWithdrawTime))) {
            dmpTask.setIsNewTask(1);
        }
    }

    private void dealProgress(DmpTaskDTO dmpTask, FormBasicInfo formBasicInfo) {
        BigDecimal taskProgress = formBasicInfo.getFinishPercent();
        if (taskProgress != null) {
            BigDecimal progress = taskProgress.multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP);
            // progress不能小于0，不能大于100
            if (progress.compareTo(BigDecimal.ZERO) < 0) {
                progress = BigDecimal.ZERO;
            } else if (progress.compareTo(BigDecimal.valueOf(100)) > 0) {
                progress = BigDecimal.valueOf(100);
            }
            dmpTask.setTaskProgress(progress);
        } else {
            log.warn("LOG50040: 任务进度为空，formId:{}", dmpTask.getEvalFormId());
            dmpTask.setTaskProgress(BigDecimal.ZERO);
        }
    }

    private void dealHasRecord(
            Map<String, DmpTaskResultNumDTO> finalTaskResultNumMap, DmpTaskDTO dmpTask) {
        DmpTaskResultNumDTO dmpTaskResultNumDTO = finalTaskResultNumMap.get(dmpTask.getId());
        if (dmpTask.getTaskType() == 0) {
            // 如果任务是动态匹配，任务状态状态是 启用，未发布，HasRecord = 0
            if (dmpTask.getActiveStatus() == 3 && (dmpTask.getTaskStatus() == 0 || dmpTask.getTaskStatus() == 5)) {
                dmpTask.setHasRecord(0);
            } else {
                dmpTask.setHasRecord(1);
            }
        } else {
            if (dmpTaskResultNumDTO != null) {
                dmpTask.setHasRecord(dmpTaskResultNumDTO.getResultNum() > 0 ? NumberEnum.ONE.getNumber() : NumberEnum.ZERO.getNumber());
            }
        }
    }

    /**
     * 保存任务基本信息
     *
     * @param orgId 机构id
     * @param dmpId 项目id
     * @param cmd   任务信息
     * @param operator  用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public DmpTaskPO createTaskBasic(
            String orgId, String dmpId, DmpTaskCreateUpdateCmd cmd, UserCacheDetail operator) {
        // 校验
        List<DmpTaskPO> tasks = dmpTaskMapper.selectByDmpIdAndName(orgId, dmpId, cmd.getTaskName());
        Validate.isTrue(CollectionUtils.isEmpty(tasks), ExceptionKeys.DMP_TASK_NAME_CONFLICT);
        List<DmpTaskDimInfoDTO> taskDims = cmd.getDims();
        Validate.isNotEmpty(taskDims, ExceptionKeys.DMP_TASK_DIM_EMPTY);
        DmpPO dmp = loadDmp(orgId, dmpId);
        // 只有未发布和撤回状态才允许创建项目
        taskCanBeCreated(dmp);

        // 保存任务
        DmpTaskPO dmpTask = buildDmpTask(orgId, dmp, cmd, operator);
        dmpTaskMapper.insert(dmpTask);

        // 保存任务维度
        String dmpTaskId = dmpTask.getId();
        handleDmpTaskDim(dmpTask.getOrgId(), dmpTask.getDmpId(), dmpTaskId, taskDims,
                Maps.newHashMap(), operator);

        // 保存表单评价的关系和权重
        if (DmpTask.Type.isEvalForm(cmd.getTaskType())) {
            Validate.isNotNull(cmd.getFormConfig(), ExceptionKeys.DMP_FORM_CONFIG_EMPTY);

            CommonUtil.execAfterCommitIfHas(() -> {
                FormFacade form4Create = createFormFacade(cmd, dmp, dmpTask);
                log.debug("LOG60020:{}",
                        BeanHelper.bean2Json(form4Create, ALWAYS));
                String evalFormId =
                        spevalAclService.createOrUpdateForm(form4Create, orgId, operator.getUserId());
                dmpTask.setEvalFormId(evalFormId);
                dmpTaskMapper.updateById(dmpTask);
            });
            // 缓存评估关系
            cacheFormEvalRelation(orgId, dmpId, cmd, dmpTaskId);
        }
        return dmpTask;
    }

    private void cacheFormEvalRelation(
            String orgId, String dmpId, DmpTaskCreateUpdateCmd cmd, String dmpTaskId) {
        FormConfigFacade formConfig = cmd.getFormConfig();
        String cache = BeanHelper.bean2Json(formConfig, ALWAYS);
        String cacheKey = String.format(RedisKeys.CK_DMP_FORM_EVAL_RELATION, orgId, dmpId);
        talentRedisRepository.opsForHash().put(cacheKey, dmpTaskId, cache);
        talentRedisRepository.expire(cacheKey, TimeUnit.DAYS.toDays(30), TimeUnit.DAYS);
    }

    private void taskCanBeCreated(DmpPO dmp) {
        Integer dmpStatus = dmp.getDmpStatus();
        Validate.isTrue(Dmp.Status.isDraft(dmpStatus) || Dmp.Status.isWithdraw(dmpStatus),
                ExceptionKeys.DMP_TASK_CREATE_FAILED);
    }

    private DmpTaskPO buildDmpTask(
            String orgId, DmpPO dmp, DmpTaskCreateUpdateCmd cmd, UserCacheDetail operator) {
        DmpTaskPO dmpTask = new DmpTaskPO();
        dmpTask.setId(ApiUtil.getUuid());
        dmpTask.setOrgId(orgId);
        dmpTask.setDmpId(dmp.getId());
        dmpTask.setTaskName(cmd.getTaskName());
        dmpTask.setTaskType(cmd.getTaskType());
        dmpTask.setActiveStatus(DmpTask.ActiveStatus.DRAFT.getCode());
        dmpTask.setTaskStatus(dmp.getDmpStatus());
        dmpTask.setRemark(cmd.getRemark());
        dmpTask.setDeleted(YesOrNo.NO.getValue());
        EntityUtil.setAuditFields(dmpTask, operator.getUserId());
        return dmpTask;
    }

    /**
     * 更新任务基本信息
     *
     * @param orgId
     * @param dmpId
     * @param taskId
     * @param taskDimInfos 任务维度
     * @param existDimMap
     * @param operator         任务
     */
    public void handleDmpTaskDim(
            String orgId, String dmpId, String taskId, List<DmpTaskDimInfoDTO> taskDimInfos,
            Map<String, DmpTaskDimPO> existDimMap, UserCacheDetail operator) {

        if (CollectionUtils.isEmpty(taskDimInfos)) {
            return;
        }

        List<DmpTaskDimPO> dmpTaskDims = new ArrayList<>();
        // 按照维度类型排序
        taskDimInfos.sort(Comparator.comparing(DmpTaskDimInfoDTO::getDimType));
        int orderIndex = 0;
        for (DmpTaskDimInfoDTO taskDimInfo : taskDimInfos) {
            DmpTaskDimPO dmpTaskDim =
                    buildDmpTaskDim(orgId, dmpId, taskId, taskDimInfo, existDimMap, operator);
            // 前端无法计算排序字段，由后端根据维度类型排序
            dmpTaskDim.setOrderIndex(orderIndex += 10);
            dmpTaskDims.add(dmpTaskDim);

            // 这里把dto赋值，后续方法使用
            taskDimInfo.setOrderIndex(dmpTaskDim.getOrderIndex());
        }

        // 填充维度分类名称
        DmpPO dmp = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
        if (dmp == null) {
            throw new ApiException(ExceptionKeys.DMP_NOT_EXISTS);
        }
        SpJqDetail4Get spJqDetail4Get =
                sptalentAclService.getSpJqDetail4Get(dmpTaskDims, orgId, dmp);
        List<SpJqItem4Get> jqSetting4GetList = spJqDetail4Get != null ? spJqDetail4Get.getJqSetting4GetList() : new ArrayList<>();
        Map<String, SpJqItem4Get> jqItem4GetMap =
                StreamUtil.list2map(jqSetting4GetList, SpJqItem4Get::getDimId);
        for (DmpTaskDimPO dmpTaskDim : dmpTaskDims) {
            Optional.ofNullable(jqItem4GetMap.get(dmpTaskDim.getJqDimId()))
                    .ifPresent(spJqItem4Get -> {
                        dmpTaskDim.setJqCataId(String.valueOf(spJqItem4Get.getRootCategoryId()));
                        dmpTaskDim.setJqCataName(spJqItem4Get.getRootCategoryName());
                        dmpTaskDim.setJqTplCatId(String.valueOf(spJqItem4Get.getTplCatalogId()));
                        dmpTaskDim.setJqTplCatName(spJqItem4Get.getTplCatalogName());
                        dmpTaskDim.setJqDimName(spJqItem4Get.getDimName());
                        dmpTaskDim.setRuleId(spJqItem4Get.getRuleId());
                    });
        }

        List<DmpTaskDimPO> existDmpTasks = new ArrayList<>();
        List<DmpTaskDimPO> newDmpTasks = new ArrayList<>();
        for (DmpTaskDimPO dmpTaskDim : dmpTaskDims) {
            if (existDimMap.containsKey(dmpTaskDim.getJqDimId())) {
                existDmpTasks.add(dmpTaskDim);
            } else {
                newDmpTasks.add(dmpTaskDim);
            }
        }
        if (CollectionUtils.isNotEmpty(existDmpTasks)) {
            dmpTaskDimMapper.updateBatch(existDmpTasks);
        }
        if (CollectionUtils.isNotEmpty(newDmpTasks)) {
            dmpTaskDimMapper.insertBatch(newDmpTasks);
        }
    }

    private FormFacade createFormFacade(
            DmpTaskCreateUpdateCmd cmd, DmpPO dmp, DmpTaskPO dmpTask) {
        FormFacade form4Create = new FormFacade();
        form4Create.setFormId(dmpTask.getEvalFormId());
        form4Create.setFormName(cmd.getTaskName());
        form4Create.setSourceType(FORM_SOURCE_TYPE);
        form4Create.setStartTime(
                Date.from(dmp.getStartTime().atZone(ZoneId.systemDefault()).toInstant()));
        form4Create.setEndTime(
                Date.from(dmp.getEndTime().atZone(ZoneId.systemDefault()).toInstant()));
        form4Create.setFormDesc(dmpTask.getRemark());
        form4Create.setProjectId(dmp.getId());
        form4Create.setProjectStatus(dmp.getDmpStatus());
        form4Create.setFormConfigFacade(cmd.getFormConfig());
        form4Create.setJqId(dmp.getJqId());
        form4Create.setRemark(dmpTask.getRemark());
        form4Create.setFormStatus(dmpTask.getActiveStatus());

        List<FormDimFacade> formDims = cmd.getDims().stream().map(dim -> {
            FormDimFacade formDim = new FormDimFacade();
            formDim.setDimType(dim.getDimType());
            formDim.setDimId(dim.getJqDimId());
            formDim.setOrderIndex(dim.getOrderIndex());
            return formDim;
        }).collect(Collectors.toList());
        form4Create.setFormDimFacadeList(formDims);

        return form4Create;
    }

    /**
     * 获取匹配岗位的任务详情，包括维度已经维度下的条件组和条件，是一个大而全的接口
     *
     * @param orgId    机构id
     * @param taskId   任务id
     * @param operator 用户信息
     * @return 任务详情
     */
    public DmpTaskDTO findTaskInfo(String orgId, String taskId, String operator) {
        DmpTaskPO dmpTask = loadDmpTask(orgId, taskId);
        DmpTaskDTO result = new DmpTaskDTO();
        BeanCopierUtil.copy(dmpTask, result);

        List<DmpTaskDimPO> dmpTaskDims =
                dmpTaskDimMapper.selectByTaskId(orgId, dmpTask.getDmpId(), taskId);
        processDmpTaskDims(dmpTaskDims, result, orgId);

        if (DmpTask.Type.isEvalForm(dmpTask.getTaskType())) {
            // 如果是表单，从测评中心获取任务状态
            FormFacade formDetail =
                    spevalAclService.getFormDetail(dmpTask.getEvalFormId(), orgId, operator);
            if (formDetail != null) {
                result.setTaskStatus(formDetail.getProjectStatus());
                result.setActiveStatus(formDetail.getFormStatus());
                result.setTaskUserCnt(formDetail.getUserCount());
                result.setFormConfig(formDetail.getFormConfigFacade());
                // 填充维度是否绑定了表单题目的标识
                fillLinked(formDetail, result);
            }
        } else {
            // 人数从人岗动态匹配项目获取
            result.setTaskUserCnt(dmpUserMapper.countByDmpId(orgId, dmpTask.getDmpId()));
        }

        return result;
    }

    private DmpTaskPO loadDmpTask(String orgId, String taskId) {
        return Optional.ofNullable(dmpTaskMapper.selectByOrgIdAndId(orgId, taskId))
                .orElseThrow(() -> new ApiException(ExceptionKeys.DMP_TASK_NOT_EXISTED));
    }

    private void processDmpTaskDims(
            List<DmpTaskDimPO> dmpTaskDims, DmpTaskDTO task4Get, String orgId) {
        if (CollectionUtils.isEmpty(dmpTaskDims)) {
            return;
        }
        String dmpId = task4Get.getDmpId();
        List<DmpTaskDimInfoDTO> dmpTaskDimInfoDtos = getDmpTaskDimInfos(orgId, dmpId, dmpTaskDims);
        dmpTaskDimInfoDtos.sort(Comparator.comparing(DmpTaskDimInfoDTO::getOrderIndex));
        task4Get.setDimInfos(dmpTaskDimInfoDtos);
    }

    private List<DmpTaskDimInfoDTO> getDmpTaskDimInfos(
            String orgId, String dmpId, List<DmpTaskDimPO> dmpTaskDims) {
        SpJqDetail4Get spJqDetail4Get =
                sptalentAclService.getSpJqDetail4Get(dmpTaskDims, orgId, loadDmp(orgId, dmpId));
        List<SpJqItem4Get> jqSetting4GetList = spJqDetail4Get != null ? spJqDetail4Get.getJqSetting4GetList() : new ArrayList<>();
        Map<String, SpJqItem4Get> jqItem4GetMap =
                StreamUtil.list2map(jqSetting4GetList, SpJqItem4Get::getDimId);
        return dmpTaskDims.stream()
                .map(dmpTaskDim -> getDmpTaskDimInfo(dmpTaskDim, jqItem4GetMap))
                .collect(Collectors.toList());
    }

    @Nonnull
    private DmpTaskDimInfoDTO getDmpTaskDimInfo(
            DmpTaskDimPO dmpTaskDim, Map<String, SpJqItem4Get> jqItem4GetMap) {
        DmpTaskDimInfoDTO taskDimInfo = new DmpTaskDimInfoDTO();
        BeanCopierUtil.copy(dmpTaskDim, taskDimInfo);

        processDimBasic(dmpTaskDim, taskDimInfo, jqItem4GetMap);

        return taskDimInfo;
    }

    /**
     * 填充维度基本信息和条件组
     *
     * @param dmpTaskDim    任务维度
     * @param taskDimInfo   任务维度信息
     * @param jqItem4GetMap 能力模型
     */
    private void processDimBasic(
            DmpTaskDimPO dmpTaskDim, DmpTaskDimInfoDTO taskDimInfo,
            Map<String, SpJqItem4Get> jqItem4GetMap) {
        SpJqItem4Get spJqItem4Get = jqItem4GetMap.get(dmpTaskDim.getJqDimId());
        Validate.isNotNull(spJqItem4Get, ExceptionKeys.DMP_JQ_DIM_NOT_EXISTED);
        taskDimInfo.setDimName(spJqItem4Get.getDimName());
        taskDimInfo.setDimCategoryName(spJqItem4Get.getDimCategoryName());
        taskDimInfo.setDimCategoryId(spJqItem4Get.getDimCategoryId());
        taskDimInfo.setRootCategoryId(spJqItem4Get.getRootCategoryId());
        taskDimInfo.setRootCategoryName(spJqItem4Get.getRootCategoryName());
        taskDimInfo.setJqTplCatId(String.valueOf(spJqItem4Get.getTplCatalogId()));
        taskDimInfo.setJqTplCatName(spJqItem4Get.getTplCatalogName());
        taskDimInfo.setJqDimId(spJqItem4Get.getDimId());
        taskDimInfo.setDeleted(spJqItem4Get.getDeleted());
        taskDimInfo.setJqType(spJqItem4Get.getJqType());
        // 填充能力等级
        taskDimInfo.setSkillLevelMaps(spJqItem4Get.getSkillLevelMaps());
        taskDimInfo.setSkillStandLevel(spJqItem4Get.getSkillStandLevel());
        if (!isRuleEquals(taskDimInfo.getRuleId(), spJqItem4Get.getRuleId())) {
            log.warn("LOG60580: 任务维度的规则id和任职资格的规则id不一致，" +
                    "taskId={}, taskDimId={}, jqDimId={}, taskRuleId={}, jqRuleId={}",
                    dmpTaskDim.getTaskId(), dmpTaskDim.getId(), taskDimInfo.getJqDimId(),
                    taskDimInfo.getRuleId(), spJqItem4Get.getRuleId());
        }
        // 填充自定义规则id
        if (StringUtils.isBlank(taskDimInfo.getRuleId()) &&
            StringUtils.isNotBlank(spJqItem4Get.getRuleId())) {
            // 之前为空，现在不为空，这种情况下，人岗匹配项目这边的规则不变，只记录warning日志
            if (DmpDimType.isText(taskDimInfo.getDimType())) {
                log.warn("LOG60010: 维度类型是文本类型，但是规则id为空，dimId:{}",
                        taskDimInfo.getJqDimId());
            }
        }
    }

    /**
     * 更新任务的维度信息
     *
     * @param orgId  机构id
     * @param taskId 任务id
     * @param cmd    任务维度信息
     * @param operator   用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void doModifyTaskDims(
            String orgId, String dmpId, String taskId, DmpTaskCreateUpdateCmd cmd,
            UserCacheDetail operator) {
        // 校验
        List<DmpTaskDimInfoDTO> dims = cmd.getDims();
        Validate.isNotEmpty(dims, ExceptionKeys.DMP_TASK_DIM_EMPTY);

        // 保存新的维度
        List<String> jqDimIds = StreamUtil.mapList(dims, DmpTaskDimInfoDTO::getJqDimId);
        List<DmpTaskDimPO> existDims = dmpTaskDimMapper.selectByTaskId(orgId, dmpId, taskId);

        Map<String, DmpTaskDimPO> existDimMap = new HashMap<>(8);
        List<DmpTaskDimPO> needDeleteDims = new ArrayList<>();
        for (DmpTaskDimPO dmpTaskDim : existDims) {
            if (!jqDimIds.contains(dmpTaskDim.getJqDimId())) {
                needDeleteDims.add(dmpTaskDim);
            } else {
                existDimMap.put(dmpTaskDim.getJqDimId(), dmpTaskDim);
            }
        }
        deleteDimByTaskId(orgId, dmpId, needDeleteDims, operator);

        handleDmpTaskDim(orgId, dmpId, taskId, cmd.getDims(), existDimMap, operator);
    }

    /**
     * 接口用于动态匹配任务第二步--维度设计，支持新增和更新
     *
     * @param orgId
     * @param taskId
     * @param dmpId
     * @param cmd
     * @param operator
     */
    @Transactional(rollbackFor = Exception.class)
    public void modifyTaskDims(
            String orgId, String taskId, String dmpId, DmpTaskCreateUpdateCmd cmd,
            UserCacheDetail operator) {

        // 将dmp状态同步到任务的状态，两者保持一致
        DmpTaskPO dmpTask = loadAndSyncDmpTaskStatus(orgId, taskId, cmd);
        EntityUtil.setUpdate(dmpTask, operator);
        dmpTaskMapper.updateById(dmpTask);

        // 处理任务维度
        doModifyTaskDims(orgId, dmpId, taskId, cmd, operator);
    }

    /**
     * 更新任务的状态,与dmp状态保持一致
     *
     * @param orgId
     * @param taskId
     * @param cmd
     */
    private DmpTaskPO loadAndSyncDmpTaskStatus(
            String orgId, String taskId, DmpTaskCreateUpdateCmd cmd) {
        DmpTaskPO dmpTask = loadDmpTask(orgId, taskId);
        DmpPO dmp = loadDmp(orgId, dmpTask.getDmpId());
        dmpTask.setTaskStatus(dmp.getDmpStatus());

        Integer activeStatus = cmd.getActiveStatus();
        // 新需求，新创建的动态匹配任务是待启用状态
        if (dmpTask.getTaskType() == 0) {
            if (cmd.getActiveStatus() == DmpTask.ActiveStatus.ENABLED.getCode()) {
                dmpTask.setActiveStatus(DmpTask.ActiveStatus.PENDING_ENABLE.getCode());
            } else if (cmd.getActiveStatus() == DmpTask.ActiveStatus.DRAFT.getCode()) {
                dmpTask.setActiveStatus(DmpTask.ActiveStatus.DRAFT.getCode());
            }
        }
        // dmpTask.setActiveStatus(activeStatus);
        if (DmpTask.ActiveStatus.isEnabled(activeStatus)) {
            dmpTask.setActiveTime(LocalDateTime.now());
        }

        return dmpTask;
    }

    private void deleteDimByTaskId(
            String orgId, String dmpId, List<DmpTaskDimPO> delDmpTaskDims, UserCacheDetail operator) {
        doDeleteDims(delDmpTaskDims, operator);

        // 删除rv_dmp_rule_score和rv_dmp_rule_score_detail
        List<String> dimIds = StreamUtil.mapList(delDmpTaskDims, DmpTaskDimPO::getId);
        List<DmpRuleScorePO> dmpRuleScores = dmpRuleScoreMapper.selectByDimIds(orgId, dimIds);
        doDeleteDmpRuleScores(dmpRuleScores, operator);

        List<String> ruleScoreIds = StreamUtil.mapList(dmpRuleScores, DmpRuleScorePO::getId);
        List<DmpRuleScoreDetailPO> dmpRuleScoreDetails =
                dmpRuleScoreDetailMapper.selectByDimIds(orgId, ruleScoreIds);
        doDeleteDmpRuleScoreDetails(dmpRuleScoreDetails, operator);

        // 删除维度下的计算结果
        dmpUserDimResultMapper.deleteByDimIds(orgId, dmpId, dimIds);
        dmpUserDimDetailMapper.deleteByDimIds(orgId, dmpId, dimIds);
    }

    private void doDeleteDmpRuleScoreDetails(
            List<DmpRuleScoreDetailPO> dmpRuleScoreDetails, UserCacheDetail operator) {
        dmpRuleScoreDetails.forEach(dmpRuleScoreDetail -> {
            dmpRuleScoreDetail.setDeleted(YesOrNo.YES.getValue());
            EntityUtil.setUpdate(dmpRuleScoreDetail, operator);
        });
        if (CollectionUtils.isNotEmpty(dmpRuleScoreDetails)) {
            dmpRuleScoreDetailMapper.updateBatch(dmpRuleScoreDetails);
        }
    }

    private void doDeleteDmpRuleScores(
            List<DmpRuleScorePO> dmpRuleScores, UserCacheDetail operator) {
        dmpRuleScores.forEach(dmpRuleScore -> {
            dmpRuleScore.setDeleted(YesOrNo.YES.getValue());
            EntityUtil.setUpdate(dmpRuleScore, operator);
        });
        if (CollectionUtils.isNotEmpty(dmpRuleScores)) {
            dmpRuleScoreMapper.updateBatch(dmpRuleScores);
        }
    }

    private void doDeleteDims(List<DmpTaskDimPO> dmpTaskDims, UserCacheDetail operator) {
        dmpTaskDims.forEach(dmpTaskDim -> {
            dmpTaskDim.setDeleted(YesOrNo.YES.getValue());
            EntityUtil.setUpdate(dmpTaskDim, operator);
        });
        dmpTaskDimMapper.batchUpdate(dmpTaskDims);
    }

    /**
     * 更新任务基本信息
     *
     * @param orgId  机构id
     * @param dmpId  项目id
     * @param taskId 任务id
     * @param cmd    任务信息
     * @param operator   用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public DmpTaskPO modifyTaskBasic(
            String orgId, String dmpId, String taskId, DmpTaskCreateUpdateCmd cmd,
            UserCacheDetail operator) {
        Validate.isNotEmpty(cmd.getDims(), ExceptionKeys.DMP_TASK_DIM_EMPTY);

        DmpPO dmp = loadDmp(orgId, dmpId);
        // 只有未发布和撤回状态可以修改任务，
        // 【注意】撤回状态理论上不允许修改原有任务，只能创建和修改新的任务，但是这里没有判断这么细
        taskCanBeCreated(dmp);
        // 已经结束的项目不能修改
        Validate.isTrue(!Dmp.Status.isFinished(dmp.getDmpStatus()),
                ExceptionKeys.DMP_FINISHED_CANNOT_MODIFY);

        // 更新任务
        DmpTaskPO dmpTask = loadAndSyncDmpTaskStatus(orgId, taskId, cmd);
        dmpTask.setTaskName(cmd.getTaskName());
        dmpTask.setRemark(cmd.getRemark());
        EntityUtil.setUpdate(dmpTask, operator);
        dmpTaskMapper.updateById(dmpTask);

        // 更新维度信息
        doModifyTaskDims(orgId, dmpId, taskId, cmd, operator);

        // 更新表单评价的关系和权重
        if (DmpTask.Type.isEvalForm(cmd.getTaskType())) {
            Validate.isNotNull(cmd.getFormConfig(), ExceptionKeys.DMP_FORM_CONFIG_EMPTY);
            CommonUtil.execAfterCommitIfHas(() -> {
                FormFacade form4Update = createFormFacade(cmd, dmp, dmpTask);
                spevalAclService.createOrUpdateForm(form4Update, orgId, operator.getUserId());
            });
            cacheFormEvalRelation(orgId, dmpId, cmd, taskId);
        }
        return dmpTask;
    }

    @Nonnull
    private DmpPO loadDmp(String orgId, String dmpId) {
        return Optional.ofNullable(dmpMapper.selectByOrgIdAndId(orgId, dmpId))
                .orElseThrow(() -> new ApiException(ExceptionKeys.DMP_NOT_EXISTED));
    }

    /**
     * 获取任职资格维度详情，用于选择维度抽屉
     *
     * @param orgId         机构id
     * @param dmpId         项目id
     * @param excludeTaskId 排除的任务id
     * @param operator
     * @return 任职资格维度详情
     */
    public DmpDimJqVO listJqDims(
            String orgId, String dmpId, String excludeTaskId, String operator) {
        DmpPO dmp = loadDmp(orgId, dmpId);
        JqReq jqReq = new JqReq();
        jqReq.setOrgId(orgId);
        jqReq.setJqId(dmp.getJqId());
        SpJqDim4Get allDims = sptalentAclService.getAllDims(jqReq);
        DmpDimJqVO dmpDimJqVO = new DmpDimJqVO();
        dmpDimJqVO.setJqId(ApiUtil.getUuid());
        dmpDimJqVO.setJqName(allDims.getJqName());
        dmpDimJqVO.setJqDimBeans(allDims.getJqDimBeans());
        dmpDimJqVO.setSkillModelDetail(allDims.getSkillModelDetail());
        dmpDimJqVO.setRtModelDetail(allDims.getRtModelDetail());
        // 设置无法选择的能力维度id
        List<String> jqDimIds = dmpTaskDimMapper.selectJqDimIds(orgId, dmpId, excludeTaskId);
        // 如果当前正在编辑的是表单任务时，不能取消选择已经绑定了表单题目的维度
        jqDimIds.addAll(getBoundFormDimIds(orgId, excludeTaskId, operator));
        dmpDimJqVO.setDisableJqDimIds(jqDimIds);
        return dmpDimJqVO;
    }

    private List<String> getBoundFormDimIds(String orgId, String dmpTaskId, String operator) {
        if (StringUtils.isBlank(dmpTaskId)) {
            return Collections.emptyList();
        }

        DmpTaskPO dmpTask = dmpTaskMapper.selectByOrgIdAndId(orgId, dmpTaskId);
        if (dmpTask == null) {
            throw new ApiException(ExceptionKeys.DMP_TASK_NOT_EXISTED);
        }
        if (isDmpTaskDeleted(dmpTask) || !DmpTask.Type.isEvalForm(dmpTask.getTaskType())) {
            return Collections.emptyList();
        }

        FormFacade formDetail =
                spevalAclService.getFormDetail(dmpTask.getEvalFormId(), orgId, operator);
        if (formDetail == null) {
            return Collections.emptyList();
        }

        List<FormDimFacade> formDimFacadeList = formDetail.getFormDimFacadeList();
        if (!CollectionUtils.isNotEmpty(formDimFacadeList)) {
            return Collections.emptyList();
        }

        return formDimFacadeList.stream()
                .filter(e -> e.getLinked() == YesOrNo.YES.getValue())
                .map(FormDimFacade::getDimId)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(
            String orgId, String dmpId, String taskId, UserCacheDetail operator) {
        DmpTaskPO dmpTask = loadDmpTask(orgId, taskId);
        Validate.isNotNull(dmpTask, ExceptionKeys.DMP_TASK_NOT_EXISTED);
        dmpTask.setDeleted(YesOrNo.YES.getValue());
        EntityUtil.setUpdate(dmpTask, operator);
        dmpTaskMapper.updateById(dmpTask);

        // 删除任务下的维度
        List<DmpTaskDimPO> dmpTaskDims = dmpTaskDimMapper.selectByTaskId(orgId, dmpId, taskId);
        deleteDimByTaskId(orgId, dmpId, dmpTaskDims, operator);

        // 删除员工任务匹配结果(rv_dmp_user_task_result)
        dmpUserTaskResultMapper.deleteByTaskId(orgId, dmpId, taskId);

        // 如果是表单评价的任务，通知删除表单
        deleteEvalForm(List.of(dmpTask), operator.getUserId());

        // 触发项目匹配结果的重新计算
        CommonUtil.execAfterCommitIfHas(() -> {
            DmpCalculator dmpCalculator = SpringContextHolder.getBean(DmpCalculator.class);
            CompletableFuture.runAsync(
                    () -> dmpCalculator.triggerCalculateDmp(orgId, dmpId, operator.getUserId()),
                    wafTaskExecutor);
        });
    }

    public void deleteEvalForm(Collection<DmpTaskPO> list, String operator) {
        CommonUtil.execAfterCommitIfHas(() -> {
            for (DmpTaskPO dmpTask : list) {
                if (DmpTask.Type.isEvalForm(dmpTask.getTaskType())) {
                    String dmpId = dmpTask.getDmpId();
                    String orgId = dmpTask.getOrgId();
                    String evalFormId = dmpTask.getEvalFormId();
                    spevalAclService.deleteForm(dmpId, orgId, evalFormId, operator);
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void revokeTask(String orgId, String dmpId, String taskId, UserCacheDetail operator) {
        switchTaskStatus(orgId, taskId, DmpTask.ActiveStatus.PENDING_REVOKE, operator);
    }

    @Transactional(rollbackFor = Exception.class)
    public void enableTask(String orgId, String dmpId, String taskId, UserCacheDetail operator) {
        switchTaskStatus(orgId, taskId, DmpTask.ActiveStatus.ENABLED, operator);
    }

    private void switchTaskStatus(
            String orgId, String taskId, DmpTask.ActiveStatus pendingRevoke, UserCacheDetail operator) {
        DmpTaskPO dmpTask = loadDmpTask(orgId, taskId);
        Validate.isNotNull(dmpTask, ExceptionKeys.DMP_TASK_NOT_EXISTED);
        dmpTask.setActiveStatus(pendingRevoke.getCode());
        EntityUtil.setUpdate(dmpTask, operator);
        dmpTaskMapper.updateById(dmpTask);
        // 如果是表单任务，通知测评
        if (DmpTask.Type.isEvalForm(dmpTask.getTaskType())) {
            CommonUtil.execAfterCommitIfHas(
                    () -> spevalAclService.formPublish(dmpTask.getEvalFormId(), orgId,
                            operator.getUserId(), pendingRevoke.getCode()));
        }
    }

    public DmpTaskUserTrackVO getTaskUser(
            String orgId, String taskId, String userId, String operator, String lang) {
        UdpLiteUserPO udpLiteUser = udpLiteUserMapper.selectByUserId(orgId, userId);
        if (udpLiteUser == null) {
            throw new ApiException(ExceptionKeys.USER_NOT_EXISTED);
        }
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang , UdpLiteUserPO.class, List.of(udpLiteUser));

        DmpTaskUserTrackVO dmpTaskUserTrackVO = new DmpTaskUserTrackVO();
        mapUdpLiteUserToTaskUser4Track(udpLiteUser, dmpTaskUserTrackVO);

        DmpTaskDTO taskInfo = findTaskInfo(orgId, taskId, operator);
        List<DmpTaskDimInfoDTO> dimInfos = taskInfo.getDimInfos();
        Validate.isNotEmpty(dimInfos, ExceptionKeys.DMP_TASK_DIM_EMPTY);

        List<String> dimIds =
                dimInfos.stream().map(DmpTaskDimInfoDTO::getId).collect(Collectors.toList());

        List<DmpUserDimResultPO> userDimResults =
                dmpUserDimResultMapper.listByTaskIdAndUserIdAndDimIds(orgId, taskId, userId,
                        dimIds);
        if (CollectionUtils.isEmpty(userDimResults)) {
            return dmpTaskUserTrackVO;
        }

        Map<String, DmpUserDimResultPO> userDimResultMap = userDimResults.stream()
                .collect(Collectors.toMap(DmpUserDimResultPO::getDimId, e -> e));

        List<DmpTaskDimInfoDTO> unMatchDims = new ArrayList<>();
        List<DmpTaskDimInfoDTO> matchDims = new ArrayList<>();

        for (DmpTaskDimInfoDTO dimInfo : dimInfos) {
            List<DmpUserDimDetailPO> dmpUserDimDetails =
                    dmpUserDimDetailMapper.selectByTaskIdAndUserIdAndDimId(orgId, taskId, userId,
                            dimInfo.getId());
            List<DmpUserDimDetailDTO> dmpUserDimDetailDtos =
                    BeanCopierUtil.convertList(dmpUserDimDetails, DmpUserDimDetailPO.class,
                            DmpUserDimDetailDTO.class);

            DmpUserDimResultPO userDimResult = userDimResultMap.get(dimInfo.getId());
            if (userDimResult != null) {
                dimInfo.setMatched(userDimResult.getMatched());
            }

            dimInfo.setUserDimDetails(dmpUserDimDetailDtos);
            if (DmpUserDimResult.MatchResult.isMatched(dimInfo.getMatched())) {
                matchDims.add(dimInfo);
            } else if (DmpUserDimResult.MatchResult.isNotMatchedOrNotMatchedException(
                    dimInfo.getMatched())) {
                unMatchDims.add(dimInfo);
            }
        }

        dmpTaskUserTrackVO.setUnMatchDims(unMatchDims);
        dmpTaskUserTrackVO.setUnMatchedDimCount(unMatchDims.size());
        dmpTaskUserTrackVO.setMatchDims(matchDims);
        dmpTaskUserTrackVO.setMatchedDimCount(matchDims.size());
        return dmpTaskUserTrackVO;
    }

    private void mapUdpLiteUserToTaskUser4Track(
            UdpLiteUserPO udpLiteUser, DmpTaskUserTrackVO dmpTaskUserTrackVO) {
        dmpTaskUserTrackVO.setUserId(udpLiteUser.getId());
        dmpTaskUserTrackVO.setUsername(udpLiteUser.getUsername());
        dmpTaskUserTrackVO.setFullname(udpLiteUser.getFullname());
        dmpTaskUserTrackVO.setImgUrl(udpLiteUser.getImgUrl());
        dmpTaskUserTrackVO.setStatus(udpLiteUser.getStatus());
        dmpTaskUserTrackVO.setDeptName(udpLiteUser.getDeptName());
        dmpTaskUserTrackVO.setPositionId(udpLiteUser.getPositionId());
        dmpTaskUserTrackVO.setPositionName(udpLiteUser.getPositionName());
    }

    public List<String> getUsedJqDimIds(String orgId, List<String> jqDimIds) {
        List<DmpTaskDimPO> dmpTaskDims = dmpTaskDimMapper.search(
                DmpTaskDimQuery.builder().orgId(orgId).jqDimIds(jqDimIds).build());
        return StreamUtil.mapList(dmpTaskDims, DmpTaskDimPO::getJqDimId);
    }

    /**
     * 获取表单任务数量
     *
     * @param orgId
     * @param dmpId
     */
    public long countFormTask(String orgId, String dmpId) {
        return dmpTaskMapper.countFormTask(orgId, dmpId);
    }

    public List<DmpTaskDimPO> findRefreshableDmpTaskDim(String orgId, String dmpId) {
        return dmpTaskDimMapper.selectRefreshableDmpTaskDim(orgId, dmpId);
    }

    public void updateDmpTaskDim(DmpTaskDimPO dmpTaskDim) {
        dmpTaskDimMapper.updateById(dmpTaskDim);
    }

    /**
     * 获取任务下人员分页列表
     *
     * @param orgId       机构id
     * @param dmpId       项目id
     * @param taskId      任务id
     * @param name        姓名
     * @param pageRequest 分页参数
     * @return 人员分页列表
     */
    public PagingList<DmpTaskUserTrackVO> getPagedTaskUsers(
            String orgId, String dmpId, String taskId, String name, PageRequest pageRequest, String lang) {
        Page<DmpUserPageVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        DmpUser4Query param = new DmpUser4Query();
        param.setSearchKey(SqlUtil.escapeSql(name));

        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        Set<String> l10nUserIds =
                l10nAclService.searchContentByKey(enableLocalization, List.of(orgId),
                        ResourceTypeEnum.USER, name);
        if(CollectionUtils.isNotEmpty(l10nUserIds)){
            param.setUserIds(new ArrayList<>(l10nUserIds));
        }
        IPage<DmpUserPageVO> datas = dmpUserMapper.pageQuery(page, dmpId, param, orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, DmpUserPageVO.class, datas.getRecords());

        Map<String, IdName> idNameMap = new HashMap<>(8);
        if(enableLocalization){
            Set<String> deptIds =
                    datas.getRecords().stream().map(DmpUserPageVO::getDeptId).collect(Collectors.toSet());
            List<IdName> idNames =
                    udpAclService.getDeptInfoByIds(
                            orgId, new ArrayList<>(deptIds));
            if (CollectionUtils.isNotEmpty(idNames)) {
                idNameMap = StreamUtil.list2map(idNames, IdName::getId);
            }
        }

        PagingList<DmpTaskUserTrackVO> result =
                BeanCopierUtil.toPagingList(datas, DmpUserPageVO.class, DmpTaskUserTrackVO.class);

        // 填充达标维度数和未达标维度数
        fillTaskUserCount(orgId, taskId, result.getDatas(), idNameMap);

        return result;
    }

    private void fillTaskUserCount(String orgId, String taskId, List<DmpTaskUserTrackVO> datas, Map<String, IdName> idNameMap) {
        if (CollectionUtils.isEmpty(datas)) {
            return;
        }

        Map<String, List<DmpUserDimResultPO>> userDimResultMap =
                getUserDimResultMap(orgId, taskId, datas);
        datas.forEach(dmpTaskUserTrackVO -> {
            List<DmpUserDimResultPO> userDimResults =
                    userDimResultMap.get(dmpTaskUserTrackVO.getUserId());
            if (userDimResults != null) {
                long matchedCount = userDimResults.stream()
                        .filter(e -> DmpUserDimResult.MatchResult.isMatched(e.getMatched()))
                        .count();
                long unMatchedCount = userDimResults.stream()
                        .filter(
                                e -> DmpUserDimResult.MatchResult.isNotMatchedOrNotMatchedException(
                                        e.getMatched()))
                        .count();
                dmpTaskUserTrackVO.setMatchedDimCount((int) matchedCount);
                dmpTaskUserTrackVO.setUnMatchedDimCount((int) unMatchedCount);
            } else {
                dmpTaskUserTrackVO.setCalculating(YesOrNo.YES.getValue());
            }

            if(MapUtils.isNotEmpty(idNameMap) && null != idNameMap.get(dmpTaskUserTrackVO.getDeptId())){
                dmpTaskUserTrackVO.setDeptName(idNameMap.get(dmpTaskUserTrackVO.getDeptId()).getName());
            }
        });
    }

    private Map<String, List<DmpUserDimResultPO>> getUserDimResultMap(
            String orgId, String taskId, List<DmpTaskUserTrackVO> datas) {
        List<String> userIds =
                datas.stream().map(DmpTaskUserTrackVO::getUserId).collect(Collectors.toList());
        List<DmpUserDimResultPO> dmpUserDimResults =
                dmpUserDimResultMapper.selectByTaskIdAndUserIds(orgId, taskId, userIds);
        return dmpUserDimResults.stream()
                .collect(Collectors.groupingBy(DmpUserDimResultPO::getUserId, Collectors.toList()));
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void editTaskName(String orgId, String dmpId, String taskId, String userId ,DmpTaskBaseEditCmd cmd) {
        DmpTaskPO dmpTaskPO = dmpTaskMapper.selectByOrgIdAndId(orgId, taskId);
        if (dmpTaskPO == null) {
            return;
        }
        dmpTaskPO.setTaskName(cmd.getTaskName());
        dmpTaskPO.setRemark(cmd.getTaskDesc());
        EntityUtil.setUpdate(dmpTaskPO, userId);
        dmpTaskMapper.updateById(dmpTaskPO);
        Integer taskType = dmpTaskPO.getTaskType();
        FormUpdateReq formUpdateReq = new FormUpdateReq();
        formUpdateReq.setFormId(dmpTaskPO.getEvalFormId());
        formUpdateReq.setFormName(cmd.getTaskName());
        formUpdateReq.setFormDesc(cmd.getTaskDesc());
        if (Objects.equals(taskType, NumberEnum.ONE.getNumber())) {
            spEvalApiFacade.updateForm(orgId, userId, formUpdateReq);
        }
    }

    /**
     * 导出生成内容
     *
     * @param orgId
     * @param dmpId
     * @param taskId
     * @param lang
     * @return
     */
    public DynamicExcelExportContent makeDynamicContent(String orgId, String dmpId, String taskId, String lang){
        DynamicExcelExportContent content = new DynamicExcelExportContent();
        List<DmpTaskDimPO> dmpTaskDimList = dmpTaskDimMapper.selectByTaskId(orgId, dmpId, taskId);

        if (CollectionUtils.isEmpty(dmpTaskDimList)) {
            throw new ApiException(ExceptionKeys.DMP_TASK_EMPTY);
        }

        Map<String, List<List<String>>> headerMap = new HashMap<>();
        List<List<String>> headers = new ArrayList<>();

        // 固定头部
        dealHeadOne(headers);
        // 记录维度和规则map
        Map<String, List<String>> dimConditonMap = new HashMap<>();

        // 动态表头
        for (DmpTaskDimPO dmpTaskDim : dmpTaskDimList) {
            String jqDimName = dmpTaskDim.getJqDimName();
            headers.add(Lists.newArrayList(jqDimName, "规则"));
            // 维度规则
            String ruleSnap = dmpTaskDim.getRuleSnap();
            LabelConditionJsonBean conditionJsonBean =
                    BeanHelper.json2Bean(ruleSnap, LabelConditionJsonBean.class);
            if (conditionJsonBean == null) {
                return new DynamicExcelExportContent();
            }
            dealHead(headers, dimConditonMap, dmpTaskDim, jqDimName, conditionJsonBean);
            headers.add(Lists.newArrayList(jqDimName, "是否达标"));
        }
        headers.add(Lists.newArrayList("总计", "达标数"));
        headers.add(Lists.newArrayList("总计", "未达标数"));
        headerMap.put(SHEET, headers);
        content.setHeaders(headerMap);

        List<IdName> sheets = new ArrayList<>();
        IdName idName = new IdName();
        idName.setId(SHEET);
        idName.setName(SHEET);
        sheets.add(idName);
        content.setSheets(sheets);

        // 学员最终数据
        List<Object> resList = new ArrayList<>();
        List<DmpUserPageVO> dmpUsers = dmpUserMapper.selectAllDmpUser(orgId, dmpId);
        List<String> userIds =
                dmpUsers.stream().map(DmpUserPageVO::getUserId).collect(Collectors.toList());

        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, DmpUserPageVO.class, dmpUsers);
        Map<String, IdName> idNameMap = new HashMap<>(8);
        if(enableLocalization){
            Set<String> deptIds =
                    dmpUsers.stream().map(DmpUserPageVO::getDeptId).collect(Collectors.toSet());
            List<IdName> idNames =
                    udpAclService.getDeptInfoByIds(
                            orgId, new ArrayList<>(deptIds));
            if (CollectionUtils.isNotEmpty(idNames)) {
                idNameMap = StreamUtil.list2map(idNames, IdName::getId);
            }
        }

        Map<String, List<Object>> dataMap = new HashMap<>();
        List<String> dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIds)) {
            dataList.add("");
            dataMap.put(SHEET, Collections.singletonList(dataList));
            content.setData(dataMap);
            return content;
        }

        List<DmpUserDimDetailPO> userDimDetails =
                dmpUserDimDetailMapper.selectByTaskIdAndUserIds(orgId, taskId, userIds);
        Map<String, List<DmpUserDimDetailPO>> userDimDetailMap = userDimDetails.stream()
                .collect(Collectors.groupingBy(DmpUserDimDetailPO::getUserId));

        List<DmpUserDimResultPO> dmpUserDimResults =
                dmpUserDimResultMapper.selectByTaskIdAndUserIds(orgId, taskId, userIds);
        Map<String, List<DmpUserDimResultPO>> userDimResultMap =
                dmpUserDimResults.stream().collect(Collectors.groupingBy(DmpUserDimResultPO::getUserId));

        dealExcelData(
                dmpTaskDimList, dimConditonMap, resList, dmpUsers, idNameMap, userDimDetailMap,
                userDimResultMap);
        dataMap.put(SHEET, resList);
        content.setData(dataMap);
        return content;
    }

    private void dealExcelData(
            List<DmpTaskDimPO> dmpTaskDimList, Map<String, List<String>> dimConditonMap,
            List<Object> resList, List<DmpUserPageVO> dmpUsers, Map<String, IdName> idNameMap,
            Map<String, List<DmpUserDimDetailPO>> userDimDetailMap,
            Map<String, List<DmpUserDimResultPO>> userDimResultMap) {
        for (DmpUserPageVO user : dmpUsers) {
            if(MapUtils.isNotEmpty(idNameMap) && null != idNameMap.get(user.getDeptId())){
                user.setDeptName(idNameMap.get(user.getDeptId()).getName());
            }
            List<String> userDatas = new ArrayList<>();
            // 人员基本信息
            // 姓名
            StringBuilder sb = new StringBuilder();
            sb.append(user.getFullname())
                    .append("(")
                    .append(user.getUsername())
                    .append(")");
            userDatas.add(sb.toString());
            if (user.getStatus() == 0) {
                userDatas.add("禁用");
            } else if (user.getStatus() == 1){
                userDatas.add("启用");
            }
            userDatas.add(user.getDeptName());
            List<DmpUserDimDetailPO> dmpUserDimDetails = userDimDetailMap.get(user.getUserId());
            Map<String, List<DmpUserDimDetailPO>> realUserDimMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(dmpUserDimDetails)) {
                realUserDimMap = dmpUserDimDetails.stream().collect(Collectors.groupingBy(DmpUserDimDetailPO::getJqDimId));
            }
            List<DmpUserDimResultPO> dimResults = userDimResultMap.get(user.getUserId());
            Map<String, DmpUserDimResultPO> dimResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(dimResults)) {
                dimResultMap = StreamUtil.list2map(dimResults, DmpUserDimResultPO::getJqDimId);
            }

            // 组装规则
            for (DmpTaskDimPO taskDim : dmpTaskDimList) {
                // 规则
                userDatas.add(makeRule(taskDim));
                // 达标结果
                List<String> conditionIds = dimConditonMap.get(taskDim.getJqDimId());
                List<DmpUserDimDetailPO> dimDetails = realUserDimMap.get(taskDim.getJqDimId());
                DmpUserDimResultPO dmpUserDimResult = dimResultMap.get(taskDim.getJqDimId());
                combiorDimValue(userDatas, conditionIds, dimDetails, dmpUserDimResult);
            }

            // 最后结果，达标数据，总数
            if (CollectionUtils.isNotEmpty(dimResults)) {
                int matchedNum =
                        (int) dimResults.stream().filter(x -> x.getMatched() == 1).count();
                int unMatchedNum =
                        (int) dimResults.stream().filter(x -> x.getMatched() == 0).count();
                userDatas.add(String.valueOf(matchedNum));
                userDatas.add(String.valueOf(unMatchedNum));
            } else {
                userDatas.add("0");
                userDatas.add("0");
            }

            resList.add(userDatas);

        }
    }

    private void dealHeadOne(List<List<String>> headers) {
        for (String key : HEADER2_KEYS) {
            List<String> headerOneList = new ArrayList<>();
            headerOneList.add(i18nComponent.getI18nValue(HEADER_PREFIX + key));
            headers.add(headerOneList);
        }
    }

    private void dealHead(
            List<List<String>> headers, Map<String, List<String>> dimConditonMap,
            DmpTaskDimPO dmpTaskDim, String jqDimName, LabelConditionJsonBean conditionJsonBean) {
        List<LabelConditionInfo> conditions = conditionJsonBean.getConditions();
        List<String> conditonIds = new ArrayList<>();
        for (LabelConditionInfo condition : conditions) {
            List<LabelRuleInfo> rules = condition.getRules();
            for (LabelRuleInfo rule : rules) {
                headers.add(Lists.newArrayList(jqDimName, rule.getName()));
                conditonIds.add(rule.getUuid());
            }
            dimConditonMap.put(dmpTaskDim.getJqDimId(), conditonIds);
        }
    }

    private void combiorDimValue(List<String> userDatas, List<String> conditionIds, List<DmpUserDimDetailPO> dimDetails,
            DmpUserDimResultPO dmpUserDimResult) {
        if (CollectionUtils.isEmpty(dimDetails) || dmpUserDimResult == null) {
            conditionIds.forEach(x -> userDatas.add("-"));
            userDatas.add("未达标");
            return;
        }
        Map<String, DmpUserDimDetailPO> dimDetailMap =
                StreamUtil.list2map(dimDetails, DmpUserDimDetailPO::getConditionId);

        for (String conditionId : conditionIds) {
            DmpUserDimDetailPO dmpUserDimDetailPO = dimDetailMap.get(conditionId);
            if (dmpUserDimDetailPO != null && StringUtils.isNotBlank(dmpUserDimDetailPO.getLabelValue())) {
                userDatas.add(dmpUserDimDetailPO.getLabelValue());
            } else {
                userDatas.add("-");
            }
        }

        userDatas.add(dmpUserDimResult.getMatched() == 1 ? "达标" : "未达标");
    }

    private String makeRule(DmpTaskDimPO taskDim) {
        StringBuffer sb = new StringBuffer();
        String ruleSnap = taskDim.getRuleSnap();
        LabelConditionJsonBean conditionJsonBean =
                BeanHelper.json2Bean(ruleSnap, LabelConditionJsonBean.class);
        Integer groupLogic = conditionJsonBean.getLogic();

        // 条件之间拼接
        List<LabelConditionInfo> conditions = conditionJsonBean.getConditions();
        int oneIndex = 1;
        int oneLen = conditions.size();
        for (LabelConditionInfo condition : conditions) {
            sb.append("【");
            Integer logic = condition.getLogic();
            List<LabelRuleInfo> rules = condition.getRules();
            int index = 1;
            int len = rules.size();
            for (LabelRuleInfo rule : rules) {
                String name = rule.getName();
                sb.append(name);
                List<LabelOperatorInfo> operators = rule.getOperators();
                if (operators.size() == 1) {
                    LabelOperatorInfo labelOperatorInfo = operators.get(0);
                    sb.append(findSymbolValue(labelOperatorInfo.getOperateType()));
                }
                String valueName = getValueName(operators , rule.getColumnType(), rule.getType());
                sb.append(valueName);
                if (index < len) {
                    sb.append(logic == 1 ? "且" : "或").append("\n");
                }
                index++;
            }
            sb.append("】");
            if (oneIndex < oneLen) {
                sb.append(groupLogic == 1 ? "且" : "或").append("\n");
            }
            oneIndex++;
        }
        return sb.toString();
    }

    // value值转换
    private String getValueName(List<LabelOperatorInfo> operators, Integer columnType, Integer type) {
        List<String> resNames = new ArrayList<>();
        if (operators.size() == 1) {
            LabelOperatorInfo labelOperatorInfo = operators.get(0);
            if (type == 1) {
                // 指标
                return getIndicatorValueName(labelOperatorInfo, columnType);

            } else {
                // 标签
                return getLableValueName(labelOperatorInfo, columnType);
            }
        }

        // 多个数组是区间
        for (LabelOperatorInfo operator : operators) {
            if (type == 1) {
                // 指标
                resNames.add(getIndicatorValueName(operator, columnType));

            } else {
                // 标签
                resNames.add(getLableValueName(operator, columnType));
            }
        }
        return "[" + String.join(",", resNames) + "]";
    }

    private String getLableValueName(LabelOperatorInfo operator, Integer columnType){
        String resName = StringUtils.EMPTY;
        Object value = operator.getValue();
        if (value == null) {
            return resName;
        }

        if (value instanceof Map) {
            DmpRuleValueDTO dmpRuleValueDTO = BeanHelper.json2Bean(
                    BeanHelper.bean2Json(value, ALWAYS),
                    DmpRuleValueDTO.class);
            resName = dmpRuleValueDTO.getName();
        } else if (value instanceof List) {
            List<DmpRuleValueDTO> dmpRuleValues =
                    BeanHelper.json2Bean(
                            BeanHelper.bean2Json(value, ALWAYS),
                            List.class,
                            DmpRuleValueDTO.class);

            List<String> collect = dmpRuleValues.stream()
                    .map(DmpRuleValueDTO::getName)
                    .collect(Collectors.toList());
            resName = String.join("、", collect);
        }

        return resName;
    }

    /**
     * 指标解析
     *
     * @param operator
     * @param columnType
     * @return
     */
    private String getIndicatorValueName(LabelOperatorInfo operator, Integer columnType) {
        String resName = StringUtils.EMPTY;
        Object value = operator.getValue();
        if (value == null) {
            return resName;
        }
        if (columnType == 0) {
            // 枚举类型
            if (value instanceof Map) {
                 DmpRuleValueDTO dmpRuleValueDTO = BeanHelper.json2Bean(
                        BeanHelper.bean2Json(value, ALWAYS),
                        DmpRuleValueDTO.class);
                 resName = dmpRuleValueDTO.getName();
            } else if (value instanceof List) {
                List<DmpRuleValueDTO> dmpRuleValues =
                        BeanHelper.json2Bean(
                                BeanHelper.bean2Json(value, ALWAYS),
                                List.class,
                                DmpRuleValueDTO.class);
                List<String> collect = dmpRuleValues.stream()
                        .map(DmpRuleValueDTO::getName)
                        .collect(Collectors.toList());
                resName = String.join("、", collect);
            }
        } else {
            if (value instanceof Number) {
                // 不论整形还是浮点型，都转成浮点型进行比较，使用BigDecimal比较浮点
                resName = String.valueOf(value);
            } else if (value instanceof String) {
                resName = (String) value;
            } else if (value instanceof Boolean tempValue) {
                resName = tempValue ? "是" : "否";
            } else if (value instanceof Date date) {
                // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
                resName = DateUtil.formatDate(date);
            } else if (value instanceof LocalDateTime localDateTime) {
                // yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd 两种格式都支持
                resName = DateTimeUtil.formatDate(localDateTime);
            }

        }

        return resName;
    }

    private String findSymbolValue(int param) {
        return switch (param) {
            case 1 -> "=";
            case 2 -> "≠";
            case 3 -> ">";
            case 4 -> "<";
            case 5 -> "≥";
            case 6 -> "≤";
            case 7 -> "包含其一";
            case 8 -> "不包含";
            case 9 -> "全部包含";
            case 10 -> "文本包含";
            case 11 -> "文本不包含";
            default -> "";
        };
    }

}
