package com.yxt.talent.rv.application.calimeet.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserInfoFill;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

import static com.yxt.talent.rv.infrastructure.service.userinfo.UserInfoFill.UserInfoFillType.USER_NAME;

/**
 * 校准人员DTO
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetUserVO extends UserBaseInfo {


    @Schema(description = "rv_calimeet_user.id")
    private String id;


    @Schema(description = "校准幅度")
    private Integer caliShift;


    @Schema(description = "校准前宫格")
    private String originalCellIndexName;


    @Schema(description = "校准后宫格")
    private String cellIndexName;

    @Schema(description = "校准人id")
    private String caliUserId;

    @UserInfoFill(userIdField = "caliUserId", type = USER_NAME)
    @Schema(description = "校准人姓名")
    private String caliUserFullName;

    @JsonIgnore
    @Schema(description = "最新的校准记录")
    private String latestRecordId;

    @Schema(description = "校准状态(0-未校准，1-已校准，2-无需缴准)")
    private Integer caliStatus;

    @JsonIgnore
    @Schema(description = "校准详情")
    private String caliDetails;

    @JsonIgnore
    @Schema(description = "结果详情")
    private String resultDetails;

    @JsonIgnore
    public String getCaliStatusStr() {
        if (caliStatus == null) {
            return "";
        }
        if (caliStatus == 0) {
            return "未校准";
        } else if (caliStatus == 1) {
            return "已校准";
        } else {
            return "无需缴准";
        }
    }

    @Schema(description = "校准时间")
    private LocalDateTime caliTime;

} 