package com.yxt.talent.rv.application.prj.user.expt;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;

import java.util.List;

/**
 * 使用模版导入盘点结果时，先导出模版
 */
public interface PrjUserResultByTplExporter {

    /**
     * 导入盘底结果模板
     *
     * @param rvUdpUsers 导入人员信息
     * @param dim        维度id
     * @param orgId      机构id
     * @return 导出文件路径
     */
    String exportUserResultByTpl(
        List<UdpLiteUserPO> rvUdpUsers, PrjDimConfPO dim, String orgId);

    /**
     * 获取导入类型
     * @return 导入类型
     */
    int getImportType();
}
