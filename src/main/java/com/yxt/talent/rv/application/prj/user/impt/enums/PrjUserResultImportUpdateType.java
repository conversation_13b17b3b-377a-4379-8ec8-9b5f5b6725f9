package com.yxt.talent.rv.application.prj.user.impt.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 数据更新方式
 */
@Getter
@RequiredArgsConstructor
public enum PrjUserResultImportUpdateType {
    /*覆盖原有数据*/
    COVER(1, "覆盖原有数据"),

    /*保留原有数据*/
    RETAIN(2, "保留原有数据");

    private final int code;
    private final String desc;

    public static boolean isValidType(int type) {
        for (PrjUserResultImportUpdateType value : PrjUserResultImportUpdateType.values()) {
            if (value.getCode() == type) {
                return true;
            }
        }
        return false;
    }
}
