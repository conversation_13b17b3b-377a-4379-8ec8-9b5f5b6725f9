package com.yxt.talent.rv.application.dmp.user.legacy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.sptalentapifacade.bean.spjq.SpJqDetail4Get;
import com.yxt.sptalentapifacade.bean.spjq.SpJqItem4Get;
import com.yxt.talent.rv.application.dmp.task.dto.DmpDimInfoDTO;
import com.yxt.talent.rv.application.org.OrgQryAppService;
import com.yxt.talent.rv.controller.manage.user.viewobj.UserProfileDmpUserDimResultVO;
import com.yxt.talent.rv.controller.openapi.viewobj.DmpUserDimResultOpenVO;
import com.yxt.talent.rv.controller.openapi.viewobj.DmpUserResultSimpleOpenVO;
import com.yxt.talent.rv.controller.openapi.viewobj.PrjUserResultOpenVO;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserDimResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserResultPO;
import com.yxt.talent.rv.infrastructure.service.remote.SptalentAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SptalentAclServiceImpl;
import com.yxt.talentrvfacade.bean.DmpDimInfo4Facade;
import com.yxt.talentrvfacade.bean.UserDimResult4Facade;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class DmpUserResultAppService {

    private final DmpMapper dmpMapper;
    private final DmpRuleMapper dmpRuleMapper;
    private final DmpUserResultMapper dmpUserResultMapper;
    private final DmpUserDimResultMapper dmpUserDimResultMapper;
    private final DmpTaskDimMapper dmpTaskDimMapper;
    private final OrgQryAppService orgQryAppService;
    private final AppProperties appProperties;
    private final SptalentAclService sptalentAclService;

    @Nonnull
    private static UserProfileDmpUserDimResultVO buildUserDimResult4Get(
            DmpPO dmp, DmpRulePO dmpRule) {
        UserProfileDmpUserDimResultVO userDimResult = new UserProfileDmpUserDimResultVO();
        userDimResult.setDmpId(dmp.getId());
        userDimResult.setStartTime(dmp.getStartTime());
        userDimResult.setEndTime(dmp.getEndTime());
        userDimResult.setMatchType(dmpRule.getMatchType());
        return userDimResult;
    }

    @Nonnull
    private static UserDimResult4Facade buildUserDimResult4Facade(
            UserProfileDmpUserDimResultVO data) {
        UserDimResult4Facade facade = new UserDimResult4Facade();
        facade.setUserId(data.getUserId());
        facade.setDmpId(data.getDmpId());
        facade.setStartTime(data.getStartTime());
        facade.setEndTime(data.getEndTime());
        facade.setMatchType(data.getMatchType());
        facade.setScore(data.getScore());
        facade.setLayerId(data.getLayerId());
        facade.setLayerName(data.getLayerName());
        return facade;
    }

    /**
     * 人岗匹配项目-员工维度盘点
     *
     * @param orgId
     * @param searchStartTime
     * @param searchEndTime
     * @param pageRequest
     */
    public IPage<DmpUserDimResultOpenVO> getUserDimResults(
            String orgId, String searchStartTime, String searchEndTime, PageRequest pageRequest) {
        Page<PrjUserResultOpenVO> page =
                new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        return dmpUserDimResultMapper.selectPage(page, orgId, searchStartTime, searchEndTime);
    }

    /**
     * 人岗匹配项目-员工结果盘点
     *
     * @param orgId
     * @param searchStartTime
     * @param searchEndTime
     * @param pageRequest
     */
    public IPage<DmpUserResultSimpleOpenVO> getUserResults(
            String orgId, String searchStartTime, String searchEndTime, PageRequest pageRequest) {
        Page<PrjUserResultOpenVO> page =
                new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        return dmpUserResultMapper.selectUserResultPage(
                page, orgId, searchStartTime, searchEndTime);
    }

    /**
     * 个人画像-岗位胜任度
     *
     * @param orgId
     * @param userId
     */
    @Nullable
    public UserProfileDmpUserDimResultVO getUserDimResult(String orgId, String userId) {
        if (orgId == null || userId == null) {
            throw new IllegalArgumentException("orgId and userId cannot be null");
        }

        // demo模版机构，查询个人画像-人岗匹配的时候忽略岗位一致性判断，减小产品运营造演示数据的复杂度  FK
        String demoOrgId = orgQryAppService.demoCopyOrgId(orgId);
        boolean isDemoOrg = Objects.equals(appProperties.getDemoOrgId(), demoOrgId);

        // 最近一条与学员当前岗位匹配的盘点项目
        DmpPO lastDmp = dmpMapper.selectLatestDmpByUserId(orgId, userId, isDemoOrg);
        if (lastDmp == null) {
            log.warn("LOG60730:latest dmp not exist, orgId={}, userId={}", orgId, userId);
            return null;
        }
        log.debug("LOG61330:[{}] [{}]", lastDmp.getId(), lastDmp.getDmpName());

        CompletableFuture<DmpRulePO> dmpMatchRuleFuture =
                CompletableFuture.supplyAsync(() -> getDmpMatchRule(orgId, lastDmp.getId()));
        CompletableFuture<DmpUserResultPO> userResultFuture = CompletableFuture.supplyAsync(
                () -> dmpUserResultMapper.selectByDmpIdAndUserId(orgId, lastDmp.getId(), userId));
        CompletableFuture<List<DmpUserDimResultPO>> userDimResultsFuture =
                CompletableFuture.supplyAsync(
                        () -> getDmpUserDimResults(orgId, lastDmp.getId(), userId));

        CompletableFuture<UserProfileDmpUserDimResultVO> results =
                CompletableFuture.completedFuture(lastDmp)
                        .thenCombineAsync(dmpMatchRuleFuture,
                                DmpUserResultAppService::buildUserDimResult4Get)
                        .thenCombineAsync(userResultFuture, (userDimResult, userResult) -> {
                            if (userResult != null) {
                                userDimResult.setUserId(userResult.getUserId());
                                userDimResult.setLayerId(userResult.getLayerId());
                                userDimResult.setLayerName(userResult.getLayerName());
                                userDimResult.setScore(userResult.getScore());
                                userDimResult.setLatestCalcTime(userResult.getCreateTime());
                            }
                            return userDimResult;
                        })
                        .thenCombineAsync(userDimResultsFuture, (userDimResult, userDimResults) -> {
                            if (CollectionUtils.isNotEmpty(userDimResults)) {
                                fillMatchedAndUnmatchedDims(
                                        userDimResult, userDimResults, orgId, lastDmp);
                            }
                            return userDimResult;
                        });

        return results.join();
    }

    private void fillMatchedAndUnmatchedDims(
            UserProfileDmpUserDimResultVO userDimResult, List<DmpUserDimResultPO> userDimResults,
            String orgId, DmpPO dmp) {
        List<String> dimIds = getDimIds(userDimResults);
        Map<String, SpJqItem4Get> jqItem4GetMap = getJqItemMap(dimIds, orgId, dmp);

        List<DmpDimInfoDTO> matchedDims = new ArrayList<>();
        List<DmpDimInfoDTO> unMatchedDims = new ArrayList<>();
        for (DmpUserDimResultPO result : userDimResults) {
            DmpDimInfoDTO dimInfo = new DmpDimInfoDTO();
            dimInfo.setDimId(result.getJqDimId());
            dimInfo.setFormScore(result.getFormScore());
            if (result.getMatched() == YesOrNo.YES.getValue()) {
                matchedDims.add(dimInfo);
            } else {
                unMatchedDims.add(dimInfo);
            }
        }
        fillDimOtherInfo(matchedDims, jqItem4GetMap);
        fillDimOtherInfo(unMatchedDims, jqItem4GetMap);

        userDimResult.setMatchedDims(matchedDims);
        userDimResult.setUnMatchedDims(unMatchedDims);
    }

    private DmpRulePO getDmpMatchRule(String orgId, String dmpId) {
        DmpRulePO dmpRule = dmpRuleMapper.selectRuleByDmpId(orgId, dmpId);
        if (dmpRule == null) {
            throw new ApiException("apis.sptalentrv.dmp.matchRule.notExist");
        }
        return dmpRule;
    }

    private List<DmpUserDimResultPO> getDmpUserDimResults(
            String orgId, String dmpId, String userId) {
        return dmpUserDimResultMapper.selectByDmpIdAndUserId(orgId, dmpId, userId);
    }

    private List<String> getDimIds(List<DmpUserDimResultPO> dmpUserDimResults) {
        return dmpUserDimResults.stream()
                .map(DmpUserDimResultPO::getDimId)
                .collect(Collectors.toList());
    }

    private Map<String, SpJqItem4Get> getJqItemMap(
            List<String> dimIds, String orgId, DmpPO dmp) {
        List<DmpTaskDimPO> dmpTaskDims = dmpTaskDimMapper.selectByIds(orgId, dimIds);
        SpJqDetail4Get spJqDetail = sptalentAclService.getSpJqDetail4Get(dmpTaskDims, orgId, dmp);
        List<SpJqItem4Get> jqSettingList = spJqDetail != null ? spJqDetail.getJqSetting4GetList() : new ArrayList<>();
        return jqSettingList.stream()
                .collect(Collectors.toMap(SpJqItem4Get::getDimId, Function.identity()));
    }

    private void fillDimOtherInfo(
            List<DmpDimInfoDTO> dimInfos, Map<String, SpJqItem4Get> jqItem4GetMap) {
        dimInfos.forEach(dimInfo -> {
            SpJqItem4Get spJqItem = jqItem4GetMap.get(dimInfo.getDimId());
            if (spJqItem != null) {
                dimInfo.setDimName(spJqItem.getDimName());
                dimInfo.setJqCataId(String.valueOf(spJqItem.getRootCategoryId()));
                dimInfo.setJqCataName(spJqItem.getRootCategoryName());
                dimInfo.setJqTplCataId(String.valueOf(spJqItem.getTplCatalogId()));
                dimInfo.setJqTplCataName(spJqItem.getTplCatalogName());
            }
        });
    }

    public CommonList<UserDimResult4Facade> getUserDimResults(String orgId, List<String> userIds) {
        List<UserProfileDmpUserDimResultVO> datas = userIds.stream()
                .map(userId -> this.getUserDimResult(orgId, userId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<UserDimResult4Facade> results =
                datas.stream().map(this::buildFacadeFromData).collect(Collectors.toList());

        return new CommonList<>(results);
    }

    private UserDimResult4Facade buildFacadeFromData(UserProfileDmpUserDimResultVO data) {
        UserDimResult4Facade facade = buildUserDimResult4Facade(data);
        List<DmpDimInfo4Facade> dims =
                Stream.concat(data.getMatchedDims().stream().map(dim -> convertToFacade(dim, true)),
                                data.getUnMatchedDims().stream().map(dim -> convertToFacade(dim, false)))
                        .collect(Collectors.toList());

        Map<String, List<DmpDimInfo4Facade>> dimMap = createDimMap(dims);
        facade.setDimMap(dimMap);
        return facade;
    }

    private Map<String, List<DmpDimInfo4Facade>> createDimMap(List<DmpDimInfo4Facade> dims) {
        Map<String, List<DmpDimInfo4Facade>> dimMap = dims.stream()
                .filter(e -> StringUtils.isNotBlank(e.getJqCataName()))
                .collect(Collectors.groupingBy(DmpDimInfo4Facade::getJqCataName));

        dimMap.forEach((s, dmpDimInfo4Facades) -> dmpDimInfo4Facades.sort(
                Comparator.comparing(DmpDimInfo4Facade::getMatched).reversed()));

        return dimMap;
    }

    // 将 DmpDimInfo 转换为 DmpDimInfo4Facade
    private DmpDimInfo4Facade convertToFacade(DmpDimInfoDTO dim, boolean matched) {
        DmpDimInfo4Facade dimInfo4Facade = new DmpDimInfo4Facade();
        dimInfo4Facade.setDimId(dim.getDimId());
        dimInfo4Facade.setDimName(dim.getDimName());
        // 从sptalent获取维度模版分类信息
        dimInfo4Facade.setJqCataId(dim.getJqTplCataId());
        dimInfo4Facade.setJqCataName(dim.getJqTplCataName());
        dimInfo4Facade.setMatched(matched ? YesOrNo.YES.getValue() : YesOrNo.NO.getValue());
        dimInfo4Facade.setFormScore(dim.getFormScore());
        return dimInfo4Facade;
    }
}
