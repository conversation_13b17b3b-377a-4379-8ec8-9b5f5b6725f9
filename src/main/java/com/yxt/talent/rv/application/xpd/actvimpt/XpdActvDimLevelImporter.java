package com.yxt.talent.rv.application.xpd.actvimpt;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.I18nComponent;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.talent.rv.application.activity.expt.ImportDimLevelErrStrategy;
import com.yxt.talent.rv.application.activity.impt.ActDimImportProcessedResult;
import com.yxt.talent.rv.application.xpd.common.dto.DimResultImportDTO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.cmd.XpdImportActCmd;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportResultVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.file.EasyExcelListener;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImportSupport;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImporter;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileReader;
import com.yxt.talent.rv.infrastructure.service.remote.FileAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.text.MessageFormat.format;
import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2024/12/21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XpdActvDimLevelImporter extends
    FileImporter<DimResultImportDTO, ActDimImportProcessedResult, ImportResultVO> {
    private final I18nComponent i18nComponent;
    private final XpdImportMapper xpdImportMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final XpdGridLevelMapper gridLevelMapper;
    private final XpdImportDimUserMapper importDimUserMapper;
    private final ImportDimLevelErrStrategy importDimLevelErrStrategy;
    private final XpdImportLogMapper importLogMapper;
    private final XpdDimRuleMapper dimRuleMapper;
    private final SpsdAclService spsdAclService;
    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;


    private final String END_SYMBOL = ";";
    private final XpdMapper xpdMapper;


    public ImportResultVO actDimLevelImport(UserCacheDetail userCache, XpdImportActCmd cmd, String fileId, MultipartFile file) {

        String orgId = userCache.getOrgId();
        String operator = userCache.getUserId();
        String lockKey = String.format(RedisKeys.LK_XPD_ACT_IMPORT, userCache.getUserId(), userCache.getOrgId());
        XpdImportPO xpdImport = xpdImportMapper.selectByPrimaryKey(cmd.getImportId());
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdImport.getXpdId());
        cmd.setXpdId(xpd.getId());
        // 获取维度名称 作为sheetName
        List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), Lists.newArrayList(xpdImport.getSdDimId()));
        String dimName = "";
        if (CollectionUtils.isNotEmpty(dimInfoList)) {
            dimName = dimInfoList.get(0).getDmName();
        }

        Function<List<DimResultImportDTO>, ActDimImportProcessedResult> dataProcessor =
            importDataList -> dataDimLevelProcess(importDataList, orgId, operator, cmd);

        FileImportSupport<DimResultImportDTO, ActDimImportProcessedResult, ImportResultVO>
            fileImportSupport =
            FileImportSupport.<DimResultImportDTO, ActDimImportProcessedResult, ImportResultVO>builder()
                .file(file)
                .fileId(fileId)
                .tranId(lockKey)
                .startRow(1)
                .orgId(orgId)
                .operator(operator)
                .importContentClazz(DimResultImportDTO.class)
                .dataReader(new XpdActvDimLevelImporter.DimLevelFileReader(file, fileId, dimName))
                .dataProcessor(dataProcessor)
                .outputStrategy(importDimLevelErrStrategy)
                .errorFileName(getErrFileName(xpdImport))
                .resultProcessor(this::generateImportResult)
                .build();
        return toImport(fileImportSupport);

    }

    private ImportResultVO generateImportResult(ActDimImportProcessedResult processedResult) {
        List<DimResultImportDTO> failedData = processedResult.getFailedData();
        List<DimResultImportDTO> successData = processedResult.getSuccessData();

        String errorFilePath =
            Optional.ofNullable(processedResult.getErrorFilePath()).orElse(EMPTY);
        ImportResultVO res = new ImportResultVO();
        res.setFailCount(failedData.size());
        if (failedData.size() > 0) {
            res.setErrorCode(2);
        } else {
            res.setErrorCode(0);
        }
        res.setSuccessCount(successData.size());
        res.setErrorFileUrl(errorFilePath);
        return res;
    }



    private ActDimImportProcessedResult dataDimLevelProcess(
        List<DimResultImportDTO> importDataList, String orgId, String userId, XpdImportActCmd cmd) {
        if (CollectionUtils.isEmpty(importDataList)) {
            return ActDimImportProcessedResult.builder().failedData(Lists.newArrayList()).successData(Lists.newArrayList()).repeatCount(0)
                .totalData(importDataList)
                .build();
        }
        // 获取维度等级
        XpdImportPO xpdImport = xpdImportMapper.selectByPrimaryKey(cmd.getImportId());
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByXpdId(orgId, xpdImport.getXpdId());
        Map<String, String> gridLevelMap =
            StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getLevelName, XpdGridLevelPO::getId);
        List<String> userNames = importDataList.stream()
            .map(DimResultImportDTO::getUserName)
            .filter(StringUtils::isNotBlank)
            .toList();

        List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserNames(orgId, userNames);
        Map<String, String> userMap = StreamUtil.list2map(users, UdpLiteUserPO::getUsername, UdpLiteUserPO::getId);
        // 获取盘点项目中人员
        XpdPO xpd = xpdMapper.selectById(xpdImport.getXpdId());
        List<String> prjUserIds =
            rvActivityParticipationMemberMapper.findAllUserIdByActId(orgId, xpd.getAomPrjId());
        // 处理重复的账号
        Set<String> setUsers = new HashSet<>();
        Set<String> repeatUsers = new HashSet<>();
        for (String userName : userNames) {
            if (!setUsers.add(userName)) {
                repeatUsers.add(userName);
            }
        }


        for (DimResultImportDTO resultImport : importDataList) {
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isBlank(resultImport.getUserName())) {
                sb.append(getI18Msg("apis.sptalentrv.xpd.act.import.username.empty")).append(END_SYMBOL);
            } else {
                if (repeatUsers.contains(resultImport.getUserName())) {
                    sb.append(getI18Msg("apis.sptalentrv.xpd.act.import.username.repeat")).append(END_SYMBOL);
                } else {
                    String tempUserId = userMap.get(resultImport.getUserName());
                    if (tempUserId == null) {
                        sb.append(getI18Msg("apis.sptalentrv.xpd.act.import.username.error")).append(END_SYMBOL);
                    } else if (!prjUserIds.contains(tempUserId)) {
                        sb.append(getI18Msg("apis.sptalentrv.xpd.act.import.username.prj.not.exist")).append(END_SYMBOL);
                    } else {
                        resultImport.setUserId(tempUserId);
                    }
                }


            }

            if (StringUtils.isBlank(resultImport.getDimLevel())) {
                sb.append(getI18Msg("apis.sptalentrv.xpd.act.import.level.name.empty")).append(END_SYMBOL);
            } else {
                String gridLevelId = gridLevelMap.get(resultImport.getDimLevel());
                if (StringUtils.isBlank(gridLevelId)) {
                    sb.append(getI18Msg("apis.sptalentrv.xpd.act.import.level.name.error")).append(END_SYMBOL);
                } else {
                    resultImport.setGridLevelId(gridLevelId);
                }
            }
            if (!sb.isEmpty()) {
                resultImport.setErrorMsg(sb.toString());
            }
        }

        // 保存
        List<DimResultImportDTO> successList =
            importDataList.stream().filter(x -> StringUtils.isBlank(x.getErrorMsg())).toList();

        List<DimResultImportDTO> failList =
            importDataList.stream().filter(x -> StringUtils.isNotBlank(x.getErrorMsg())).toList();

        saveData(successList, userId, cmd);

        return ActDimImportProcessedResult.builder().failedData(failList).successData(successList).repeatCount(0)
            .totalData(importDataList)
            .build();

    }

    private void saveData(List<DimResultImportDTO> successList, String operator, XpdImportActCmd cmd){
        if (CollectionUtils.isEmpty(successList)) {
            return;
        }
        XpdImportPO xpdImport = xpdImportMapper.selectByPrimaryKey(cmd.getImportId());
        String orgId = xpdImport.getOrgId();
        String xpdId = xpdImport.getXpdId();
        String importId = cmd.getImportId();

        Set<String> userIds = successList.stream().map(DimResultImportDTO::getUserId).collect(Collectors.toSet());
        // 找到已经存在的数据
        List<XpdImportDimUserPO> existImportDimUsers =
            importDimUserMapper.getByUserIds(orgId, xpdImport.getXpdId(), xpdImport.getSdDimId(), xpdImport.getId(),
                userIds);
        Map<String, XpdImportDimUserPO> importDimUserMap =
            StreamUtil.list2map(existImportDimUsers, XpdImportDimUserPO::getUserId);

        List<XpdImportDimUserPO> addList = new ArrayList<>();
        List<XpdImportDimUserPO> updateList = new ArrayList<>();

        for (DimResultImportDTO resultImportDTO : successList) {
            XpdImportDimUserPO existImportDimUser = importDimUserMap.get(resultImportDTO.getUserId());
            if (existImportDimUser == null) {
                XpdImportDimUserPO importDimUser = new XpdImportDimUserPO();
                importDimUser.setId(ApiUtil.getUuid());
                importDimUser.setOrgId(orgId);
                importDimUser.setImportId(importId);
                importDimUser.setXpdId(xpdId);
                importDimUser.setUserId(resultImportDTO.getUserId());
                importDimUser.setSdDimId(xpdImport.getSdDimId());
                importDimUser.setGridLevelId(resultImportDTO.getGridLevelId());
                importDimUser.setDeleted(0);
                EntityUtil.setAuditFields(importDimUser, operator);
                addList.add(importDimUser);
            } else {
                if (cmd.getCover() == 0){
                    existImportDimUser.setGridLevelId(resultImportDTO.getGridLevelId());
                    EntityUtil.setAuditFields(existImportDimUser, operator);
                    updateList.add(existImportDimUser);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(addList)) {
            importDimUserMapper.insertBatch(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            importDimUserMapper.batchUpdate(updateList);
        }

        // 将该维度的规则设置为失效
        dimRuleMapper.setRuleDisable(orgId, xpdId, xpdImport.getSdDimId(), operator);
        // 生成导入记录表
        XpdImportLogPO importLogPO = new XpdImportLogPO();
        importLogPO.setId(ApiUtil.getUuid());
        importLogPO.setOrgId(orgId);
        importLogPO.setImportId(importId);
        importLogPO.setXpdId(xpdId);
        importLogPO.setImportTime(LocalDateTime.now());
        importLogPO.setImportFile("");
        importLogPO.setDeleted(0);
        EntityUtil.setAuditFields(importLogPO, operator);

        importLogMapper.insert(importLogPO);
    }

    private String getI18Msg(String param){
        return i18nComponent.getI18nValue(param);
    }


    private String getErrFileName(XpdImportPO xpdImport) {
        if (xpdImport.getImportType() == 0) {
            // 导入维度指标明细
            return i18nComponent.getI18nValue("apis.sptalentrv.xpd.import.dim.indicator.errFile")
                   + DateTimeUtil.dateToString(
                new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS) +
                   FileConstants.FILE_SUFFIX_XLSX;
        } else {
            // 维度指标
            return i18nComponent.getI18nValue("apis.sptalentrv.xpd.import.dim.result.errFile")
                   + DateTimeUtil.dateToString(
                new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS) +
                   FileConstants.FILE_SUFFIX_XLSX;
        }
    }

    @RequiredArgsConstructor
    private class DimLevelFileReader extends FileReader<DimResultImportDTO> {

        private final MultipartFile file;

        private final String fileId;

        private final String dimName;

        @Override
        @Nonnull
        public List<DimResultImportDTO> read() {
            return doReadExcel();
        }

        /**
         * 由于导入文件中有部分表头是动态生成的, 所以这里没办法使用静态导入的方式, 而是采用动态解析的方式来处理导入的数据
         */
        @Nonnull
        private List<DimResultImportDTO> doReadExcel() {
            ExcelReader excelReader = null;
            List<DimResultImportDTO> importDataList;
            try (
                InputStream inputStream = getInputStream(file, fileId)
            ) {
                excelReader = EasyExcelFactory.read(inputStream).build();
                ReadSheet sheet = excelReader.excelExecutor().sheetList().get(0);

                // 读取sheet
                EasyExcelListener listener = new EasyExcelListener();
                sheet.setCustomReadListenerList(Collections.singletonList(listener));
                sheet.setHeadRowNumber(2);
                sheet.setAutoTrim(true);
                excelReader.read(sheet);

                // 提取Excel数据
                importDataList = generateImportList(listener);
            } catch (IOException e) {
                log.error("DimLevelFileReader error={}", e);
                throw new ApiException(e.getMessage());
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
            }
            return importDataList;
        }

    }

    private List<DimResultImportDTO> generateImportList(EasyExcelListener listener){
        List<Map<Integer, String>> list = listener.getData();
        List<DimResultImportDTO> importList = new ArrayList<>();
        for (Map<Integer, String> map : list) {
            int index  = 0;
            DimResultImportDTO result = new DimResultImportDTO();
            result.setFullName(map.get(index++));
            result.setUserName(map.get(index++));
            result.setDimLevel(map.get(index++));
            importList.add(result);
        }
        return importList;

    }


    @jakarta.annotation.Nonnull
    private InputStream getInputStreamChkSheetName(MultipartFile file, String fileId) throws IOException {
        if (Objects.nonNull(file)) {
            return file.getInputStream();
        } else if (fileId != null) {

            return getFileStreamFromFileCenter(fileId);
        } else {
            throw new ApiException("无效的文件输入");
        }
    }

    @jakarta.annotation.Nonnull
    protected InputStream getFileStreamFromFileCenter(String fileId) throws IOException {
        String downloadUrl = getFileAclService().getDownloadUrl(fileId,
            ExceptionKeys.TRANSFER_IMPORT_FILE_ID_INVALID);

        if (downloadUrl == null) {
            throw new IOException(format("fileId [{0}] 没有找到对应的下载链接!", fileId));
        }
        InputStream remoteInputStream = ExcelUtils.getRemoteInputStream(downloadUrl);
        return remoteInputStream;
    }

    @jakarta.annotation.Nonnull
    protected FileAclService getFileAclService() {
        FileAclService fileAclService = SpringContextHolder.getBean(FileAclService.class);
        Validate.isNotNull(fileAclService, "fileAclService is null");
        return fileAclService;
    }

}
