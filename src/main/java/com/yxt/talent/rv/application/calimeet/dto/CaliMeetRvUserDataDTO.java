package com.yxt.talent.rv.application.calimeet.dto;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 校准导入数据处理完的数据集合
 *
 * <AUTHOR>
 * @Date 2024/7/23 17:09
 **/

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetRvUserDataDTO {

    private List<CaliMeetRvUserImportDTO> importDataList = new ArrayList<>();

    /**
     * 验证后有问题的数据 *
     */
    private List<CaliMeetRvUserImportDTO> failedList = new ArrayList<>();

    /**
     * 无问题的就绪数据 *
     */
    private List<CaliMeetRvUserImportDTO> prepareList = new ArrayList<>();

    /**
     * 待更新用户 udp信息 *
     */
    private List<UdpLiteUserPO> udpLiteUsers = new ArrayList<>();

}
