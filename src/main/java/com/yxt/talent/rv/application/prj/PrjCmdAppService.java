package com.yxt.talent.rv.application.prj;

import com.yxt.ApplicationCommandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class PrjCmdAppService {

//    private final UserTransferComponent userTransferComponent;
//    private final CoreAclService coreAclService;
//    private final ActivityService activityService;
//
//    /**
//     * 进行资源转移
//     *
//     * @param orgId
//     * @param fromUserId
//     * @param toUserId
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void transferResource(String orgId, String fromUserId, String toUserId) {
//        int cnt = activityService.countOwnedActivity(UacdTypeEnum.PRJ_XPD.getRegId(), orgId, fromUserId);
//        userTransferComponent.transferResource4Xpd(orgId, fromUserId, toUserId,
//            cnt, () -> {
//                    activityService.changeActivityOwner(UacdTypeEnum.PRJ_XPD.getRegId(), orgId, fromUserId, toUserId, "udp_mq");
//                    /*prjMapper.transferResource(orgId, fromUserId, toUserId);
//                    prjMgrMapper.transferResourceStep1(orgId, fromUserId, toUserId);
//                    prjMgrMapper.transferResourceStep2(orgId, fromUserId, toUserId);*/
//
//                    // 将负责人添加到业务角色中
//                    coreAclService.addRoleUserByCode(CommonUtil.PRODUCT_CODE,
//                            ROLE_CODE_TALENT_REVIEW_MANAGE, Set.of(toUserId), orgId,
//                            "transfer_resource", "transfer_resource");
//                }, TRANSFERABLE_RESOURCES_CODE_PRJ);
//    }

}
