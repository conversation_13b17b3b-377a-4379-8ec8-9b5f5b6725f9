package com.yxt.talent.rv.application.org.profile.model;

import com.yxt.talent.rv.application.org.profile.SpmodelTableConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Setter
@Getter
@SqlUtil.SqlTable(SpmodelTableConstants.DWD_USER_PRJ_DETAIL)
@Schema(description = "盘点项目实体")
public class PrjUserDetailDTO {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "机构ID")
    private String orgId;

    @Schema(description = "三方用户ID")
    private String thirdUserId;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "盘点项目ID")
    private String projectId;

    @Schema(description = "盘点项目名称")
    private String projectName;

    @Schema(description = "维度ID")
    private String dimensionId;

    @Schema(description = "维度名称")
    private String dimensionName;

    @Schema(description = "类型（1:绩效 2:能力，3:潜力，4-机构自定义）")
    private Byte dimensionType;

    @Schema(description = "项目盘点结果（0-默认，1-低，2-中，3-高）")
    private Byte calibrationLevel;

    @Schema(description = "项目盘点得分")
    private BigDecimal calibrationScore;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @SqlUtil.SqlOrder(order = 1, direction = SqlUtil.SqlOrder.DESC)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @SqlUtil.SqlIgnore
    @Schema(description = "第三方部门id")
    private String thirdDeptId;

    @SqlUtil.SqlIgnore
    @Schema(description = "第三方部门id")
    private String deptId;
}
