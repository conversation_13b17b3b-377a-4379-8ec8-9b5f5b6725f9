package com.yxt.talent.rv.application.dmp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpConfDTO {

    @Schema(description = "项目配置id")
    private String confId;

    @Schema(description = "发布方式（0-手动发布 1-定时发布）")
    private int launchType = 0;

    @Schema(description = "定时发布时间类型（0-按开始时间 1-自定义时间）")
    private int launchTimeType;

    @Schema(description = "定时发布时设置的自定义发布时间")
    private LocalDateTime launchTime;

    @Schema(description = "结束方式（0-手动结束 1-定时结束）")
    private int stopType;

    @Schema(description = "结束时间类型（0-按结束时间 1-自定义时间），定时结束时有值")
    private int stopTimeType;

    @Schema(description = "项目结束时间，结束类型为定时结束且自定义时间时有值")
    private LocalDateTime stopTime;

    @Schema(description = "结束时间到期后，不允许学员继续参与评价：0-否 1-是")
    private Integer disableEval = 0;
}
