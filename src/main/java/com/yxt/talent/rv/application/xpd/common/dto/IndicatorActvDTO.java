package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "指标对应的活动列表")
public class IndicatorActvDTO {

    @Schema(description = "指标id")
    private String sdIndicatorId;

    @Schema(description = "对应的活动列表，及其指标在活动中的总分")
    private List<ActivityInfoDTO> actvList;
}

