package com.yxt.talent.rv.application.calimeet.legacy;

import com.yxt.ApplicationCommandService;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.calimeet.CaliMeetMsgSender;
import com.yxt.talent.rv.application.common.legacy.AttachmentAppService;
import com.yxt.talent.rv.application.prj.calc.PrjCalculator;
import com.yxt.talent.rv.application.prj.dim.legacy.PrjDimConfAppService;
import com.yxt.talent.rv.application.prj.rule.dto.PrjDimRuleDTO;
import com.yxt.talent.rv.application.prj.rule.dto.UserMaxScoreParamDTO;
import com.yxt.talent.rv.application.prj.rule.dto.UserScoreLevelResultDTO;
import com.yxt.talent.rv.application.prj.rule.legacy.PrjDimRuleAppService;
import com.yxt.talent.rv.application.prj.rule.legacy.PrjDimRuleCondAppService;
import com.yxt.talent.rv.controller.common.viewobj.BigDecimalRangeVO;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetCreateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserUpdateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetCreateResultVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserResultVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetVO;
import com.yxt.talent.rv.domain.common.Attachment;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjImptType;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleCondPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.domain.calimeet.calimeet.CaliMeetAttendee.UserTypeEnum.ORGANIZER;
import static com.yxt.talent.rv.domain.calimeet.calimeet.CaliMeetAttendee.UserTypeEnum.TALENT_COMMITTEE;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.CALI_MEET_NAME_EXISTED;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.CALI_MEET_NOT_EXISTED;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.CALI_MEET_USER_NOT_EXISTED;


@Slf4j
@Component
@Deprecated(since = "5.2")
@RequiredArgsConstructor
@ApplicationCommandService
public class CaliMeetAppManage {
    private final CaliMeetAppService caliMeetAppService;
    private final AttachmentAppService attachmentAppService;
    private final CaliMeetMsgSender caliMeetMsgSender;
    private final PrjCalculator prjCalculator;
    private final PrjDimRuleAppService prjDimRuleAppService;
    private final PrjDimRuleCondAppService prjDimRuleCondAppService;
    private final PrjDimConfAppService prjDimConfAppService;
    private final CaliMeetUserResultMapper caliMeetUserResultMapper;
    private final CaliMeetMapper caliMeetMapper;
    private final XpdDimMapper xpdDimMapper;

    @DbHintMaster
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public CaliMeetCreateResultVO saveCaliMeet(
            UserCacheDetail operator, CaliMeetCreateCmd command, String token) {

        String operatorUserId = operator.getUserId();
        String orgId = operator.getOrgId();
        String calimeetId = command.getId();
        String calimeetName = command.getMeetName();
        String projectId = command.getProjectId();
        // 检查是否设置了盘点规则
        List<XpdDimPO> dimPOS = xpdDimMapper.listByXpdId(orgId, projectId);
        if (CollectionUtils.isEmpty(dimPOS)){
            throw new ApiException(ExceptionKeys.XPD_DIMIDS_EMPTY);
        }
        int count = caliMeetAppService.countByOrgIdAndProjectIdAndMeetNameAndIdNot(orgId, projectId, calimeetName, calimeetId);
        Validate.isTrue(count <= 0, CALI_MEET_NAME_EXISTED);

        // 保存基本信息
        CaliMeetCreateResultVO result =
                caliMeetAppService.saveOrUpdateBasic(command, operatorUserId, orgId);

        // 保存会议组织者,允许清空
        caliMeetAppService.saveBatchDelOldBy(command.getOrganizerList(), ORGANIZER, result.getId(),
                operatorUserId, orgId);

        // 保存参会的人才委员会人员,允许清空
        caliMeetAppService.saveBatchDelOldBy(command.getTalentCommitteeList(), TALENT_COMMITTEE,
                result.getId(), operatorUserId, orgId);

        // 保存附件
        if (StringUtils.isNotBlank(calimeetId)) {
            // 附件时在会议开始后才能上传的， 如果是新建的会议不需要调用
            attachmentAppService.saveBatchAndDelOld(command.getAppendixList(), calimeetId,
                    Attachment.AppSource.CALIBRATION.getCode(), operatorUserId, orgId);
        }

        /*// 事务之外发送消息
        CommonUtil.execAfterCommitIfHas(() -> sendMsg(operator, command, token, result,
                isStartTimeChanged(command, orgId)));*/

        return result;
    }

    private void sendMsg(
            UserCacheDetail currentUser, CaliMeetCreateCmd caliMeetCreateCmd, String token,
            CaliMeetCreateResultVO result, boolean startTimeChanged) {
        if (null != caliMeetCreateCmd.getMeetTime()) { // 时间==null 时候是点击跟踪，上传附件时调用的
            // 发送加人消息
            CaliMeetPO cm =
                    caliMeetMapper.selectByIdAndOrgId(result.getId(), currentUser.getOrgId());
            caliMeetMsgSender.sendTemplateMessageForJoinUser(token, currentUser, cm);

            // 判断时间有变更，尝试加入缓存(是否能加入，则具体判断)
            if (startTimeChanged) {
                caliMeetAppService.putCache(cm);
            }
        }
    }

    /**
     * 会议开始时间是否有变更
     */
    private boolean isStartTimeChanged(CaliMeetCreateCmd cm4cu, String orgId) {
        if (StringUtils.isBlank(cm4cu.getId())) {
            return true;
        }

        if (null == cm4cu.getMeetTime()) {
            return false;
        }

        CaliMeetPO cm = caliMeetMapper.selectByIdAndOrgId(cm4cu.getId(), orgId);
        if (null == cm) {
            throw new ApiException(CALI_MEET_NOT_EXISTED);
        }
        String d1 = DateTimeUtil.dateToString(cm.getMeetTime(), DateTimeUtil.YYYY_MM_DD_HH_MM);
        String d2 = DateTimeUtil.dateToString(cm4cu.getMeetTime(), DateTimeUtil.YYYY_MM_DD_HH_MM);

        return !d1.equals(d2);
    }

    public CaliMeetUserResultVO getMeetingUser4GetById(String id, String orgId, String lang) {
        // 查询详情
        CaliMeetUserResultVO detail = caliMeetAppService.getMeetingUser4GetById(id, orgId, lang);

        // 准备
        CaliMeetUserPO calMeetUser = caliMeetAppService.getByIdAndOrgId(id, orgId);
        if (calMeetUser == null) {
            throw new ApiException(CALI_MEET_USER_NOT_EXISTED);
        }
        CaliMeetVO caliMeetVO =
                caliMeetAppService.getMeeting4GetById(calMeetUser.getMeetingId(), orgId, lang);
        /*List<PrjDimConfPO> prjDimConfs =
                prjDimConfAppService.getDimByPrjId(caliMeetVO.getProjectId(), orgId, null);
        List<String> dimConfIds =
                prjDimConfs.stream().map(PrjDimConfPO::getId).collect(Collectors.toList());
        List<String> dimIds =
                prjDimConfs.stream().map(PrjDimConfPO::getDimensionId).collect(Collectors.toList());
        Map<String, Integer> toolTypeMap = prjDimConfs.stream()
                .collect(Collectors.toMap(PrjDimConfPO::getDimensionId, PrjDimConfPO::getToolType));*/

        // 封装参数
        UserMaxScoreParamDTO userMaxScoreParamDTO = new UserMaxScoreParamDTO();
        //userMaxScoreParamDTO.setDimensionConfigIds(dimConfIds);
        userMaxScoreParamDTO.setProjectId(caliMeetVO.getProjectId());
        userMaxScoreParamDTO.setUserId(detail.getUserId());
        // 计算维度的最大分值
        /*Map<String, UserScoreLevelResultDTO> map =
                prjCalculator.userMaxScore(userMaxScoreParamDTO, orgId);*/
        // 设置MaxValue MinValue
        /*for (String dimId : dimIds) {
            BigDecimal maxValue = new BigDecimal(0);
            if (!map.isEmpty() && map.containsKey(dimId)) {
                maxValue = map.get(dimId).getScore();
            }
            BigDecimal minValue = BigDecimal.valueOf(0);
            detail.getScoreRangeMap().put(dimId,
                    BigDecimalRangeVO.builder().maxValue(maxValue).minValue(minValue).build());
            detail.getToolTypeMap().put(dimId, toolTypeMap.get(dimId));
        }*/
        return detail;
    }

    /**
     * 盘点结果校准【单人】
     *
     * @param caliMeetUserUpdateCmd 主数据
     * @param userId                当前用户Id
     * @param orgId                 当前机构Id
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void calibration(
            CaliMeetUserUpdateCmd caliMeetUserUpdateCmd, String userId, String orgId) {
        CaliMeetUserPO calMeetUser =
                caliMeetAppService.getById(caliMeetUserUpdateCmd.getId(), orgId); //  操作数据需要带orgid
        if (calMeetUser == null) {
            throw new ApiException(CALI_MEET_USER_NOT_EXISTED);
        }

        // 校验校准分数
        Map<String, BigDecimal> calibrationScore = caliMeetUserUpdateCmd.getCalibrationScore();
        if (!calibrationScore.isEmpty()) {
            for(Map.Entry<String, BigDecimal> entry : calibrationScore.entrySet()) {
                BigDecimal value = entry.getValue();
                if (value != null && value.compareTo(new BigDecimal(10000)) > 0) {
                    throw new ApiException(ExceptionKeys.CALI_SCORE_OVER_MAXS);
                }
            }
        }


        // 保存发展建议
        calMeetUser.setSuggestion(caliMeetUserUpdateCmd.getSuggestion());
        EntityUtil.setUpdate(calMeetUser, userId);
        caliMeetAppService.updateById(calMeetUser); //  操作数据需要带orgid

        // 保存校准的维度
        ArrayList<String> userIdList = new ArrayList<>();
        userIdList.add(calMeetUser.getUserId());
        List<CaliMeetUserResultPO> resultList =
                caliMeetAppService.findByMeetingIdAndUserIds(orgId, calMeetUser.getMeetingId(),
                        userIdList);
        List<CaliMeetUserResultPO> preResultList = new ArrayList<>();
        resultList.forEach(r -> {
            // 维度类型 = 维度等级
            if (PrjImptType.Type.LEVEL.getCode() == caliMeetUserUpdateCmd.getDimensionType() &&
                null != caliMeetUserUpdateCmd.getCalibrationLevel().get(r.getDimensionId())) {
                r.setCalibrationLevel(
                        caliMeetUserUpdateCmd.getCalibrationLevel().get(r.getDimensionId()));
                EntityUtil.setUpdate(r, userId);
                r.setInitType(1); // 设置标志位 1=为已校准
                preResultList.add(r);
            }
            // 维度类型 = 维度评分
            if (PrjImptType.Type.SCORE.getCode() == caliMeetUserUpdateCmd.getDimensionType() &&
                null != caliMeetUserUpdateCmd.getCalibrationScore().get(r.getDimensionId())) {

                // 如果盘点是等级，则校验输入绩效等级是否有效

                // 一般存储度评分、但维度是绩效时 存储( S+ = 1、S = 2、S- = 3 等等，具体参考枚举表 )
                r.setCalibrationScore(
                        caliMeetUserUpdateCmd.getCalibrationScore().get(r.getDimensionId()));
                EntityUtil.setUpdate(r, userId);
                r.setInitType(1); // 设置标志位 1=为已校准
                preResultList.add(r);
            }
        });

        if (preResultList.isEmpty()) {
            log.warn("LOG66220:userId={}, ", userId);
            return;
        }

        // 如果是维度评分 需要计算校准等级
        if (PrjImptType.Type.SCORE.getCode() == caliMeetUserUpdateCmd.getDimensionType()) {
            calculation(preResultList);
        }

        for (CaliMeetUserResultPO caliMeetUserResultPo : preResultList) {
            caliMeetUserResultMapper.updateById(caliMeetUserResultPo);
        }
    }

    /**
     * 计算等级
     *
     * @param preResultList
     */
    private void calculation(List<CaliMeetUserResultPO> preResultList) {

        // 查询出每个维度的设置类型（0-枚举，1-百分比，2-绝对值）
        CaliMeetUserResultPO caliMeetUserResultPO = preResultList.get(0);
        List<PrjDimRuleDTO> rule4GetList =
                prjDimRuleAppService.findByProjectId(caliMeetUserResultPO.getProjectId(),
                        caliMeetUserResultPO.getOrgId());
        // Map的value虽然是list 但正常情况只有一个值。
        Map<String, List<PrjDimRuleDTO>> ruleMap =
                rule4GetList.stream().collect(Collectors.groupingBy(PrjDimRuleDTO::getDimensionId));

        // 规则分级条件
        List<PrjDimRuleCondPO> prjRuleCondList =
                prjDimRuleCondAppService.findByProjectId(caliMeetUserResultPO.getProjectId(),
                        caliMeetUserResultPO.getOrgId());
        Map<String, List<PrjDimRuleCondPO>> conditionMap = prjRuleCondList.stream()
                .collect(Collectors.groupingBy(PrjDimRuleCondPO::getRuleId));

        preResultList = preResultList.stream().filter(r -> {
            List<PrjDimRuleCondPO> tempRc =
                    conditionMap.get(ruleMap.get(r.getDimensionId()).get(0).getId());
            if (CollectionUtils.isNotEmpty(tempRc) &&
                ruleMap.get(r.getDimensionId()).get(0).getClassType() == 0) {
                // 校验维度等级是否 合规
                //chkLevelScore(tempRc, r.getCalibrationScore());


                // 枚举, 与dict表保存一致，dict是int类型
                // (uslrMap中维度值是整型、ruleConditions是浮点，调用计算方法报错，所以转换一下)
                tempRc.forEach(
                        a -> a.setSymbolValue(new BigDecimal(a.getSymbolValue().intValue())));
                // 如果校准的维度值在项目的分级规则中不存在，则不参与运算；
                Map<BigDecimal, List<PrjDimRuleCondPO>> ruleConditionMap = tempRc.stream()
                        .collect(Collectors.groupingBy(PrjDimRuleCondPO::getSymbolValue));
                return ruleConditionMap.containsKey(r.getCalibrationScore());
            }
            return true;
        }).collect(Collectors.toList());

        preResultList.forEach(r -> {
            List<PrjDimRuleCondPO> tempRc =
                    conditionMap.get(ruleMap.get(r.getDimensionId()).get(0).getId());
            List<UserScoreLevelResultDTO> uslrList = new ArrayList<>();
            UserScoreLevelResultDTO temp = new UserScoreLevelResultDTO();
            temp.setScore(r.getCalibrationScore());
            temp.setUserId(r.getUserId());
            temp.setDimensionId(r.getDimensionId());
            uslrList.add(temp);
            // 计算维度等级
            prjCalculator.score2LevelCalibration(uslrList,
                    ruleMap.get(r.getDimensionId()).get(0).getClassType(), tempRc, r.getProjectId(),
                    r.getOrgId());
            // 设置校准结果
            r.setCalibrationLevel(uslrList.get(0).getConditionLevel());
        });
    }

    private void chkLevelScore(List<PrjDimRuleCondPO> tempRc, BigDecimal celScore){
        List<BigDecimal> levelScore = tempRc.stream().map(PrjDimRuleCondPO::getSymbolValue).toList();
        boolean flag = false;
        for (BigDecimal score : levelScore) {
            if (score.compareTo(celScore) == 0) {
                return;
            }
        }
        if (!flag) {
            throw new ApiException(ExceptionKeys.PRJ_PERF_LEVEL_NO_RIGHT);
        }

    }

}
