package com.yxt.talent.rv.application.calimeet.impt;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetRvUserDataDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetRvUserImportDTO;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.application.user.UserQryAppService;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserBatchAddCmd;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.ExportConstant;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.transfer.FileProcessedResult;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.file.EasyExcelListener;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImportSupport;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImporter;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileReader;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * @Description 盘点校准人员导入
 * <AUTHOR>
 * @Date 2024/7/23 15:55
 **/

@Component
@RequiredArgsConstructor
public class CaliMeetUserImporter
        extends FileImporter<CaliMeetRvUserImportDTO, FileProcessedResult<CaliMeetRvUserImportDTO>, FileImportResult> {

    private final PrjUserMapper prjUserMapper;

    private final CaliMeetMapper caliMeetMapper;

    private final UserQryAppService userQryAppService;

    private final I18nComponent i18nComponent;

    private final CaliMeetAppService caliMeetAppService;

    private final CaliMeetUserImpErrStrategy caliMeetUserImpErrStrategy;

    private final DlcComponent dlcComponent;

    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;
    private final XpdMapper xpdMapper;


    public FileImportResult toImport(
            String caliMeetId, FileImportCmd fileImportCmd, MultipartFile file,
            UserCacheDetail currentUser) {

        String orgId = currentUser.getOrgId();
        String lockKey = String.format(
                RedisKeys.LK_CALI_MEET_USER_IMPT, orgId, caliMeetId);
        String fileId = fileImportCmd.getFileId();

        Function<List<CaliMeetRvUserImportDTO>, FileProcessedResult<CaliMeetRvUserImportDTO>>
                dataProcessor =
                importDataList -> dataProcess(caliMeetId, currentUser, importDataList);

        UnaryOperator<FileProcessedResult<CaliMeetRvUserImportDTO>> errorProcessor =
                this::errorProcess;

        FileImportSupport<CaliMeetRvUserImportDTO, FileProcessedResult<CaliMeetRvUserImportDTO>, FileImportResult>
                fileImportSupport =
                FileImportSupport.<CaliMeetRvUserImportDTO, FileProcessedResult<CaliMeetRvUserImportDTO>, FileImportResult>builder()
                        .file(file)
                        .fileId(fileId)
                        .tranId(lockKey)
                        .startRow(0)
                        .orgId(orgId)
                        .operator(currentUser.getUserId())
                        .importContentClazz(CaliMeetRvUserImportDTO.class)
                        .dataReader(new CaliMeetUserReader(file, fileId))
                        .dataProcessor(dataProcessor)
                        .errorProcessor(errorProcessor)
                        .resultProcessor(this::buildDefaultImportResult)
                        .build();

        return Optional.ofNullable(toImport(fileImportSupport)).orElse(FileImportResult.EMPTY);
    }

    private FileImportResult buildDefaultImportResult(
            FileProcessedResult<CaliMeetRvUserImportDTO> processedResult) {
        return FileImportResult.builder()
                .totalCount(processedResult.getTotalCount())
                .failCount(processedResult.getFailedCount())
                .failedCount(processedResult.getFailedCount())
                .successCount(processedResult.getSuccessCount())
                .successCount(processedResult.getSuccessCount())
                .filePath(processedResult.getErrorFilePath())
                .errorFileUrl(processedResult.getErrorFilePath())
                .build();
    }

    @RequiredArgsConstructor
    private class CaliMeetUserReader extends FileReader<CaliMeetRvUserImportDTO> {

        private final MultipartFile file;

        private final String fileId;

        @Override
        @jakarta.annotation.Nonnull
        public List<CaliMeetRvUserImportDTO> read() {
            return doReadExcel();
        }

        /**
         * 由于导入文件中有部分表头是动态生成的, 所以这里没办法使用静态导入的方式, 而是采用动态解析的方式来处理导入的数据
         */
        @jakarta.annotation.Nonnull
        private List<CaliMeetRvUserImportDTO> doReadExcel() {
            ExcelReader excelReader = null;
            List<CaliMeetRvUserImportDTO> importDataList;
            try (
                    InputStream inputStream = this.getInputStream(file, fileId)
            ) {
                excelReader = EasyExcelFactory.read(inputStream).build();
                ReadSheet sheet = excelReader.excelExecutor().sheetList().get(0);

                // 读取sheet
                EasyExcelListener listener = new EasyExcelListener();
                sheet.setCustomReadListenerList(Collections.singletonList(listener));
                sheet.setHeadRowNumber(1);
                excelReader.read(sheet);

                // 提取Excel数据
                importDataList = generateMeetingUser4ExcelImportList(listener);
            } catch (IOException e) {
                throw new ApiException(e.getMessage());
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
            }
            return importDataList;
        }
    }


    @jakarta.annotation.Nonnull
    private List<CaliMeetRvUserImportDTO> generateMeetingUser4ExcelImportList(
            EasyExcelListener listener) {

        List<CaliMeetRvUserImportDTO> list = new ArrayList<>();
        List<Map<Integer, String>> datas = listener.getData();
        // 内容从第二行读取
        for (int i = 1; i < datas.size(); i++) {
            Map<Integer, String> map = datas.get(i);
            int index = 0;
            CaliMeetRvUserImportDTO user = new CaliMeetRvUserImportDTO();
            user.setUserName(map.get(index++));
            user.setFullName(map.get(index++));
            list.add(user);
        }
        return list;
    }



    private FileProcessedResult<CaliMeetRvUserImportDTO> errorProcess(FileProcessedResult<CaliMeetRvUserImportDTO> processData){

        List<CaliMeetRvUserImportDTO> failedDatas = processData.getFailedData();
        if (CollectionUtils.isEmpty(failedDatas)) {
            return processData;
        }
        String fileName = getFileName();

        String errorFilePath = dlcComponent.upload2TemporaryDisk(fileName, failedDatas, caliMeetUserImpErrStrategy);

        processData.setErrorFilePath(errorFilePath);
        return processData;

    }

    private String getFileName() {
        String fileName = i18nComponent.getI18nValue(ExportConstant.RV_CALI_IMPORT_ERROR_FILE);
        return fileName + "_" + System.currentTimeMillis() + FileConstants.FILE_SUFFIX_XLSX;
    }


    private DynamicExcelExportContent handleFailedData(List<CaliMeetRvUserImportDTO> failedDatas){
        // 表头，描述相关
        DynamicExcelExportContent result = new DynamicExcelExportContent();
        result.setHeaders(initHeadData());

        IdName idName = new IdName();
        idName.setId(i18nComponent.getI18nValue(ExportConstant.RV_CALI_EXCEL_SHEET_NAME));
        idName.setName(i18nComponent.getI18nValue(ExportConstant.RV_CALI_EXCEL_SHEET_NAME));
        result.setSheets(Lists.newArrayList(idName));

        result.setData(findData(failedDatas));

        return result;
    }

    private Map<String, List<Object>> findData(List<CaliMeetRvUserImportDTO> failedDatas){

        Map<String, List<Object>> dataMap = new HashMap<>();
        List<Object> list = new ArrayList<>();
        for (CaliMeetRvUserImportDTO failedData : failedDatas) {
            List<String> every = new ArrayList<>();
            every.add(failedData.getUserName());
            every.add(failedData.getFullName());
            every.add(failedData.getErrorMsg());
            list.add(every);
        }

        dataMap.put(i18nComponent.getI18nValue(ExportConstant.RV_CALI_EXCEL_SHEET_NAME), list);
        return dataMap;
    }

    private Map<String, List<List<String>>> initHeadData(){
        Map<String, List<List<String>>> headMap = new HashMap<>();
        List<List<String>> heads = new ArrayList<>();
        // 表题描述
        String param = "llll";
        // 账号
        List<String> head0nes = new ArrayList<>();
        head0nes.add(param);
        head0nes.add(i18nComponent.getI18nValue(ExportConstant.RV_USERNAME));
        heads.add(head0nes);

        // 姓名
        List<String> headTwos = new ArrayList<>();
        headTwos.add(param);
        headTwos.add(i18nComponent.getI18nValue(ExportConstant.RV_FULLNAME));
        heads.add(headTwos);

        // 错误原因
        List<String> errHeads = new ArrayList<>();
        errHeads.add(param);
        errHeads.add(i18nComponent.getI18nValue(ExportConstant.RV_CALI_EXCEL_ERR_COL_NAME));
        heads.add(errHeads);
        headMap.put(i18nComponent.getI18nValue(ExportConstant.RV_CALI_EXCEL_SHEET_NAME), heads);

        return headMap;
    }


    private FileProcessedResult<CaliMeetRvUserImportDTO> dataProcess(
            String caliMeetId, UserCacheDetail currentUser,
            List<CaliMeetRvUserImportDTO> importDataList) {

        CaliMeetPO meeting = caliMeetMapper.selectByIdAndOrgId(caliMeetId, currentUser.getOrgId());
        Validate.isNotNull(meeting, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        String xpdId = meeting.getProjectId();
        XpdPO xpd = xpdMapper.selectById(xpdId);
        //noinspection DataFlowIssue
        /*List<String> rvUserIds =
                prjUserMapper.selectUserIdByPrjId(meeting.getOrgId(), meeting.getProjectId());*/
        // 获取盘点项目中人员数据
        List<String> rvUserIds =
            rvActivityParticipationMemberMapper.findAllUserIdByActId(meeting.getOrgId(), xpd.getAomPrjId());

        // 去除换行符
        List<CaliMeetRvUserImportDTO> newImport = new ArrayList<>();
        for (CaliMeetRvUserImportDTO user : importDataList) {
            if (StringUtils.isBlank(user.getUserName())) {
                continue;
            }
            String username = user.getUserName().replaceAll("\n", "");
            if (StringUtils.isEmpty(username)) {
                continue;
            }
            user.setUserName(username);
            newImport.add(user);
        }
        importDataList = newImport;
        CaliMeetRvUserDataDTO bean = prepareData(importDataList, currentUser);

        // 校验
        importDataValid(bean, rvUserIds);
        // 导入人员
        List<String> userIds = bean.getPrepareList()
                .stream()
                .map(CaliMeetRvUserImportDTO::getUserId)
                .collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(userIds)) {
            CaliMeetUserBatchAddCmd add = new CaliMeetUserBatchAddCmd();
            add.setMeetingId(caliMeetId);
            add.setUserIdList(userIds);
            caliMeetAppService.batchAddUser(add, currentUser.getUserId(), meeting.getOrgId());
        }

        return new FileProcessedResult<>(
                bean.getPrepareList(), bean.getFailedList(), importDataList);
    }

    private void importDataValid(CaliMeetRvUserDataDTO bean, List<String> rvUserIds){

        List<CaliMeetRvUserImportDTO> importDataList = bean.getImportDataList();
        List<UdpLiteUserPO> udpLiteUsers = bean.getUdpLiteUsers();

        Map<String, UdpLiteUserPO> userMap =
                StreamUtil.list2map(udpLiteUsers, UdpLiteUserPO::getUsername);

        for (CaliMeetRvUserImportDTO user: importDataList) {
            // 账号校验
            if (StringUtils.isEmpty(user.getUserName())) {
                user.setErrorMsg(i18nComponent.getI18nValue(ExportConstant.RV_CALI_IMPORT_USUERNAME_EMPTY));
                bean.getFailedList().add(user);
                continue;
            }

            // 账号是否存在
            if (!userMap.containsKey(user.getUserName())) {
                user.setErrorMsg(i18nComponent.getI18nValue(ExportConstant.RV_CALI_IMPORT_USUERNAME_NOT_EXIST));
                bean.getFailedList().add(user);
                continue;
            }

            // 账号是否在盘点中
            UdpLiteUserPO liteUser = userMap.get(user.getUserName());
            if (!rvUserIds.contains(liteUser.getId())) {
                user.setErrorMsg(i18nComponent.getI18nValue(ExportConstant.RV_CALI_IMPORT_USUER_NOT_IN_RV));
                bean.getFailedList().add(user);
                continue;
            } else {
                user.setUserId(liteUser.getId());
            }

            if (StringUtils.isBlank(user.getErrorMsg())) {
                bean.getPrepareList().add(user);
            }
        }
    }

    private CaliMeetRvUserDataDTO prepareData(List<CaliMeetRvUserImportDTO> importDataList,
            UserCacheDetail currentUser) {

        List<String> usernames = importDataList.stream().map(CaliMeetRvUserImportDTO::getUserName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        List<UdpLiteUserPO> rvUdpUsers =
                userQryAppService.findOrgUsersByUserNameIncludeDeleted(currentUser.getOrgId(), usernames);
        CaliMeetRvUserDataDTO res = new CaliMeetRvUserDataDTO();
        res.setUdpLiteUsers(rvUdpUsers);
        res.setImportDataList(importDataList);
        return res;
    }

}
