package com.yxt.talent.rv.application.common;

import com.yxt.ApplicationQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用于查询的Application Service，基于CQRS命令和查询责任分离的模式，
 * 查询方法中如果涉及到复杂返回对象的需求，可以不经过领域模型层，直接依赖底层存储mapper，直接返回结果。
 * 【注意】CommandAppService和QueryAppService之间不应相互依赖，如果有两边通用的逻辑，应该抽取到XxxAppComponent中
 */
@Slf4j
@RequiredArgsConstructor
@ApplicationQueryService
public class CategoryQryAppService {
}
