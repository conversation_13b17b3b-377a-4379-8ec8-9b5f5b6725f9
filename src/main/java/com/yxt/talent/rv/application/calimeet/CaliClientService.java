package com.yxt.talent.rv.application.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.controller.client.general.meet.query.MeetClientQuery;
import com.yxt.talent.rv.controller.client.general.meet.viewobj.MeetClientVO;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliClentUserCmd;
import com.yxt.talent.rv.controller.manage.xpd.xpd.query.XpdQuery;
import com.yxt.talent.rv.controller.manage.xpd.xpd.viewobj.XpdVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CaliClientService {
    private final CalimeetMapper calimeetMapper;
    private final XpdMapper xpdMapper;
    private final CalimeetParticipantsMapper calimeetParticipantsMapper;

    public PagingList<MeetClientVO> clientList(
        PageRequest pageRequest, MeetClientQuery search,
        String orgId, String userId) {
        Page<MeetClientVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<MeetClientVO> meetClient = calimeetMapper.clientPage(page, orgId, userId, search);

        PagingList<MeetClientVO> result = BeanCopierUtil.toPagingList(meetClient);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            List<String> caliMeetIds = result.getDatas().stream().map(MeetClientVO::getId).toList();
            List<CalimeetParticipantsPO> calimeetParticipantsList =
                calimeetParticipantsMapper.listByCalimeetIdsAndUserId(orgId, caliMeetIds, userId);
            Map<String, List<CalimeetParticipantsPO>> partMap =
                calimeetParticipantsList.stream().collect(Collectors.groupingBy(CalimeetParticipantsPO::getCalimeetId));
            result.getDatas().forEach(cm -> {
                // 设置所属盘点项目名称
                XpdQuery criteria = new XpdQuery();
                criteria.setXpdId(cm.getProjectId());
                List<XpdVO> xpdList = xpdMapper.search(orgId, criteria);
                if (CollectionUtils.isEmpty(xpdList)) {
                    throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
                }
                cm.setProjectName(xpdList.get(0).getXpdName());
                // 校准完成状态
                List<CalimeetParticipantsPO> calimeetParticipants = partMap.get(cm.getId());
                if (CollectionUtils.isNotEmpty(calimeetParticipants)) {
                    CalimeetParticipantsPO calimeetParticipantsPO = calimeetParticipants.get(0);
                    cm.setCaliStatus(calimeetParticipantsPO.getCaliStatus());
                }
            });
        }

        return result;
    }

    public MeetClientVO getCaliMsg(String orgId, String calimeetId, String userId){
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(calimeetId, orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        MeetClientVO res = new MeetClientVO();

        res.setMeetName(calimeet.getCalimeetName());
        res.setMeetStatus(calimeet.getCalimeetStatus());
        res.setProjectId(calimeet.getXpdId());
        XpdQuery criteria = new XpdQuery();
        criteria.setXpdId(calimeet.getXpdId());
        List<XpdVO> xpdList = xpdMapper.search(orgId, criteria);
        if (CollectionUtils.isNotEmpty(xpdList)) {
            XpdVO xpdVO = xpdList.get(0);
            res.setProjectName(xpdVO.getXpdName());
        }
        res.setStartTime(DateTimeUtil.makeLocalDateTime2Date(calimeet.getStartTime()));
        res.setEndTime(DateTimeUtil.makeLocalDateTime2Date(calimeet.getEndTime()));

        // 是否校准完成
        List<CalimeetParticipantsPO> calimeetParticipantList =
            calimeetParticipantsMapper.listByCalimeetIdAndUserId(orgId, calimeetId, userId);
         if (CollectionUtils.isNotEmpty(calimeetParticipantList)) {
             CalimeetParticipantsPO calimeetParticipants = calimeetParticipantList.get(0);
             res.setCaliStatus(calimeetParticipants.getCaliStatus());
         }
        return  res;

    }

    public void updateUserCaliStatus(String orgId, @RequestBody CaliClentUserCmd cmd, String userId) {
        // 完成校准
        calimeetParticipantsMapper.updateFinishByUserId(orgId, cmd.getCalimeetId(), userId);

    }

}
