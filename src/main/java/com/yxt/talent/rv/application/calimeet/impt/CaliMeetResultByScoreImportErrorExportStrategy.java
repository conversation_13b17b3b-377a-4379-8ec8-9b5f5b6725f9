package com.yxt.talent.rv.application.calimeet.impt;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@RequiredArgsConstructor
public class CaliMeetResultByScoreImportErrorExportStrategy extends AbstractExportStrategy {
    private static final String TASK_NAME =
            "apis.sptalentrv.meeting.user.import.score.error.file.name";
    private final I18nComponent i18nComponent;

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        DynamicExcelExportContent prjResult = (DynamicExcelExportContent) data;
        ExcelUtils.exportWithDynamicHeader(
                prjResult.getHeaders(), prjResult.getSheets(), prjResult.getData(), filePath);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        String taskName = i18nComponent.getI18nValue(TASK_NAME);
        return buildDownInfo(userCache, fileName, taskName);
    }
}
