package com.yxt.talent.rv.application.prj;

import com.yxt.common.service.ILock;
import com.yxt.event.EventListener;
import com.yxt.talent.rv.application.prj.calc.PrjCalculator;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjResultService;
import com.yxt.talent.rv.domain.prj.event.*;
import com.yxt.talent.rv.domain.user.event.UserProductCodeChangeDomainEvent;
import com.yxt.talent.rv.domain.user.event.UserTransferResourceMessageEvent;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;
import java.util.function.Consumer;

import static com.yxt.talent.rv.application.prj.calc.PrjCalculator.CK_PRJ_CALC_BZ;
import static com.yxt.talent.rv.application.prj.calc.PrjCalculator.CK_PRJ_CALC_EVAL;
import static com.yxt.talent.rv.application.prj.calc.PrjCalculator.CK_PRJ_CALC_IMPT;
import static com.yxt.talent.rv.application.prj.calc.PrjCalculator.CK_PRJ_CALC_PERF;
import static com.yxt.talent.rv.application.prj.calc.PrjCalculator.CK_PRJ_USER_RESULT_CALC;
import static java.lang.String.format;
import static java.util.concurrent.TimeUnit.MINUTES;

/**
 * 针对领域关心的事件（包括消息事件<MessageEvent>和领域事件<DomainEvent>）作出响应
 * 监听器中应只包括简单的接收和检验，以及消息的转换封装，具体的处理逻辑应调用CmdAppService和QryAppService进行处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PrjEventListener implements EventListener {

    private static final String LK_PRJ_CALC = "sprv:lk:prj:calc:%s:%s:%s";

    private final ILock lockService;
    private final PrjCalculator prjCalculator;
    private final PrjResultService prjResultService;
    private final PrjCmdAppService prjCmdAppService;

    /**
     * 处理用户被取消奇点产品的标识事件时，对PRJ领域的影响
     */
    @org.springframework.context.event.EventListener
    public void handleUserCancelSpProductCodeEvent(UserProductCodeChangeDomainEvent event) {
        log.info("LOG13185:{}", event);
    }

    @org.springframework.context.event.EventListener
    public void handlePrjResultRuleInitEvent(PrjResultRuleInitEvent event) {
        prjResultService.initResultLabelAsync(Collections.singletonList(event.getOrgId()));
    }

    /**
     * 计算盘点项目的评价结果
     */
    @org.springframework.context.event.EventListener
    public void handlePrjUserResultCalcEvalEvent(PrjCalcEvalMessageEvent event) {
        doHandlePrjCalcEvent(event, prjCalculator::singleCalcEval, CK_PRJ_CALC_EVAL);
    }

    /**
     * 计算盘点项目的倍智测评结果
     */
    @org.springframework.context.event.EventListener
    public void handlePrjUserResultCalcBzEvent(PrjCalcBeizMessageEvent event) {
        doHandlePrjCalcEvent(event, prjCalculator::singleCalcBzEval, CK_PRJ_CALC_BZ);
    }

    /**
     * 计算盘点项目的导入维度结果
     */
    @org.springframework.context.event.EventListener
    public void handlePrjUserResultCalcExportEvent(PrjCalcImptMessageEvent event) {
        doHandlePrjCalcEvent(event, prjCalculator::singleCalcImpt, CK_PRJ_CALC_IMPT);
    }

    /**
     * 计算盘点项目的绩效结果
     */
    @org.springframework.context.event.EventListener
    public void handlePrjUserResultCalcPerfEvent(PrjCalcPerfMessageEvent event) {
        doHandlePrjCalcEvent(event, prjCalculator::singleCalcPerf, CK_PRJ_CALC_PERF);
    }

    private <T extends AbstractPrjCalcEvent> void doHandlePrjCalcEvent(
            T event, Consumer<T> eventConsumer, String dimCalcFinishCntKeyFormat) {
        String orgId = event.getOrgId();
        String prjId = event.getPrjId();
        String lockKey = format(LK_PRJ_CALC, orgId, prjId, event.hashCode());
        try {
            if (lockService.tryLock(lockKey, 5, MINUTES)) {
                eventConsumer.accept(event);
            }
        } catch (Exception e) {
            log.error("LOG13005:{}", event, e);
        } finally {
            try {
                String prjDimConfId = event.getPrjDimConf().getId();
                String dimCalcFinishCntKey =
                        format(dimCalcFinishCntKeyFormat, orgId, prjId, prjDimConfId);
                String prjCalcFinishCntKey = format(CK_PRJ_USER_RESULT_CALC, orgId, prjId);
                prjCalculator.setPrjAndDimCalcFinishInfo(
                        prjCalcFinishCntKey, dimCalcFinishCntKey, event.getQueueCount());
            } catch (Exception e) {
                log.error("LOG13015:", e);
            }
            lockService.unLock(lockKey);
        }
    }

}
