package com.yxt.talent.rv.application.common.dto;

import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.Collection;

import static com.yxt.talent.rv.application.common.dto.UserBaseInfoDTO.Assembler.INSTANCE;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class UserBaseInfoDTO implements L10NContent {

    @Schema(description = "账号")
    private String username;

    @Schema(description = "用户id")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "姓名")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    @Schema(description = "账号状态：用于标识当前用户的状态(0-禁用,1-启用, 2-已删除)")
    private Integer status;

    @Schema(description = "部门id")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    private String deptId;

    @Schema(description = "部门全链路名称")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @Schema(description = "岗位id")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;

    @Schema(description = "岗位名称")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionName;

    @Schema(description = "职级id")
    private String gradeId;

    @Schema(description = "职级名称")
    private String gradeName;

    @Schema(description = "头像")
    private String imgUrl;

    public UserBaseInfoDTO(UdpLiteUserPO user) {
        INSTANCE.update(user, this);
    }

    public String getStatusDesc() {
        return status == 2 ? "删除" : status == 1 ? "启用" : "禁用";
    }

    @Mapper
    public interface Assembler {
        UserBaseInfoDTO.Assembler INSTANCE = Mappers.getMapper(UserBaseInfoDTO.Assembler.class);

        Collection<UserBaseInfoDTO> convert(Collection<UserBaseInfoDTO> source);

        UserBaseInfoDTO convert(UserBaseInfoDTO source);

        @Mapping(target = "userId", source = "id")
        void update(UdpLiteUserPO source,  @MappingTarget UserBaseInfoDTO target);
    }

}
