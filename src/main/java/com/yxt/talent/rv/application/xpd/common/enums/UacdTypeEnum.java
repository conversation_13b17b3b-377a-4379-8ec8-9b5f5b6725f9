package com.yxt.talent.rv.application.xpd.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * UACD 项目注册器的类型
 */
@Getter
@AllArgsConstructor
public enum UacdTypeEnum {

    PRJ_XPD("proj_rcpd", "盘点项目"),
    ACTV_PERF("actv_perf", "绩效评估"),
    ACTV_PROF("actv_prof", "动态人才评估"),
    ACTV_SPEVAL("actv_speval", "测评"),
    ACTV_EXAM("actv_exam", "考试"),
    ;

    private final String regId;
    private final String regName;


    public static String getNameByRegId(String reqId) {
        for (UacdTypeEnum uacdTypeEnum : UacdTypeEnum.values()) {
            if (uacdTypeEnum.getRegId().equals(reqId)) {
                return uacdTypeEnum.getRegName();
            }
        }
        return "";
    }

    public static boolean hasLevelActv(String regId) {
        return ACTV_SPEVAL.getRegId().equals(regId) || ACTV_EXAM.getRegId().equals(regId);
    }
}
