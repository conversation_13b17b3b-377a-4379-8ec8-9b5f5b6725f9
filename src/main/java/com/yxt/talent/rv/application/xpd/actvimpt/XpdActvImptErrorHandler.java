package com.yxt.talent.rv.application.xpd.actvimpt;

import com.yxt.common.service.AuthService;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.ApiUtil;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.activity.expt.ImportIndErrorStrategy;
import com.yxt.talent.rv.application.xpd.common.dto.DimIndInfoDTO;
import com.yxt.talent.rv.application.xpd.common.dto.DimIndicatorImportDTO;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExporter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 *
 *
 * @date 2024/12/20
 */

@Service
@AllArgsConstructor
@Slf4j
public class XpdActvImptErrorHandler extends FileExporter
{
    private final I18nComponent i18nComponent;
    private final MessageSourceService msgSource;
    private final AuthService authService;
    private final ImportIndErrorStrategy importIndErrorStrategy;



    private final String SCORE = "apis.sptalentrv.xpd.import.export.temp.score";
    private final String STANDARD = "apis.sptalentrv.xpd.import.export.temp.standard";
    private final String ERR_MSG = "apis.sptalentrv.xpd.act.import.indicator.errMsg";
    private static final String[] HEADER_KEYS = {"fullname", "username"};
    public void exportTemp(List<DimIndicatorImportDTO> exports, String fileName) {
        if (CollectionUtils.isEmpty(exports)) {
            return;
        }
        /*DynamicExcelExportContent content = dealExport(exports);
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        String userId = userCacheBasic.getUserId();
        String orgId = userCacheBasic.getOrgId();
        FileExportSupportWithWaf fileExportSupport =
            FileExportSupportWithWaf.builder() // NOSONAR
                .orgId(orgId)
                .operator(userId)
                .fileName(fileName)
                .tranId(getLockKey(userId, orgId))
                .outputStrategy(importIndErrorStrategy)
                .exportContent(content)
                .build();
        toExport(fileExportSupport);*/
    }

    private String getLockKey(String userId, String orgId) {
        return String.format(RedisKeys.LK_XPD_IMPORT_ERROR_EXPORT, orgId, userId);
    }

    public DynamicExcelExportContent dealExport(List<DimIndicatorImportDTO> exports, DynamicExcelExportContent content) {
        Locale locale = authService.getLocale();

        //头部
        //content.setHeaders(initHead(exports, locale));

        //content.setSheets(FileExporter.buildSingleSheets(FileConstants.SHEET_1, getI18Msg("apis.sptalentrv.xpd.import.export.temp.sheetName")));
        // 数据
        Map<String, List<Object>> resMap = new HashMap<>();
        List<Object> dataList = new ArrayList<>();
        Map<String, List<List<String>>> headers = content.getHeaders();
        List<List<String>> lists = headers.get(FileConstants.SHEET_1);

        //int size = datas.size();
        for (DimIndicatorImportDTO export : exports) {
            List<String> datas = new ArrayList<>();
            datas.add(export.getFullName());
            datas.add(export.getUsername());
            Map<String, DimIndInfoDTO> indInfoMap = export.getIndInfoMap();

            indInfoMap.forEach((k,v) ->{
                String score = v.getScore();
                // 处理分数，如果超过两位小数则截断为两位小数
                if (score != null && !score.isEmpty()) {
                    // 使用MathUtil工具类处理小数位数
                    v.setScore(MathUtil.truncateDecimal(score, 2, RoundingMode.DOWN));
                }
                datas.add(v.getScore());
                datas.add(v.getStandard());
            });
            datas.add(export.getErrorMsg());
            dataList.add(datas);
        }
        resMap.put(FileConstants.SHEET_1, dataList);
        content.setData(resMap);
        return content;
    }

    public Map<String, List<List<String>>> initHead(List<DimIndicatorImportDTO> exports, Locale locale) {
        locale = authService.getLocale();
        String i18ScoreMsg = getI18Msg(SCORE);
        String i18Standard = getI18Msg(STANDARD);
        StringBuilder sb = new StringBuilder();
        sb.append(getI18Msg("apis.sptalentrv.xpd.import.export.temp.remark0")).append(":").append("\n");
        sb.append(getI18Msg("apis.sptalentrv.xpd.import.export.temp.remark1")).append("\n");
        sb.append(getI18Msg("apis.sptalentrv.xpd.import.export.temp.remark2")).append("\n");
        sb.append(getI18Msg("apis.sptalentrv.xpd.import.export.temp.remark3")).append("\n");
        sb.append(getI18Msg("apis.sptalentrv.xpd.import.export.temp.remark4"));
        String remark = sb.toString();

        // 固定部分
        List<List<String>> headers = new ArrayList<>();
        Locale finalLocale = locale;
        Arrays.stream(HEADER_KEYS).forEach(key -> {
            String header= "";
            if ("username".equals(key)) {
                header = ApiUtil.getL10nString(msgSource, "apis.sptalentrv.xpd.import.export.temp." + key,
                        finalLocale) + "*";
            } else {
                header = ApiUtil.getL10nString(msgSource, "apis.sptalentrv.xpd.import.export.temp." + key,
                        finalLocale);
            }

            List<String> strings = new ArrayList<>();
            strings.add(remark);
            strings.add(header);
            strings.add(header);
            headers.add(strings);
        });
        DimIndicatorImportDTO importDTO = exports.get(0);
        Map<String, DimIndInfoDTO> indInfoMap = importDTO.getIndInfoMap();
        // 动态部分
        indInfoMap.forEach((k, v) ->{
            List<String> strings = new ArrayList<>();
            strings.add(remark);
            strings.add(k);
            strings.add(i18ScoreMsg + "*");
            headers.add(strings);

            List<String> string2 = new ArrayList<>();
            string2.add(remark);
            string2.add(k);
            string2.add(i18Standard);
            headers.add(string2);

        });

        // 结尾错误数据
        String errMsg = getI18Msg(ERR_MSG);
        List<String> string3 = new ArrayList<>();
        string3.add("");
        string3.add(errMsg);
        string3.add(errMsg);
        headers.add(string3);


        Map<String, List<List<String>>> map = new HashMap<>();
        map.put(FileConstants.SHEET_1, headers);
        return map;

    }
    private String getI18Msg(String param){
        return i18nComponent.getI18nValue(param);
    }



}
