package com.yxt.talent.rv.application.org.profile.model;


import com.yxt.talent.rv.application.org.profile.SpmodelTableConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@SqlUtil.SqlTable(SpmodelTableConstants.DIM_DEPT_CLOSURE)
public class DimDeptClosure {

    private String id;

    private String orgId;

    private String thirdDeptId;

    private String thirdParentId;

    private String deptId;

    private String parentId;

    private int orderIndex = 1;

}
