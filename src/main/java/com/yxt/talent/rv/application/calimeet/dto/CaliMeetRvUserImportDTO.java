package com.yxt.talent.rv.application.calimeet.dto;

import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 校准人员导入
 *
 * <AUTHOR>
 * @Date 2024/7/23 16:47
 **/

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetRvUserImportDTO implements ImportContent {


    // 用户账号
    private String userName = "";

    // 用户姓名
    private String fullName = "";

    private String userId;


    private String errorMsg;
}
