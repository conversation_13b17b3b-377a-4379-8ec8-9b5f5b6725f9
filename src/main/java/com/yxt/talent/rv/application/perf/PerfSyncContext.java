package com.yxt.talent.rv.application.perf;

import com.yxt.talent.rv.controller.openapi.command.PerfSyncV2OpenCmd;
import com.yxt.talent.rv.domain.perf.Perf;
import com.yxt.talent.rv.domain.perf.PerfGrade;
import com.yxt.talent.rv.domain.perf.PerfPeriod;
import lombok.Data;

import java.util.Collection;
import java.util.Map;

/**
 * 绩效同步上下文，封装同步过程中需要的所有数据
 */
@Data
public class PerfSyncContext {
    
    /**
     * 机构ID
     */
    private String orgId;
    
    /**
     * 原始同步数据
     */
    private Collection<PerfSyncV2OpenCmd> syncData;
    
    /**
     * 第三方用户ID到系统用户ID的映射
     */
    private Map<String, String> userIdDict;
    
    /**
     * 绩效等级名称到绩效等级对象的映射
     */
    private Map<String, PerfGrade> perfGradeNameDict;
    
    /**
     * 绩效周期字典（key为周期标识，value为周期对象）
     */
    private Map<String, PerfPeriod> perfPeriodDict;
    
    /**
     * 已存在的绩效数据（key为用户绩效标识，value为绩效对象）
     */
    private Map<String, Perf> existingPerfs;
}
