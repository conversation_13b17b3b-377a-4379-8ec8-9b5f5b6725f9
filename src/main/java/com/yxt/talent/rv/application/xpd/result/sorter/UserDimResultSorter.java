package com.yxt.talent.rv.application.xpd.result.sorter;

import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;

import java.util.List;

/**
 * 用户维度结果排序器接口
 * 
 * 负责对宫格中的用户列表进行排序
 */
public interface UserDimResultSorter {
    
    /**
     * 对用户列表按照指定维度进行排序
     * 
     * @param userList 待排序的用户列表
     * @param sortDimId 排序维度ID
     * @param xSdDimId x轴维度ID
     * @param ySdDimId y轴维度ID
     * @param orgId 组织ID
     * @param xpdId 盘点项目ID
     * @return 排序后的用户列表
     */
    List<XpdUserDimResultsVO> sort(
            List<XpdUserDimResultsVO> userList, 
            String sortDimId, 
            String xSdDimId, 
            String ySdDimId,
            String orgId, 
            String xpdId);
}
