package com.yxt.talent.rv.application.activity.dto;

import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class ProfUserListParam {
    @Schema(description = "活动ID")
    @NotBlank(message = ExceptionKeys.ACTV_ID_NOT_BLANK)
    private String actvId;
    @Schema(description = "部门ID列表")
    private List<String> deptIds;
    @Schema(description = "岗位ID列表")
    private List<String> positionIds;
    /**
     * 用户状态  0-禁用 ，1-启用, 2-删除
     */
    @Schema(description = "用户状态  0-禁用 ，1-启用, 2-删除")
    private Integer status;
    /**
     * 搜索关键字 (姓名/账号)
     */
    @Schema(description = "搜索关键字 (姓名/账号)")
    private String keyword;

    @Schema(description = "关键字类型 2-账号，1-姓名，-1-账号或姓名")
    private Integer kwType;
}
