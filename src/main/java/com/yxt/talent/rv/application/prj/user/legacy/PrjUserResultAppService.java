package com.yxt.talent.rv.application.prj.user.legacy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.rv.application.prj.prj.dto.PrjResultDTO;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjResultService;
import com.yxt.talent.rv.application.prj.user.dto.PrjUserTalentDTO;
import com.yxt.talent.rv.controller.manage.prj.dim.viewobj.PrjDimVO;
import com.yxt.talent.rv.controller.manage.prj.result.enums.RuleTypeEnum;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjGridScopeAuthQuery;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserDimResultVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.XpdUserGridResultVO;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConf;
import com.yxt.talent.rv.domain.prj.entity.user.PrjUserResult;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.EnableEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PrjUserResultAppService {

    private final PrjDimConfMapper prjDimConfMapper;
    private final PrjUserResultMapper prjUserResultMapper;
    private final PrjDimMapper prjDimMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final L10nAclService l10nAclService;
    private final PrjResultService prjResultService;

    /**
     * 查询项目人员维度计算结果
     *
     * @param orgId     机构id
     * @param projectId 项目id
     * @param userIds   用户ids
     * @return List<PrjUserCalcResult>
     */
    public List<PrjUserResultPO> findByProjectIdAndUserIds(
            String orgId, String projectId, List<String> userIds) {
        return prjUserResultMapper.selectByOrgIdAndPrjIdAndUserIds(orgId, projectId, userIds);
    }

    /**
     * 盘点结果九宫格查询
     */
    public PagingList<XpdUserGridResultVO> findByXyAxis(
            String orgId, PrjGridScopeAuthQuery search, IPage<XpdUserGridResultVO> requestPage,
            String lang) {
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        Set<String> l10nUserIds =
                l10nAclService.searchContentByKey(enableLocalization, List.of(orgId),
                        ResourceTypeEnum.USER, search.getSearchKey());
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            search.setUserIds(new ArrayList<>(l10nUserIds));
        }
        IPage<XpdUserGridResultVO> page =
                prjUserResultMapper.selectByXyAxis(orgId, search, requestPage);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            l10nAclService.translateList(enableLocalization, List.of(orgId), lang,
                    XpdUserGridResultVO.class, page.getRecords());
            page.getRecords().forEach(item -> {
                long total = page.getTotal();
                if (total > requestPage.getSize() * requestPage.getCurrent()) {
                    item.setPage(1);
                }
            });
        }

        return BeanCopierUtil.toPagingList(page);
    }

    /**
     * 盘点结果九宫格查询
     *
     * @param orgId 机构id
     */
    @SuppressWarnings({"OverlyLongMethod"})
    public List<XpdUserGridResultVO> palaces(
            String orgId, PrjGridScopeAuthQuery search, PageRequest pageRequest, String lang) {

        List<XpdUserGridResultVO> result = new ArrayList<>();
        Map<String, BigDecimal> map = new HashMap<>(8);
        BigDecimal sumTotalUser = BigDecimal.ZERO;
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        // 初始化坐标轴
        for (int x = 1; x <= 3; x++) {
            for (int y = 1; y <= 3; y++) {
                Page<XpdUserGridResultVO> requestPage =
                        new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
                search.setValueX(x);
                search.setValueY(y);
                Set<String> l10nUserIds =
                        l10nAclService.searchContentByKey(enableLocalization, List.of(orgId),
                                ResourceTypeEnum.USER, search.getSearchKey());
                if (CollectionUtils.isNotEmpty(l10nUserIds)) {
                    search.setUserIds(new ArrayList<>(l10nUserIds));
                }
                IPage<XpdUserGridResultVO> page =
                        prjUserResultMapper.selectByXyAxis(orgId, search, requestPage);
                l10nAclService.translateList(enableLocalization, List.of(orgId), lang,
                        XpdUserGridResultVO.class, page.getRecords());

                // 分页查询，取出list
                List<XpdUserGridResultVO> list = page.getRecords();
                if (CollectionUtils.isNotEmpty(list)) {
                    long total = page.getTotal();
                    BigDecimal tu = new BigDecimal(String.valueOf(total));
                    sumTotalUser = sumTotalUser.add(tu);
                    map.put(String.format("%s%s%s", x, "&", y), tu);
                    for (XpdUserGridResultVO item : list) {
                        item.setXAxis(search.getAxisX());
                        item.setXValue(x);
                        item.setYAixs(search.getAxisY());
                        item.setYValue(y);
                        item.setTotalUser(total);
                        if ((int) total > requestPage.getSize()) {
                            item.setPage(1);
                        }
                    }
                    result.addAll(list);
                }
            }
        }
        // 计算百分比
        BigDecimal finalSumTotalUser = sumTotalUser;
        map.keySet().forEach(key -> {
            BigDecimal divide = map.get(key).divide(finalSumTotalUser, 4, RoundingMode.HALF_UP);
            map.put(key, divide);
        });
        result.forEach(e -> {
            String format = String.format("%s%s%s", e.getXValue(), "&", e.getYValue());
            BigDecimal bigDecimal = map.get(format);
            e.setPercentStr(bigDecimal.multiply(new BigDecimal("100")).stripTrailingZeros()
                                    .toPlainString() + "%");
        });
        return result;
    }

    /**
     * 查询项目下人员的在各个维度下初始盘点等级结果
     *
     * @param orgId     机构id
     * @param projectId 盘点项目id
     * @param userId    人员id
     */
    public PrjUserDimResultVO getUserResult(
            String orgId, String projectId, String userId, String lang) {
        PrjUserDimResultVO prjUserDimResultVO = new PrjUserDimResultVO();
        // 查询人员的信息
        UdpLiteUserPO user = udpLiteUserMapper.selectByUserId(orgId, userId);
        if (user == null) {
            throw new ApiException(ExceptionKeys.USER_NOT_EXISTED);
        }
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(
                enableLocalization, List.of(orgId), lang, UdpLiteUserPO.class, List.of(user));

        prjUserDimResultVO.setUserId(userId);
        prjUserDimResultVO.setFullName(user.getFullname());
        prjUserDimResultVO.setUserName(user.getUsername());
        prjUserDimResultVO.setDeptName(user.getDeptName());
        prjUserDimResultVO.setPosition(user.getPositionName());
        prjUserDimResultVO.setImgUrl(user.getImgUrl());
        // 获取项目下的维度
        List<PrjDimConfPO> list =
                prjDimConfMapper.selectByPrjIdAndOrgIdAndIfStatus(projectId, orgId,
                        EnableEnum.ENABLED.getCode());
        List<PrjDimVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> dimensionIds =
                    list.stream().map(PrjDimConfPO::getDimensionId).collect(Collectors.toList());
            List<PrjDimPO> prjDims =
                    prjDimMapper.selectByOrgIdAndIfDimIdsOrderByCreateTime(orgId, dimensionIds);
            Map<String, String> dimensionIdNameMap =
                    StreamUtil.list2map(prjDims, PrjDimPO::getId, PrjDimPO::getDimensionName);
            Map<String, Integer> dimensionIdTypeMap =
                    StreamUtil.list2map(prjDims, PrjDimPO::getId, PrjDimPO::getDimensionType);
            // 查询项目下的结果
            List<PrjUserResultPO> calcResults =
                    prjUserResultMapper.selectByOrgIdAndPrjIdAndUserIdAndDimIds(orgId, projectId,
                            userId, dimensionIds);
            if (CollectionUtils.isNotEmpty(calcResults)) {
                Map<String, Integer> dimensionMap =
                        StreamUtil.list2map(calcResults, PrjUserResultPO::getDimensionId,
                                PrjUserResultPO::getLastLevel);
                list.forEach(item -> {
                    PrjDimVO bean = new PrjDimVO();
                    BeanCopierUtil.copy(item, bean);
                    bean.setDimensionName(dimensionIdNameMap.get(item.getDimensionId()));
                    bean.setLevel(null != dimensionMap.get(item.getDimensionId()) ?
                            dimensionMap.get(item.getDimensionId()) : 0);
                    bean.setDimensionType(dimensionIdTypeMap.get(item.getDimensionId()));
                    result.add(bean);
                });
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            List<PrjDimVO> collect =
                    result.stream().sorted(Comparator.comparing(PrjDimVO::getDimensionType))
                            .collect(Collectors.toList());
            prjUserDimResultVO.setResult(collect);
            return prjUserDimResultVO;
        }
        prjUserDimResultVO.setResult(result);
        return prjUserDimResultVO;
    }

    /**
     * 盘点人才定义
     *
     * @param orgId     机构id
     * @param projectId 项目id
     */
    public List<PrjUserTalentDTO> calcPrjUserTalents(
            String orgId, String projectId, List<String> userIds) {
        List<PrjDimConfPO> configs =
                prjDimConfMapper.selectByOrgIdAndPrjIdAndIfStatus(orgId, projectId,
                        PrjDimConf.DimStatus.CONFIGURED.getCode());
        if (CollectionUtils.isEmpty(configs)) {
            return new ArrayList<>();
        }

        // 防止出现一个维度有两个配置的情况，这里去个重再返回
        Map<String, PrjDimConfPO> collect = configs.stream().collect(
                Collectors.toMap(PrjDimConfPO::getDimensionId, Function.identity(),
                        (k1, k2) -> k2));
        List<PrjDimConfPO> finalConfigs = new ArrayList<>(collect.values());


        List<PrjUserResultPO> userCalcResults =
                prjUserResultMapper.selectByOrgIdAndPrjIdAndLastLevelNe0AndIfUserIds(orgId,
                        projectId, userIds);
        Map<String, List<PrjUserResultPO>> userCalcResultMap =
                userCalcResults.stream().collect(Collectors.groupingBy(PrjUserResultPO::getUserId));
        List<PrjResultDTO> ruleList = prjResultService.getResultLabelListForCal(orgId);
        List<PrjDimPO> allDims = prjDimMapper.selectByOrgId(orgId);
        // 存放每个人的人才定义
        List<PrjUserTalentDTO> prjUserTalentDtos = new ArrayList<>();
        userCalcResultMap.forEach((userId, userDimCalcResults) -> {
            // 获取每个维度的最后一次盘点结果
            Map<String, PrjUserResultPO> prjUserCalcResultMap = userDimCalcResults.stream().collect(
                    Collectors.toMap(PrjUserResultPO::getDimensionId, Function.identity(),
                            BinaryOperator.maxBy(
                                    Comparator.comparing(PrjUserResultPO::getUpdateTime))));
            if (prjUserCalcResultMap.isEmpty()) {
                return;
            }
            // 获取每个维度的盘点结果
            List<Integer> dimCalcResults =
                    prjUserCalcResultMap.values().stream().map(PrjUserResultPO::getLastLevel)
                            .collect(Collectors.toList());
            if (dimCalcResults.size() < 2) {
                return;
            }
            // 至少2个维度出来结果，才会给出一个定义
            log.info(
                    "LOG14595:calcPrjUserTalents ruleList={}",
                    BeanHelper.bean2Json(ruleList, JsonInclude.Include.ALWAYS));
            //int defined = talentResult(dimCalcResults);
            int defined = newTalentResult(prjUserCalcResultMap, dimCalcResults, ruleList, allDims);
            prjUserTalentDtos.add(new PrjUserTalentDTO(userId, defined, dimCalcResults.size(),
                    dimCalcResults.size() == finalConfigs.size() ? 1 : 0));
        });
        return prjUserTalentDtos;
    }

    private int newTalentResult(
            Map<String, PrjUserResultPO> prjUserCalcResultMap, List<Integer> levels,
            List<PrjResultDTO> ruleList, List<PrjDimPO> allDimCons) {
        // 高等级数量
        long highNum =
                levels.stream().filter(reslut -> reslut == PrjUserResult.Level.HIGH.getCode())
                        .count();
        // 中等级数量
        long middleNum =
                levels.stream().filter(reslut -> reslut == PrjUserResult.Level.MIDDLE.getCode())
                        .count();
        // 低等级数量
        long lowNum = levels.stream().filter(reslut -> reslut == PrjUserResult.Level.LOW.getCode())
                .count();
        //组装变量
        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put(RuleTypeEnum.HIGH.getCode(), highNum);
        variableMap.put(RuleTypeEnum.MIDDLE.getCode(), middleNum);
        variableMap.put(RuleTypeEnum.LOW.getCode(), lowNum);
        Map<String, Object> dimensionMap = new HashMap<>();
        for (PrjDimPO dimConf : allDimCons) {
            if (null != prjUserCalcResultMap.get(dimConf.getId())) {
                dimensionMap.put(
                        dimConf.getId(), prjUserCalcResultMap.get(dimConf.getId()).getLastLevel());
            } else {
                dimensionMap.put(dimConf.getId(), 0);
            }
        }
        variableMap.put("dimension", dimensionMap);
        log.info("LOG14605:newTalentResult variableMap:{}", variableMap);
        StandardEvaluationContext context = new StandardEvaluationContext();
        for (Map.Entry<String, Object> entry : variableMap.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }
        //根据sPel表达式计算结果
        return calByRule(context, ruleList);
    }

    private int calByRule(StandardEvaluationContext context, List<PrjResultDTO> ruleList) {
        int result = 0;
        for (PrjResultDTO rule : ruleList) {
            if (null != rule.getRuleExpression()) {
                ExpressionParser parser = new SpelExpressionParser();
                Expression exp = parser.parseExpression(rule.getRuleExpression());
                log.info("LOG14615:newTalentResult ruleExpression:{}", rule.getRuleExpression());
                if (Boolean.TRUE.equals(exp.getValue(context, Boolean.class))) {
                    result = rule.getOrderIndex();
                    return result;
                }
            }
        }
        return result;
    }
}
