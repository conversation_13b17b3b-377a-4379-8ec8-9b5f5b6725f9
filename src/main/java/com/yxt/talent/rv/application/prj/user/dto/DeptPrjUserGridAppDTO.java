package com.yxt.talent.rv.application.prj.user.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Schema(name = "管理端-盘点项目-盘点结果九宫格")
public class DeptPrjUserGridAppDTO {

    @Schema(description = "项目id")
    private String prjId;

    @Schema(description = "名称")
    private String prjName;

    @Schema(description = "维度盘点结果")
    private List<UserDimGridAppDTO> dimGrid4GetList = new ArrayList<>();
}
