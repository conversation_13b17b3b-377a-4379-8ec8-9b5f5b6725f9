package com.yxt.talent.rv.application.xpd.result;

import com.yxt.talent.rv.application.xpd.result.strategy.UserDimResultFillContext;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用户维度结果数据集服务
 * 负责从数据集表获取用户维度数据并填充到用户信息中
 *
 * @deprecated 建议使用 {@link UserDimResultFillContext} 策略模式
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Deprecated
public class UserDimResultDatasetService {

    private final UserDimResultFillContext userDimResultFillContext;

    /**
     * 从数据集表获取用户维度数据并填充到用户信息中
     *
     * @param orgId 机构ID
     * @param userIds 用户ID列表
     * @param xpdGrid 宫格信息
     * @param userInfoMap 用户信息映射
     * @deprecated 建议使用 {@link UserDimResultFillContext#fillFromDataset(String, String, List, XpdGridPO, Map)}
     */
    @Deprecated
    public void fillUserDimResultsFromDataset(String orgId, List<String> userIds,
                                              XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap) {
        // 委托给策略模式实现
        userDimResultFillContext.fillFromDataset(orgId, null, userIds, xpdGrid, userInfoMap);
    }
}
