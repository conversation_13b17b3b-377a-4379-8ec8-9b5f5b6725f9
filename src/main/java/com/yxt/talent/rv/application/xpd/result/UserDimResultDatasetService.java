package com.yxt.talent.rv.application.xpd.result;

import com.alibaba.fastjson.JSONObject;
import com.yxt.common.util.StreamUtil;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder;
import com.yxt.talent.rv.application.xpd.result.dto.UserDimensionDataDTO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import com.yxt.talent.rv.infrastructure.service.spmodel.SpmodelAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.jsonObject2BeanList;

/**
 * 用户维度结果数据集服务
 * 负责从数据集表获取用户维度数据并填充到用户信息中
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDimResultDatasetService {

    private final SpmodelAclService spmodelAclService;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final AppProperties appProperties;

    /**
     * 从数据集表获取用户维度数据并填充到用户信息中
     *
     * @param orgId 机构ID
     * @param userIds 用户ID列表
     * @param xpdGrid 宫格信息
     * @param userInfoMap 用户信息映射
     */
    public void fillUserDimResultsFromDataset(String orgId, List<String> userIds,
                                              XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        // 构建查询用户维度数据的SQL
        String sql = SpmodelSqlBuilder.buildUserDimensionDataSql(orgId, userIds, xpdGrid.getGridType());
        if (StringUtils.isBlank(sql)) {
            return;
        }

        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(sql);

        log.info("LOG21234:orgId={}, userDimensionSql={}", orgId, sql);

        // 查询用户维度数据
        List<UserDimensionDataDTO> userDimensions =
            jsonObject2BeanList(UserDimensionDataDTO.class, spmodelAclService.sql(param));

        if (CollectionUtils.isEmpty(userDimensions)) {
            return;
        }

        // 按用户ID分组维度数据
        Map<String, List<UserDimensionDataDTO>> userDimMap =
            userDimensions.stream().collect(Collectors.groupingBy(UserDimensionDataDTO::getUserId));

        // 获取宫格对应的维度分层，从而拿到第三维度颜色
        List<XpdGridLevelPO> gridLevelList = xpdGridLevelMapper.listByXpdId(orgId, xpdGrid.getId());
        Map<Integer, XpdGridLevelPO> gridLevelMap = StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getOrderIndex);

        // 为每个用户填充维度结果
        userDimMap.forEach((userId, dimList) -> {
            XpdUserDimResultsVO userInfo = userInfoMap.get(userId);
            if (userInfo != null && CollectionUtils.isNotEmpty(dimList)) {
                List<XpdUserDimResultVO> dimResultVOs = dimList.stream()
                    .map(dimData -> {
                        XpdUserDimResultVO dimResult = new XpdUserDimResultVO();
                        dimResult.setSdDimId(dimData.getDimensionId());
                        dimResult.setGridLevelOrderIndex(dimData.getCalibrationLevel());
                        dimResult.setThirdDimColor(decideThirdDimColor(xpdGrid, gridLevelMap, dimData.getCalibrationLevel()));
                        return dimResult;
                    })
                    .collect(Collectors.toList());
                userInfo.setUserDimResults(dimResultVOs);
            }
        });
    }

    /**
     * 决定第三维度颜色
     *
     * @param xpdGrid 宫格信息
     * @param gridLevelMap 宫格分层映射
     * @param calibrationLevel 校准等级
     * @return 第三维度颜色
     */
    private String decideThirdDimColor(XpdGridPO xpdGrid, Map<Integer, XpdGridLevelPO> gridLevelMap, Integer calibrationLevel) {
        if (calibrationLevel == null) {
            return null;
        }
        XpdGridLevelPO gridLevel = gridLevelMap.get(calibrationLevel);
        if (gridLevel == null) {
            return null;
        }
        return gridLevel.decideThirdDimColor(xpdGrid, appProperties);
    }
}
