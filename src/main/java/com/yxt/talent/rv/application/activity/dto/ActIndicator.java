package com.yxt.talent.rv.application.activity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class ActIndicator {
    @Schema(
        description = "指标ID"
    )
    private String id;

    @Schema(
        description = "具体值，普通与分类类型为文本，其余为id"
    )
    private String itemValue;

    @Schema(
        description = "指标名称"
    )
    private String itemName;

    @Schema(
        description = "排序字段"
    )
    private Integer orderIndex;

}
