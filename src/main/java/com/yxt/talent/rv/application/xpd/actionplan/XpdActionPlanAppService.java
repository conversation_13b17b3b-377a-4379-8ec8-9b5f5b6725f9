package com.yxt.talent.rv.application.xpd.actionplan;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yxt.aom.base.bean.md.AomActivityPartMember4Get;
import com.yxt.aom.base.bean.md.AomDrawer4RespDTO;
import com.yxt.aom.base.controller.md.AomActivityParticipationMemberController;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.common.Constants;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.*;
import com.yxt.o2ofacade.bean.response.ProjectBaseInfoResp;
import com.yxt.o2ofacade.bean.response.talent.SpProjectInfoResp;
import com.yxt.o2ofacade.bean.talent.GroupMember4Create;
import com.yxt.o2ofacade.service.ProjectFacade;
import com.yxt.talent.rv.application.xpd.common.enums.XpdActionPlanTargetTypeEnum;
import com.yxt.talent.rv.controller.manage.prj.train.command.PrjTrainBindCmd;
import com.yxt.talent.rv.controller.manage.prj.train.viewobj.PrjBindTrainVO;
import com.yxt.talent.rv.controller.manage.prj.train.viewobj.PrjTrainBindResultVO;
import com.yxt.talent.rv.controller.manage.xpd.actionplan.cmd.XpdActionPlanCreateCmd;
import com.yxt.talent.rv.controller.manage.xpd.result.query.XpdResultQuery;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombTableResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdTableResultVO;
import com.yxt.talent.rv.controller.manage.xpd.user.command.XpdUserAddActionPlanCmd;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.MQConstant;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.train.PrjTrainMapPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdActionPlanPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO;
import com.yxt.talent.rv.infrastructure.service.remote.MqAclSender;
import com.yxt.talentbkfacade.bean.PoolUser4Add;
import com.yxt.talentbkfacade.service.TalentbkFacade;
import com.yxt.talentrvfacade.bean.PrjUserO2oInfoVO;
import com.yxt.talentrvfacade.bean.PrjUserO2oQuery;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.Validate.isNotBlank;
import static com.yxt.common.util.Validate.isNotEmpty;
import static com.yxt.talent.rv.application.xpd.common.enums.XpdActionPlanTargetTypeEnum.TALENT_POOL;
import static com.yxt.talent.rv.application.xpd.common.enums.XpdActionPlanTargetTypeEnum.TRAINING_PROJECT;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_ACTION_PLAN_COMMAND_CELL_ID_INVALID;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_ACTION_PLAN_COMMAND_DIMCOMB_INVALID;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_ACTION_PLAN_COMMAND_DIM_INVALID;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_ACTION_PLAN_COMMAND_GRID_LEVEL_ID_INVALID;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_ACTION_PLAN_COMMAND_LEVEL_ID_INVALID;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_ACTION_PLAN_COMMAND_POOL_ID_EMPTY;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_ACTION_PLAN_COMMAND_TRAINING_ID_EMPTY;
import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service
@AllArgsConstructor
public class XpdActionPlanAppService {
    private final AuthService authService;
    private final XpdActionPlanMapper xpdActionPlanMapper;
    private final ProjectFacade projectFacade;
    private final MqAclSender mqAclSender;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    // aom底层提供的胶水层接口
    private final AomActivityParticipationMemberController memberController;
    private final TalentbkFacade talentbkFacade;
    private final XpdActivityParticipationMemberMapper xpdActivityParticipationMemberMapper;

    /**
     * 创建行动计划
     * @param xpdId
     * @param command
     * @param userCacheDetail
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PrjTrainBindResultVO create(String xpdId, XpdActionPlanCreateCmd command, UserCacheDetail userCacheDetail) {
        log.info("LOG10512:开始创建行动计划, xpdId={}, targetType={}", xpdId, command.getTargetType());
        String orgId = userCacheDetail.getOrgId();

        if (XpdActionPlanTargetTypeEnum.TRAINING_PROJECT.getCode().equals(command.getTargetType())) {
            return handleTrainingProjectPlan(xpdId, command, userCacheDetail, orgId);
        } else if (TALENT_POOL.getCode().equals(command.getTargetType())) {
            return handleTalentPoolPlan(command, orgId, userCacheDetail.getUserId(), xpdId);
        }

        log.info("LOG10502:行动计划创建完成, xpdId={}", xpdId);
        return null;
    }



    private PrjTrainBindResultVO handleTrainingProjectPlan(String xpdId, XpdActionPlanCreateCmd command,
            UserCacheDetail userCacheDetail, String orgId) {
        isNotEmpty(command.getTargetIds(), XPD_ACTION_PLAN_COMMAND_TRAINING_ID_EMPTY);
        List<String> userIds = getUserIdsByRadioType(command, orgId, xpdId);
        isNotEmpty(userIds, ExceptionKeys.XPD_ACTION_PLAN_USER_ID_EMPTY);
        //noinspection SpringTransactionalMethodCallsInspection
        command.setUserIds(userIds);
        return bindTraining(xpdId, new PrjTrainBindCmd(userIds, command.getTargetIds(), xpdId), userCacheDetail);
    }

    private List<String> getUserIdsByRadioType(XpdActionPlanCreateCmd command, String orgId, String xpdId) {
        return switch (command.getRadioType()) {
            case 0 -> getDimResultUser(command, orgId, xpdId);
            case 1 -> getDimCombUsers(command, orgId, xpdId);
            case 2 -> getTalentLevelUsers(command, orgId, xpdId);
            default -> Collections.emptyList();
        };
    }

    private List<String> getDimResultUser(XpdActionPlanCreateCmd command, String orgId, String xpdId) {
        isNotBlank(command.getDimOrCombId(), XPD_ACTION_PLAN_COMMAND_DIM_INVALID);
        isNotEmpty(command.getLevelOrCellIds(), XPD_ACTION_PLAN_COMMAND_GRID_LEVEL_ID_INVALID);

        XpdResultQuery query = buildBaseQuery(command, 1);
        query.setTargetId(command.getDimOrCombId());
        query.setLevelIds(command.getLevelOrCellIds());

        return xpdResultUserDimMapper.selectUserDimResult(orgId, xpdId, query)
                .stream()
                .map(XpdTableResultVO::getUserId)
                .collect(Collectors.toList());
    }

    private List<String> getDimCombUsers(XpdActionPlanCreateCmd command, String orgId, String xpdId) {
        isNotBlank(command.getDimOrCombId(), XPD_ACTION_PLAN_COMMAND_DIMCOMB_INVALID);
        isNotEmpty(command.getLevelOrCellIds(), XPD_ACTION_PLAN_COMMAND_CELL_ID_INVALID);

        XpdResultQuery query = buildBaseQuery(command, 2);
        query.setTargetId(command.getDimOrCombId());
        query.setCellIds(command.getLevelOrCellIds());

        XpdDimCombPO xpdDimComb = Optional.ofNullable(xpdDimCombMapper.selectByPrimaryKey(command.getDimOrCombId()))
                .orElseThrow(() -> new IllegalArgumentException(XPD_ACTION_PLAN_COMMAND_DIMCOMB_INVALID));

        return xpdResultUserDimMapper.getDimCombTableResult(
                orgId, xpdId, xpdDimComb.getXSdDimId(), xpdDimComb.getYSdDimId(), query)
                .stream()
                .map(XpdDimCombTableResultVO::getUserId)
                .collect(Collectors.toList());
    }

    private List<String> getTalentLevelUsers(XpdActionPlanCreateCmd command, String orgId, String xpdId) {
        isNotEmpty(command.getLevelOrCellIds(), XPD_ACTION_PLAN_COMMAND_LEVEL_ID_INVALID);

        XpdResultQuery query = buildBaseQuery(command, 3);
        query.setLevelIds(command.getLevelOrCellIds());

        return xpdResultUserMapper.selectUserTableResult(orgId, xpdId, query)
                .stream()
                .map(XpdTableResultVO::getUserId)
                .collect(Collectors.toList());
    }

    private XpdResultQuery buildBaseQuery(XpdActionPlanCreateCmd command, int queryType) {
        XpdResultQuery query = new XpdResultQuery();
        query.setQueryType(queryType);
        query.setOpenAuth(false);
        return query;
    }

    private PrjTrainBindResultVO handleTalentPoolPlan(XpdActionPlanCreateCmd command, String orgId, String oporater, String xpdId) {
        isNotEmpty(command.getTargetIds(), XPD_ACTION_PLAN_COMMAND_POOL_ID_EMPTY);
        List<String> userIds = getUserIdsByRadioType(command, orgId, xpdId);
        isNotEmpty(userIds, ExceptionKeys.XPD_ACTION_PLAN_USER_ID_EMPTY);
        try {
            for (String targetId : command.getTargetIds()) {
                PoolUser4Add param = new PoolUser4Add();
                param.setOrgId(orgId);
                param.setUserId(oporater);
                param.setPoolId(targetId);
                param.setUserIdList(userIds);
                talentbkFacade.savePoolUser(param);
            }
        } catch (Exception e) {
            log.error("LOG20063:", e);
        }
        PrjTrainBindResultVO result = new PrjTrainBindResultVO();
        if (CollectionUtils.isNotEmpty(command.getTargetIds())) {
            result.setActionPlanIds(command.getTargetIds());
        }
        List<XpdActionPlanPO> existTrainings = xpdActionPlanMapper.selectByOrgIdAndXpdIdAndType(orgId, xpdId,
            TALENT_POOL.getCode());
        List<String> poolIds = existTrainings.stream().map(XpdActionPlanPO::getTargetId).toList();
        //   找出新增的人才池id
        List<String> inPoolIds = command.getTargetIds();
        List<String> realPoolIds = new ArrayList<>();
        for (String inPoolId : inPoolIds) {
            if (!poolIds.contains(inPoolId)) {
                realPoolIds.add(inPoolId);
            }
        }

        // 人才池数据插入到数据库
        List<XpdActionPlanPO> actionPlanList = new ArrayList<>();
        for (String realPoolId : realPoolIds) {
            XpdActionPlanPO actionPlan = new XpdActionPlanPO();
            actionPlan.setId(ApiUtil.getUuid());
            actionPlan.setOrgId(orgId);
            actionPlan.setXpdId(xpdId);
            actionPlan.setTargetId(realPoolId);
            actionPlan.setTargetType(XpdActionPlanTargetTypeEnum.TALENT_POOL.getCode());
            actionPlan.setDeleted(0);
            EntityUtil.setAuditFields(actionPlan, oporater);
            actionPlanList.add(actionPlan);
        }
        if (CollectionUtils.isNotEmpty(actionPlanList)) {
            xpdActionPlanMapper.batchInsert(actionPlanList);
        }
        return result;
    }

    /**
     * 行动计划列表
     * @param pageRequest
     * @param prjId
     * @param orgId
     * @param targetType
     * @return
     */
    public PagingList<PrjBindTrainVO> pagelist(PageRequest pageRequest, String prjId, String orgId, Integer targetType) {

        Page<XpdActionPlanPO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<XpdActionPlanPO> page = xpdActionPlanMapper.pagingByOrgIdAndXpdIdAndType(pageable, orgId, prjId, targetType);

        PagingList<XpdActionPlanPO> listpage = BeanCopierUtil.toPagingList(page);

        List<PrjBindTrainVO> prjlist = new ArrayList<>();
        PagingList<PrjBindTrainVO> result = new PagingList(prjlist, listpage.getPaging());

        if (listpage.getDatas().isEmpty()) {
            return result;
        }

        List<String> trainingIds = StreamUtil.mapList(page.getRecords(), XpdActionPlanPO::getTargetId);
        List<Long> longTrainingIds = CommonUtil.str2Long(trainingIds);
        List<SpProjectInfoResp> spProjectInfoResps =
                projectFacade.listSpProjects(orgId, longTrainingIds);

        listpage.getDatas().forEach(
                xpdActionPlanPO -> {
                    SpProjectInfoResp vo =
                            spProjectInfoResps.stream()
                                    .filter(p -> p.getId().equals(Long.valueOf(xpdActionPlanPO.getTargetId())))
                                    .findFirst()
                                    .orElse(null);
                    if (Objects.nonNull(vo)) {
                        PrjBindTrainVO prjBindTrainVO = new PrjBindTrainVO();
                        prjBindTrainVO.setId(vo.getId());
                        prjBindTrainVO.setName(vo.getName());
                        prjBindTrainVO.setStatus(vo.getStatus());
                        prjBindTrainVO.setStartTime(vo.getStartTime());
                        prjBindTrainVO.setEndTime(vo.getEndTime());
                        prjBindTrainVO.setActionPlanId(xpdActionPlanPO.getId());
                        prjlist.add(prjBindTrainVO);
                    }
                }
        );
        result.setDatas(prjlist);
//        List<XpdActionPlanPO> bindedList =
//                xpdActionPlanMapper.selectByOrgIdAndXpdIdAndType(orgId, prjId, targetType);
//        if (CollectionUtils.isEmpty(bindedList)) {
//            return Collections.emptyList();
//        }

//        List<String> trainingIds = StreamUtil.mapList(bindedList, XpdActionPlanPO::getTargetId);
//        List<Long> longTrainingIds = CommonUtil.str2Long(trainingIds);
//        List<SpProjectInfoResp> spProjectInfoResps =
//                projectFacade.listSpProjects(orgId, longTrainingIds);
//        BeanCopierUtil.convertList(
//                spProjectInfoResps, SpProjectInfoResp.class, PrjBindTrainVO.class);
        return result;
    }



    /**
     * 盘点人员批量加入培训并创建一个行动计划
     * @param xpdId
     * @param xpdActionPlanCreateCmd
     * @param operator
     * @return
     */
    @SuppressWarnings("UnusedReturnValue")
    @Transactional(rollbackFor = Exception.class)
    public PrjTrainBindResultVO bindTraining(String xpdId, PrjTrainBindCmd xpdActionPlanCreateCmd, UserCacheDetail operator) {
        List<Long> trainingIds = CommonUtil.str2Long(xpdActionPlanCreateCmd.getTrainingIds());
        String orgId = operator.getOrgId();
        List<SpProjectInfoResp> spProjectInfoResps = projectFacade.listSpProjects(orgId, trainingIds);

        PrjTrainBindResultVO bindResult = new PrjTrainBindResultVO();

        // 由于删除的项目o2o不会返回，这里将这些项目直接加入到返回结果中
        if (CollectionUtils.isEmpty(spProjectInfoResps)) {
            bindResult.getDeletedProjectIds().addAll(xpdActionPlanCreateCmd.getTrainingIds());
            return bindResult;
        }

        // 搜集删除的项目
        for (Long trainingId : trainingIds) {
            if (spProjectInfoResps.stream().noneMatch(p -> p.getId().equals(trainingId))) {
                bindResult.getDeletedProjectIds().add(String.valueOf(trainingId));
            }
        }

        List<Long> validTrainingIds = new ArrayList<>();
        for (SpProjectInfoResp spProjectInfoResp : spProjectInfoResps) {
            String trainingId = String.valueOf(spProjectInfoResp.getId());
            if (isTrainingIdSelected(xpdActionPlanCreateCmd, trainingId)) {
                int projectStatus = spProjectInfoResp.getStatus();
                categorizeProjectStatus(bindResult, trainingId, projectStatus);
                if (isValidProjectStatus(projectStatus)) {
                    validTrainingIds.add(spProjectInfoResp.getId());
                }
            }
        }

        List<Long> nonExistingTrainingIds = checkTrainingBinding(new ArrayList<>(trainingIds), spProjectInfoResps);

        List<XpdActionPlanPO> existTrainings = xpdActionPlanMapper.selectByOrgIdAndXpdIdAndType(orgId, xpdId,
            TRAINING_PROJECT.getCode());
        List<String> existTrainingIds = StreamUtil.mapList(existTrainings, XpdActionPlanPO::getTargetId);

        List<XpdActionPlanPO> bindedList =
            createUnboundActionPlan(existTrainingIds, validTrainingIds, operator, xpdId);

        if (!bindedList.isEmpty()) {
            xpdActionPlanMapper.batchInsert(bindedList);
            bindResult.setActionPlanIds(StreamUtil.mapList(bindedList, XpdActionPlanPO::getId));
        }
        bindUsers(xpdActionPlanCreateCmd, operator, validTrainingIds, nonExistingTrainingIds);

        return bindResult;
    }

    private List<Long> checkTrainingBinding(List<Long> trainingIds, List<SpProjectInfoResp> spProjectInfoResps) {
        trainingIds.removeIf(
            bindingId -> spProjectInfoResps.stream().anyMatch(e1 -> Objects.equals(e1.getId(), bindingId)));
        if (CollectionUtils.isNotEmpty(trainingIds)) {
            log.info("LOG10492:trainingIds:{} is not exist", trainingIds);
        }
        return trainingIds;
    }

    private boolean isTrainingIdSelected(PrjTrainBindCmd prjTrainBindCmd, String trainingId) {
        return prjTrainBindCmd.getTrainingIds().contains(trainingId);
    }

    private void categorizeProjectStatus(
        PrjTrainBindResultVO bindResult, String trainingId, int projectStatus) {
        // 3：已结束；4：已归档；5：已删除
        if (projectStatus == 3) {
            bindResult.getEndProjectIds().add(trainingId);
        } else if (projectStatus == 4) {
            bindResult.getFiledProjectIds().add(trainingId);
        } else if (projectStatus == 5) {
            bindResult.getDeletedProjectIds().add(trainingId);
        }
    }

    private boolean isValidProjectStatus(int projectStatus) {
        return projectStatus != 3 && projectStatus != 4 && projectStatus != 5;
    }

    private List<XpdActionPlanPO> createUnboundActionPlan(
        List<String> existTrainingIds, List<Long> validTrainingIds, UserCacheDetail operator, String projectId) {
        List<XpdActionPlanPO> bindedList = new ArrayList<>();
        for (Long bindingTrainingId : validTrainingIds) {
            if (!existTrainingIds.contains(String.valueOf(bindingTrainingId))) {
                XpdActionPlanPO prjTrainMap =
                    buildXpdActionPlan(bindingTrainingId, TRAINING_PROJECT, projectId, operator.getOrgId(), operator);
                bindedList.add(prjTrainMap);
            }
        }
        return bindedList;
    }

    private void bindUsers(
        PrjTrainBindCmd prjTrainBindCmd, UserCacheDetail operator, List<Long> validTrainingIds,
        List<Long> nonExistingTrainingIds) {
        CommonUtil.execAfterCommitIfHas(() -> {
            for (Long trainingId : validTrainingIds) {
                if (nonExistingTrainingIds.contains(trainingId)) {
                    log.info("LOG10472:");
                    continue;
                }

                ProjectBaseInfoResp projectBaseInfo = projectFacade.getProjectBaseInfo(operator.getOrgId(), trainingId);
                if (projectBaseInfo == null) {
                    log.warn("LOG10462:projectBaseInfo is null, trainingId:{}", trainingId);
                    continue;
                }

                boolean isCycle = Objects.equals(projectBaseInfo.getTimeType(), 1);
                List<String> userIds = prjTrainBindCmd.getUserIds();
                List<List<String>> userLists = ListUtils.partition(userIds, 50);

                for (List<String> userList : userLists) {
                    addGroupMember(operator.getOrgId(), trainingId, isCycle, userList, operator.getUserId(),
                        operator.getSource());
                }
            }
        });
    }

    public void addGroupMember(
        String orgId, Long trainingId, boolean isCycle, List<String> userIds, String optUserId, String source) {
        if (trainingId == null) {
            return;
        }
        // 人员加人到培训项目
        GroupMember4Create groupMember4Create = new GroupMember4Create();
        groupMember4Create.setOrgId(orgId);
        groupMember4Create.setProjectId(trainingId);
        Set<String> set = new HashSet<>(userIds);
        groupMember4Create.setUserIds(set);
        groupMember4Create.setOptUserId(optUserId);
        groupMember4Create.setSource(source);
        groupMember4Create.setFormal(1);
        groupMember4Create.setRole(1);
        if (isCycle) {
            groupMember4Create.setStartTime(LocalDateTime.now());
        }
        try {
            mqAclSender.asyncSend(
                MQConstant.TOPIC_PE_C_TALENT_ADD_GROUP_MEMBER,
                BeanHelper.bean2Json(groupMember4Create, ALWAYS));
        } catch (Exception e) {
            log.error("LOG10482:", e);
        }
    }

    @SuppressWarnings("SameParameterValue")
    private XpdActionPlanPO buildXpdActionPlan(
        Long targetId, XpdActionPlanTargetTypeEnum typeEnum, String xpdId, String orgId, UserCacheDetail operator) {
        XpdActionPlanPO actionPlan = new XpdActionPlanPO();
        actionPlan.setOrgId(orgId);
        actionPlan.setXpdId(xpdId);
        actionPlan.setTargetId(String.valueOf(targetId));
        actionPlan.setTargetType(typeEnum.getCode());
        EntityUtil.setAuditFields(actionPlan, operator);
        return actionPlan;
    }

    /**
     * 盘点人员列表中选择人员加入人才池/发起培训
     * @param request
     * @param xpdId
     * @param cmd
     */
    public void addUserToActionPlan(HttpServletRequest request, String xpdId, XpdUserAddActionPlanCmd cmd) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail(request);
        List<String> userIds = Optional.ofNullable(cmd.getUserIds()).orElse(new ArrayList<>());
        if (cmd.isAddAll()) {
            log.debug("LOG10632:limit={}", request.getParameter(Constants.PARAM_NAME_LIMIT));
            extractUserIds(request, cmd, userIds);
        }
        Validate.isNotEmpty(userIds, ExceptionKeys.XPD_ACTION_PLAN_USER_ID_EMPTY);
        cmd.setUserIds(userIds);
        this.doAddUserToActionPlan(xpdId, userIds, cmd, userCacheDetail);
    }

    private void extractUserIds(HttpServletRequest request, XpdUserAddActionPlanCmd cmd, List<String> userIds) {
        // 遍历查询分页接口，获取所有的人员id
        while (true) {
            PagingList<AomActivityPartMember4Get> subResults = memberController.pageList(request, cmd);
            if (CollectionUtils.isEmpty(subResults.getDatas())) {
                break;
            }
            for (AomActivityPartMember4Get data : subResults.getDatas()) {
                userIds.addAll(extractUserId(data.getUserId()));
            }
        }
    }

    private List<String> extractUserId(AomDrawer4RespDTO userDTO) {
        Set<String> userIds = new HashSet<>();
        for (Object data : userDTO.getDatas()) {
            try {
                JSONObject jsonObject = (JSONObject) JSON.toJSON(data);
                Object userId = jsonObject.getByPath("id");
                if (userId != null) {
                    userIds.add((String) userId);
                }
            } catch (Exception e) {
                log.warn("LOG10642:", e);
            }
        }
        return new ArrayList<>(userIds);
    }

    private void doAddUserToActionPlan(
        String xpdId, List<String> userIds, XpdUserAddActionPlanCmd cmd, UserCacheDetail userCacheDetail) {
        // TODO: 盘点人员选择人员加入人才池和培训时，是否需要创建行动计划？
        // 行动计划类型：0-培训, 1-人才池
        if (cmd.getTargetType() == 0) {
            isNotEmpty(cmd.getTargetIds(), XPD_ACTION_PLAN_COMMAND_TRAINING_ID_EMPTY);
            //noinspection SpringTransactionalMethodCallsInspection
            this.bindTraining(xpdId, new PrjTrainBindCmd(userIds, cmd.getTargetIds(), xpdId), userCacheDetail);
        } else if (cmd.getTargetType() == 1) {
            isNotEmpty(cmd.getTargetIds(), XPD_ACTION_PLAN_COMMAND_POOL_ID_EMPTY);
            for (String targetId : cmd.getTargetIds()) {
                try {
                    PoolUser4Add param = new PoolUser4Add();
                    param.setOrgId(userCacheDetail.getOrgId());
                    param.setUserId(userCacheDetail.getUserId());
                    param.setPoolId(targetId);
                    param.setUserIdList(userIds);
                    talentbkFacade.savePoolUser(param);
                } catch (Exception e) {
                    log.error("LOG20063:", e);
                }
            }
        }
    }

    public void delete(String orgId, String userId, String id) {
        XpdActionPlanPO entity = xpdActionPlanMapper.selectByPrimaryKey(id);
        if (entity == null) {
            // 行动计划不存在
            throw new ApiException("apis.sptalentrv.xpd.actionplan.null");
        }

        entity.setDeleted(YesOrNo.YES.getValue());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(userId);
        xpdActionPlanMapper.updateByPrimaryKey(entity);
    }

    public boolean hasTrainingUsedInProject(String orgId, String trainingId) {
        int cnt = xpdActionPlanMapper.countByOrgIdAndXpdIdAndType(orgId, trainingId, XpdActionPlanTargetTypeEnum.TRAINING_PROJECT.getCode());
        return cnt > 0;
    }

    public List<PrjUserO2oInfoVO> findO2oAndUserInPrj(PrjUserO2oQuery prjUserO2oQuery) {
        String orgId = prjUserO2oQuery.getOrgId();

        if (CollectionUtils.isEmpty(prjUserO2oQuery.getProjectIds())) {
            return Lists.newArrayList();
        }
        List<Long> trainIds = prjUserO2oQuery.getProjectIds();
        List<String> strTrainIds = trainIds.stream().map(Object::toString).toList();
        List<XpdActionPlanPO> existTrainings = xpdActionPlanMapper.selectByTargetIdsAndType(orgId, strTrainIds, TRAINING_PROJECT.getCode());

        if (CollectionUtils.isEmpty(existTrainings)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(prjUserO2oQuery.getUserIds())) {
            return Lists.newArrayList();
        }
        Map<String, List<XpdActionPlanPO>> prjTrainMap = existTrainings.stream().collect(groupingBy(XpdActionPlanPO::getTargetId));
        List<PrjUserO2oInfoVO> res = new ArrayList<>();
        for (Map.Entry<String, List<XpdActionPlanPO>> entry : prjTrainMap.entrySet()) {
            PrjUserO2oInfoVO user = new PrjUserO2oInfoVO();
            user.setProjectId(Long.parseLong(entry.getKey()));
            List<XpdActionPlanPO> values = entry.getValue();
            List<String> xpdIds = values.stream().map(XpdActionPlanPO::getXpdId).toList();
            List<ActivityParticipationMember> members =xpdActivityParticipationMemberMapper.selectByXpdIds(orgId, xpdIds);
            if (CollectionUtils.isEmpty(members)) {
                continue;
            }
            user.setUserIds(StreamUtil.mapList(members, ActivityParticipationMember::getUserId));
            res.add(user);
        }
        return res;
    }

}
