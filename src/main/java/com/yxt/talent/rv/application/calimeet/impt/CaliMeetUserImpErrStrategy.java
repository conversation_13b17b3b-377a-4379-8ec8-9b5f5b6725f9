package com.yxt.talent.rv.application.calimeet.impt;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.infrastructure.common.constant.ExportConstant;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/7/24 15:19
 **/
@Component
@RequiredArgsConstructor
public class CaliMeetUserImpErrStrategy extends AbstractExportStrategy {

    private final I18nComponent i18nComponent;

    private static final String TEMPLATE_PATH = "excel/cali_user_import_error.xlsx";

    private static final String TEMPLATE_PATH_TW = "excel/cali_user_import_error_tw.xlsx";

    private static final String TEMPLATE_PATH_EN = "excel/cali_user_import_error_en.xlsx";

    private static final String HEADER_PREFIX = "apis.sptalentrv.cali.";
    private static final String[] HEADER_KEYS = new String[]{"userName", "fullName"};


    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        String language = i18nComponent.getI18nValue(ExportConstant.RV_CALI_EXCEL_LANGUAGE);
        String template = findTemplate(language);
        /*DynamicExcelExportContent prjResult = (DynamicExcelExportContent) data;
        ExcelUtils.exportWithDynamicHeader(
                prjResult.getHeaders(), prjResult.getSheets(), prjResult.getData(), filePath);*/

        ExcelUtils.exportWithTemplate(i18nComponent.getI18nMap(HEADER_PREFIX, HEADER_KEYS), data, filePath, template);
        return fileName;
    }

    private String findTemplate(String language){
        if (Objects.equals(language, "china")) {
            return TEMPLATE_PATH;
        } else if (Objects.equals(language, "en")) {
            return TEMPLATE_PATH_EN;
        } else if (Objects.equals(language, "tw")) {
            return TEMPLATE_PATH_TW;
        } else {
            return TEMPLATE_PATH;
        }
    }


    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        String taskName = i18nComponent.getI18nValue(ExportConstant.RV_CALI_IMPORT_ERROR_FILE);
        return buildDownInfo(userCache, fileName, taskName);
    }
}
