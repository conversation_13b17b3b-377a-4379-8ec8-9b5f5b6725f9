package com.yxt.talent.rv.application.xpd.result.strategy;

import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户维度结果填充策略上下文
 * 负责管理和选择合适的填充策略
 */
@Slf4j
@Service
public class UserDimResultFillContext {

    private final Map<UserDimResultFillStrategyType, UserDimResultFillStrategy> strategyMap;

    public UserDimResultFillContext(List<UserDimResultFillStrategy> strategies) {
        this.strategyMap = strategies.stream()
            .collect(Collectors.toMap(
                UserDimResultFillStrategy::getStrategyType,
                Function.identity()
            ));
    }

    /**
     * 使用指定策略填充用户维度结果
     *
     * @param strategyType 策略类型
     * @param orgId 机构ID
     * @param xpdId 盘点项目ID
     * @param userIds 用户ID列表
     * @param xpdGrid 宫格信息
     * @param userInfoMap 用户信息映射
     */
    public void fillUserDimResults(UserDimResultFillStrategyType strategyType,
                                  String orgId, String xpdId, List<String> userIds,
                                  XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap) {
        UserDimResultFillStrategy strategy = strategyMap.get(strategyType);
        if (strategy == null) {
            log.warn("未找到对应的用户维度结果填充策略: {}", strategyType);
            return;
        }

        log.debug("使用策略 {} 填充用户维度结果", strategyType);
        strategy.fillUserDimResults(orgId, xpdId, userIds, xpdGrid, userInfoMap);
    }

    /**
     * 从数据集表填充用户维度结果
     *
     * @param orgId 机构ID
     * @param xpdId 盘点项目ID
     * @param userIds 用户ID列表
     * @param xpdGrid 宫格信息
     * @param userInfoMap 用户信息映射
     */
    public void fillFromDataset(String orgId, String xpdId, List<String> userIds,
                               XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap) {
        fillUserDimResults(UserDimResultFillStrategyType.DATASET, orgId, xpdId, userIds, xpdGrid, userInfoMap);
    }

    /**
     * 从业务库填充用户维度结果
     *
     * @param orgId       机构ID
     * @param xpdId       盘点项目ID
     * @param userIds     用户ID列表
     * @param userInfoMap 用户信息映射
     */
    public void fillFromBusiness(String orgId, String xpdId, List<String> userIds,
        Map<String, XpdUserDimResultsVO> userInfoMap) {
        fillUserDimResults(UserDimResultFillStrategyType.BUSINESS, orgId, xpdId, userIds, null, userInfoMap);
    }
}
