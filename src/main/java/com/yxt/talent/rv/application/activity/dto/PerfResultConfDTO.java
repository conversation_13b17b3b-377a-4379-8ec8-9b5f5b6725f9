package com.yxt.talent.rv.application.activity.dto;

import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "绩效活动针对绩效评估结果")
public class PerfResultConfDTO {

    /**
     * 活动ID,rv_activity_perf.id
     */
    @Schema(description = "活动ID,rv_activity_perf.id")
    private String actvPerfId;

    /**
     * 结果名称
     */
    @Schema(description = "结果名称")
    @NotBlank(message = "apis.sptalentrv.perf.conf.resultname.notnull")
    @Size(max = 200, message = "apis.sptalentrv.perf.conf.resultname.maxlength")
    private String resultName;

    /**
     * 得分
     */
    @Schema(description = "得分")
    @Max(value = 999, message = "apis.sptalentrv.perf.conf.score.maxlength")
    private BigDecimal score;

    /**
     * 是否达标(0-不达标, 1-达标)
     */
    @Schema(description = "是否达标(0-不达标, 1-达标)")
    @NotNull(message = "apis.sptalentrv.perf.conf.qualified.notnull")
    @Max(value = 1, message = "apis.sptalentrv.perf.conf.qualified.error")
    @Min(value = 0, message = "apis.sptalentrv.perf.conf.qualified.error")
    private Integer qualified;

//    /**
//     * 规则JSON配置
//     */
//    @Schema(description = "规则JSON配置")
//    @NotBlank(message = ExceptionKeys.PERF_RESULT_CONF_RULECONF_NULL)
//    private String ruleConf;

    /**
     * 规则JSON配置
     */
    @Schema(description = "规则Bean")
    @NotNull(message = ExceptionKeys.PERF_RESULT_CONF_RULECONF_NULL)
    private SpRuleBean ruleConfBean;

    /**
     * 规则描述
     */
    @Schema(description = "规则描述")
    private String ruleDisplay;

    @Schema(description = "排序号，最小的放前面")
    private Integer orderNum;

}
