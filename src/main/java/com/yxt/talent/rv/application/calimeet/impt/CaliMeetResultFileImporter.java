package com.yxt.talent.rv.application.calimeet.impt;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserImportDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserImportsDTO;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.application.common.DictQryAppService;
import com.yxt.talent.rv.application.prj.calc.PrjCalculator;
import com.yxt.talent.rv.application.prj.rule.dto.PrjDimRuleDTO;
import com.yxt.talent.rv.application.prj.rule.dto.UserScoreLevelResultDTO;
import com.yxt.talent.rv.application.prj.rule.legacy.PrjDimRuleAppService;
import com.yxt.talent.rv.application.prj.rule.legacy.PrjDimRuleCondAppService;
import com.yxt.talent.rv.application.user.UserQryAppService;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetVO;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.transfer.FileProcessedResult;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.org.OrgSettingMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleCondPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.file.EasyExcelListener;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExporter;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImportSupport;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImporter;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileReader;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 导入校准结果
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaliMeetResultFileImporter
        extends FileImporter<CaliMeetUserImportDTO, FileProcessedResult<CaliMeetUserImportDTO>, FileImportResult> {
    private final CaliMeetAppService caliMeetAppService;
    private final DlcComponent dlcComponent;
    private final CaliMeetResultByLevelImportErrorExportStrategy importErrorLevelStrategy;
    private final CaliMeetResultByScoreImportErrorExportStrategy importErrorScoreStrategy;
    private final UserQryAppService userQryAppService;
    private final PrjDimRuleAppService prjDimRuleAppService;
    private final PrjDimRuleCondAppService prjDimRuleCondAppService;
    private final PrjCalculator prjCalculator;
    private final PerfGradeMapper perfGradeMapper;
    private final CaliMeetUserResultMapper caliMeetUserResultMapper;
    private final CaliMeetUserMapper caliMeetUserMapper;
    private final L10nAclService l10nAclService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final I18nComponent i18nComponent;
    private final XpdDimMapper xpdDimMapper;
    private final XpdMapper xpdMapper;
    private final SpsdAclService spsdAclService;
    private final CaliMeetMapper caliMeetMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;

    /**
     * 导入校准结果
     *
     * @param caliMeetId    校准会id
     * @param type          数据类型：1-维度等级，2-维度评分
     * @param fileImportCmd 文件导入参数
     * @param file          如果是通过文件流上传, 该参数应有值
     * @param currentUser
     * @return
     */
    @Nullable
    public FileImportResult toImport(
            String caliMeetId, int type, FileImportCmd fileImportCmd, MultipartFile file,
            UserCacheDetail currentUser) {
        String orgId = currentUser.getOrgId();
        String lockKey = String.format(RedisKeys.LK_CALI_MEET_USER_RESULT_TYPE_IMPT, orgId,
                caliMeetId, type);
        String fileId = fileImportCmd.getFileId();

        // 处理导入数据
        Function<List<CaliMeetUserImportDTO>, FileProcessedResult<CaliMeetUserImportDTO>>
                dataProcessor =
                importDataList -> dataProcess(caliMeetId, type, currentUser, importDataList);

        // 处理错误数据
        UnaryOperator<FileProcessedResult<CaliMeetUserImportDTO>> errorProcessor =
                processedResult -> errorProcess(type, processedResult);

        FileImportSupport<CaliMeetUserImportDTO, FileProcessedResult<CaliMeetUserImportDTO>, FileImportResult>
                fileImportSupport =
                FileImportSupport.<CaliMeetUserImportDTO, FileProcessedResult<CaliMeetUserImportDTO>, FileImportResult>builder()
                        .file(file)
                        .fileId(fileId)
                        .tranId(lockKey)
                        .startRow(0)
                        .orgId(orgId)
                        .operator(currentUser.getUserId())
                        .importContentClazz(CaliMeetUserImportDTO.class)
                        .dataReader(new CliMeetResultFileReader(file, fileId))
                        .dataProcessor(dataProcessor)
                        .errorProcessor(errorProcessor)
                        .resultProcessor(this::buildDefaultImportResult)
                        .build();

        return toImport(fileImportSupport);
    }

    private FileImportResult buildDefaultImportResult(
            FileProcessedResult<CaliMeetUserImportDTO> processedResult) {
        return FileImportResult.builder()
                .totalCount(processedResult.getTotalCount())
                .failedCount(processedResult.getFailedCount())
                .successCount(processedResult.getSuccessCount())
                .filePath(processedResult.getErrorFilePath())
                .build();
    }

    @NotNull
    private FileProcessedResult<CaliMeetUserImportDTO> errorProcess(
            int type, FileProcessedResult<CaliMeetUserImportDTO> processedResult) {
        List<CaliMeetUserImportDTO> failedData = processedResult.getFailedData();
        if (CollectionUtils.isEmpty(failedData)) {
            return processedResult;
        }
        String subName = (type == 1 ? "导入校准结果（维度等级）错误" : "导入校准结果（维度评分）错误");
        String fileName = subName + DateTimeUtil.dateToString(
                new Date(),
                DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS);
        OutputStrategy outputStrategy =
                (type == 1 ? importErrorLevelStrategy : importErrorScoreStrategy);
        DynamicExcelExportContent dynamicHeaderFileExportContent = handleFailedData(failedData);
        String errorFilePath =
                dlcComponent.upload2TemporaryDisk(fileName + FileConstants.FILE_SUFFIX_XLSX,
                        dynamicHeaderFileExportContent, outputStrategy);
        processedResult.setErrorFilePath(errorFilePath);
        return processedResult;
    }

    @NotNull
    private FileProcessedResult<CaliMeetUserImportDTO> dataProcess(
            String caliMeetId, int type, UserCacheDetail currentUser,
            List<CaliMeetUserImportDTO> importDataList) {
        CaliMeetVO caliMeetVO =
                caliMeetAppService.getMeeting4GetById(caliMeetId, currentUser.getOrgId(),
                        currentUser.getLocale());
        CaliMeetUserImportsDTO preBean =
                prepareData(importDataList, caliMeetId, caliMeetVO.getProjectId(), currentUser);
        // 验证数据
        importDataValid(preBean, caliMeetId, currentUser.getOrgId(), type);
        calcAndSave(preBean, type, currentUser, caliMeetVO.getProjectId());
        return new FileProcessedResult<>(
                preBean.getPrepareList(), preBean.getFailedList(), importDataList);
    }

    /**
     * 备菜
     */
    private CaliMeetUserImportsDTO prepareData(
            List<CaliMeetUserImportDTO> importDataList, String meetingId, String projectId,
            UserCacheDetail currentUser) {
        log.info("LOG61830:meetingId={}", meetingId);

        CaliMeetUserImportsDTO preBean = new CaliMeetUserImportsDTO();
        String orgId = currentUser.getOrgId();
        /*List<PrjDimConfPO> prjDimConfs =
                prjDimConfMapper.listActiveDimConfByPrjId(orgId, projectId);*/
        List<String> userNames =
                importDataList.stream().map(CaliMeetUserImportDTO::getUserName)
                        .collect(Collectors.toList());
        List<UdpLiteUserPO> rvUdpUsers =
                userQryAppService.findOrgUsersByUserNameIncludeDeleted(currentUser.getOrgId(), userNames);
        preBean.setImportDataList(importDataList);
        //preBean.setPrjDimConfs(prjDimConfs);
        XpdPO xpdPO = xpdMapper.selectById(projectId);
        List<XpdDimPO> dimList = xpdDimMapper.selectByXpdId(xpdPO.getOrgId(), projectId, null);
        preBean.setUdpLiteUsers(rvUdpUsers);
        preBean.setDimList(dimList);

        return preBean;
    }

    /**
     * 处理导入失败的数据
     */
    private DynamicExcelExportContent handleFailedData(
            @NonNull List<CaliMeetUserImportDTO> failedList) {
        // 封装 DynamicHeaderExport
        DynamicExcelExportContent result = new DynamicExcelExportContent();
        result.setHeaders(initFailedDataHeaders(failedList));
        result.setSheets(
                FileExporter.buildSingleSheets(FileConstants.SHEET_1, FileConstants.SHEET_1));
        result.setData(initFailedData(failedList));
        return result;
    }

    private Map<String, List<List<String>>> initFailedDataHeaders(
            List<CaliMeetUserImportDTO> failedList) {

        Map<String, List<List<String>>> headerMap = new HashMap<>(8);
        List<List<String>> headers = new ArrayList<>();

        CaliMeetUserImportDTO data = failedList.get(0);
        data.getHeadNames().forEach(a -> {
            List<String> strings = new ArrayList<>();
            strings.add(a);
            headers.add(strings);
        });

        List<String> strings = new ArrayList<>();
        strings.add("错误原因");
        headers.add(strings);

        headerMap.put(FileConstants.SHEET_1, headers);
        return headerMap;
    }

    private Map<String, List<Object>> initFailedData(List<CaliMeetUserImportDTO> failedList) {
        Map<String, List<Object>> result = new HashMap<>(8);
        List<Object> data = new ArrayList<>();

        failedList.forEach(e -> {
            List<String> every = new ArrayList<>();
            every.add(e.getFullName());
            every.add(e.getUserName());
            every.add(e.getDeptName());
            every.add(e.getPositionName());
            every.add(e.getSuggestion());

            for (String key : e.getOriginalMap().keySet()) {
                every.add(e.getOriginalMap().get(key));
            }
            every.add(e.getErrorMsg());
            data.add(every);
        });

        result.put(FileConstants.SHEET_1, data);
        return result;
    }

    /**
     * 验证数据
     */
    private void importDataValid(
            CaliMeetUserImportsDTO preBean, String meetingId, String orgId, int type) {
        // 名称 - dimConfMap
        Map<String, PrjDimConfPO> dimConfMap = preBean.getPrjDimConfs()
                .stream()
                .collect(Collectors.toMap(PrjDimConfPO::getDimensionName, a -> a));
        List<XpdDimPO> dimList = preBean.getDimList();
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, dimIds);
        List<String> dimNames = baseDimDetails.stream().map(DimensionList4Get::getDmName).toList();
        Map<String, String> dimNameMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getDmName, DimensionList4Get::getId);

        // 账号 - id映射
        Map<String, UdpLiteUserPO> userNameMap = preBean.getUdpLiteUsers()
                .stream()
                .collect(Collectors.toMap(UdpLiteUserPO::getUsername, a -> a));
        List<String> udpUserIds =
                preBean.getUdpLiteUsers().stream().map(UdpLiteUserPO::getId)
                        .collect(Collectors.toList());

        Set<String> fullNameList = preBean.getImportDataList()
                .stream()
                .map(CaliMeetUserImportDTO::getFullName)
                .collect(Collectors.toSet());

        //兼容国际化，根据姓名获取userId
        Map<String, UdpLiteUserPO> udpLiteUserPOMap = new HashMap<>();
        Set<String> l10nUserIds =
                l10nAclService.exactQueryContentByKeys(true, List.of(orgId),
                        ResourceTypeEnum.USER, fullNameList);
        List<UdpLiteUserPO> l10nUsers =
                udpLiteUserMapper.selectByUserIds(orgId, new ArrayList<>(l10nUserIds));
        if (CollectionUtils.isNotEmpty(l10nUsers)) {
            udpLiteUserPOMap = StreamUtil.list2map(l10nUsers, UdpLiteUserPO::getUsername);
        }

        Map<String, UdpLiteUserPO> finalUdpLiteUserPOMap = udpLiteUserPOMap;
        preBean.getImportDataList().forEach(caliMeetUserImportDTO -> {
            if (null != finalUdpLiteUserPOMap &&
                null != finalUdpLiteUserPOMap.get(caliMeetUserImportDTO.getUserName())) {
                caliMeetUserImportDTO.setFullName(
                        finalUdpLiteUserPOMap.get(caliMeetUserImportDTO.getUserName())
                                .getFullname());
            }
        });

        // 查询出待更新数据
        CaliMeetPO caliMeetPO = caliMeetMapper.selectByIdAndOrgId(meetingId, orgId);
        String projectId = caliMeetPO.getProjectId();
        // 获取维度数据
        /*xpdGridMapper.selectByXpdId(orgId, projectId);*/
        List<XpdGridLevelPO> gridLevelList = xpdGridLevelMapper.listByXpdId(orgId, projectId);
        Map<String, Integer> gridLevelMap =
            StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getLevelName, XpdGridLevelPO::getOrderIndex);
        List<CaliMeetUserPO> calMeetUsers =
                caliMeetUserMapper.listByOrgIdAndMeetingId(orgId, meetingId);
        List<CaliMeetUserResultPO> resultList =
                caliMeetUserResultMapper.listByOrgIdAndMeetIdAndUserIdIn(orgId, meetingId,
                        udpUserIds);
        Map<String, CaliMeetUserPO> meetingUserMap =
                calMeetUsers.stream().collect(Collectors.toMap(CaliMeetUserPO::getUserId, a -> a));
        Map<String, List<CaliMeetUserResultPO>> resultMap =
                resultList.stream().collect(Collectors.groupingBy(CaliMeetUserResultPO::getUserId));

        for (CaliMeetUserImportDTO mu : preBean.getImportDataList()) {
            if (StringUtils.isEmpty(mu.getFullName())) {
                // 姓名为空
                mu.setErrorMsg(i18nComponent.getI18nValue("apis.sptalentrv.meeting.cali.import.fullname.empty"));
                preBean.getFailedList().add(mu);
                continue;
            }
            if (StringUtils.isEmpty(mu.getUserName())) {
                // 账号为空
                mu.setErrorMsg(i18nComponent.getI18nValue("apis.sptalentrv.meeting.cali.import.username.empty"));
                preBean.getFailedList().add(mu);
                continue;
            }
//            // 账号存在 且 账号和姓名匹配
//            if (!userNameMap.containsKey(mu.getUserName()) ||
//                !mu.getFullName().equals(userNameMap.get(mu.getUserName()).getFullname())) {
//                // 人员不存在
//                mu.setErrorMsg(i18nComponent.getI18nValue("apis.sptalentrv.meeting.cali.import.username.not.exist"));
//                preBean.getFailedList().add(mu);
//                continue;
//            }
            UdpLiteUserPO udpLiteUserPO = userNameMap.get(mu.getUserName());
            if (Objects.isNull(udpLiteUserPO)) {
                log.warn("LOG20723:{}", mu.getUserName());
                continue;
            }
            String userId = udpLiteUserPO.getId();
            if (!meetingUserMap.containsKey(userId)) {
                // 用户不在校准人员列表
                mu.setErrorMsg(i18nComponent.getI18nValue("apis.sptalentrv.meeting.cali.import.user.not.exist"));
                preBean.getFailedList().add(mu);
                continue;
            }

            // 客户导入发展建议为空，则数据库中的发展建议也置为空
            if (StringUtils.isEmpty(mu.getSuggestion())) {
                mu.setSuggestion(StringUtils.EMPTY);
            }

            if (StringUtils.isNotEmpty(mu.getSuggestion()) && mu.getSuggestion().length() > 800) {
                mu.setErrorMsg(i18nComponent.getI18nValue("apis.sptalentrv.meeting.cali.import.suggestion"));
                preBean.getFailedList().add(mu);
                continue;
            }

            // 维度校验
            List<CaliMeetUserResultPO> tempList = new ArrayList<>();
            validateAndProcessData(mu, dimConfMap, resultMap, tempList, userId, type, orgId, gridLevelMap,
                dimNameMap);

            // 验证无错误 ，加入列表
            if (StringUtils.isEmpty(mu.getErrorMsg())) {
                preBean.getCmrList().addAll(tempList);
                preBean.getMuList().add(meetingUserMap.get(userId));
                preBean.getPrepareList().add(mu);
            } else {
                preBean.getFailedList().add(mu);
            }
        }
    }

    private void validateAndProcessData(
            CaliMeetUserImportDTO mu, Map<String, PrjDimConfPO> dimConfMap,
            Map<String, List<CaliMeetUserResultPO>> resultMap, List<CaliMeetUserResultPO> tempList,
            String userId, int type, String orgId, Map<String, Integer> gridLevelMap,
        Map<String, String> dimNameMap) {

        Map<String, CaliMeetUserResultPO> forResultMap = resultMap.get(userId)
                .stream()
                .collect(Collectors.toMap(
                        CaliMeetUserResultPO::getDimensionId,
                        Function.identity()));

        /*Map<String, Integer> dictMap = getDictMap(orgId);*/

        for (String dimName : mu.getDimensionMap().keySet()) {
            String errorMsg = validateData(mu,dimName, type, gridLevelMap, dimNameMap);
            if (errorMsg != null) {
                mu.setErrorMsg(errorMsg);
                break;
            }

            //String dimId = dimConfMap.get(dimName).getDimensionId();
            String dimId = dimNameMap.get(dimName);
            if (forResultMap.containsKey(dimId)) {
                tempList.add(forResultMap.get(dimId));
            }
        }
    }

    @Nullable
    private String validateData(
            CaliMeetUserImportDTO mu, String dimName,
            int type, Map<String, Integer> gridLevelMap, Map<String, String> dimNameMap) {
        if (!dimNameMap.containsKey(dimName)) {
            return i18nComponent.getI18nValue("apis.sptalentrv.meeting.cali.import.dim.not.exist");
        }

        /*if (dimConfMap.get(dimName).getToolType() == 3 && type == 2) {
            return ErrorMessageConstants.DIMENSION_TOOLTYPE_3;
        }*/

        String dimValue = mu.getDimensionMap().get(dimName);
        if (dimValue != null) {
            if (type == 1 && !gridLevelMap.containsKey(dimValue)) {
                return ErrorMessageConstants.DIMENSION_LEVEL_RANGE;
            }

            /*if (type == 2) {
                return validatePerformance(dimName, dimValue, dictMap);
            }*/
        }

        return null;
    }

    @Nullable
    private String validatePerformance(
            String dimName, String dimValue, Map<String, Integer> dictMap) {
        /*if (dimName.contains("绩效") && !dictMap.containsKey(dimValue)) {
            return ErrorMessageConstants.DIMENSION_PERF_RANGE;
        }*/

        if (!dimName.contains("绩效")) {
            try {
                new BigDecimal(dimValue);
            } catch (Exception e) {
                return ErrorMessageConstants.DIMENSION_RANGE;
            }
        }

        return null;
    }

    private Map<String, Integer> getDictMap(String orgId) {
        //List<DictPO> dictList = dictQryAppService.list(1);
        Map<String, Integer> dictMap = new HashMap<>(8);
        //OrgSettingPO orgSetting = orgSettingMapper.selectByOrgId(orgId);
        //boolean isOrgGrades = null != orgSetting && orgSetting.getGradeSet() == 1;
        //if (isOrgGrades) {
        Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(perfGrades)) {
            dictMap = perfGrades.stream()
                    .collect(Collectors.toMap(
                            PerfGradePO::getGradeName,
                            PerfGradePO::getGradeValue, (a, b) -> a));
        }
        /*} else {
            dictMap = dictList.stream()
                    .collect(Collectors.toMap(DictPO::getDictName, DictPO::getDictValue));
        }*/
        return dictMap;
    }

    /**
     * 计算导入结果&保存导入数据
     */
    private void calcAndSave(
            CaliMeetUserImportsDTO preBean, int type, UserCacheDetail currentUser,
            String projectId) {

        Map<String, String> userNameMap = preBean.getUdpLiteUsers()
                .stream()
                .collect(Collectors.toMap(UdpLiteUserPO::getUsername, UdpLiteUserPO::getId));

        Map<String, String> dimNameMap = preBean.getPrjDimConfs()
                .stream()
                .collect(Collectors.toMap(
                        PrjDimConfPO::getDimensionName,
                        PrjDimConfPO::getDimensionId));
        /*Map<String, String> dimIdMap = preBean.getPrjDimConfs()
                .stream()
                .collect(Collectors.toMap(
                        PrjDimConfPO::getDimensionId,
                        PrjDimConfPO::getDimensionName));*/

        List<XpdDimPO> dimList = preBean.getDimList();
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        XpdPO xpdPO = xpdMapper.selectByPrimaryKey(projectId);
        String orgId = xpdPO.getOrgId();
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, dimIds);
        Map<String, String> dimIdMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, DimensionList4Get::getDmName);

        List<XpdGridLevelPO> gridLevelList = xpdGridLevelMapper.listByXpdId(orgId, projectId);
        Map<String, Integer> gridLevelMap =
            StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getLevelName, XpdGridLevelPO::getOrderIndex);

        if (type == 1) {
            // 直接设置维度等级到 两个实体对象列表
            // 将计算结果设置到 cmrList,muList 两个列表中
            prepareResultDataForLevel(preBean.getImportDataList(), userNameMap, dimIdMap,
                    preBean.getCmrList(), preBean.getMuList(), gridLevelMap);
        } else {
            // 需要将得分转为等级,然后设置到两个实体对象列表
            // 准备计算需要的数据
            Map<String, List<UserScoreLevelResultDTO>> uslrMap = new HashMap<>(8);
            prepareCalcData(preBean.getImportDataList(), userNameMap, dimNameMap, uslrMap,
                    currentUser.getOrgId());

            // 计算
            doCalc(projectId, currentUser.getOrgId(), uslrMap);

            // 将计算结果设置到 cmrList,muList 两个列表中
            prepareResultDataForScore(preBean.getImportDataList(), userNameMap, uslrMap,
                    preBean.getCmrList(), preBean.getMuList());
        }

        // 保存cmrList,muList两个列表中的数据
        saveResultData(preBean.getCmrList(), preBean.getMuList());
    }

    /**
     * 准备计算需要的数据<br>
     * 将导入数据对象->转换为->计算接口所需的对象
     *
     * @param importDatas 已完成验证的导入数据
     * @param userNameMap Map<key,value>，key=userName value=用户id
     * @param dimNameMap  Map<key,value>，key=维度名称 value=维度Id
     * @param uslrMap     Map<key,List<UserScoreLevelResult>> key = 维度Id
     */
    private void prepareCalcData(
            List<CaliMeetUserImportDTO> importDatas, Map<String, String> userNameMap,
            Map<String, String> dimNameMap, Map<String, List<UserScoreLevelResultDTO>> uslrMap,
            String orgId) {
        // 准备计算数据
        Map<String, Integer> dictMap = new HashMap<>(8);
        // 判断是否开启自定义绩效等级
        //OrgSettingPO orgSetting = orgSettingMapper.selectByOrgId(orgId);
        //boolean isOrgGrades = null != orgSetting && orgSetting.getGradeSet() == 1;
        //if (isOrgGrades) {
        Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(perfGrades)) {
            dictMap = perfGrades.stream()
                    .collect(Collectors.toMap(
                            PerfGradePO::getGradeName,
                            PerfGradePO::getGradeValue));
        }
        /*} else {
            List<DictPO> dictList = dictQryAppService.list(1); // 1 - 绩效字典  魔法值修改
            dictMap = dictList.stream()
                    .collect(Collectors.toMap(DictPO::getDictName, DictPO::getDictValue));
        }*/
        for (CaliMeetUserImportDTO mu : importDatas) {
            if (!StringUtils.isEmpty(mu.getErrorMsg())) {
                continue;
            }
            String userId = userNameMap.get(mu.getUserName());
            for (String dimName : mu.getDimensionMap().keySet()) {

                String dimId = dimNameMap.get(dimName);
                String dimValue = mu.getDimensionMap().get(dimName);
                if (dimValue == null) {
                    continue;
                }

                if (!uslrMap.containsKey(dimId)) {
                    uslrMap.put(dimId, new ArrayList<>());
                }

                UserScoreLevelResultDTO uslr = new UserScoreLevelResultDTO();
                uslr.setDimensionId(dimId);
                uslr.setUserId(userId);
                if (dictMap.containsKey(dimValue)) {
                    // 绩效 A、B、C 转为 1、2、3
                    uslr.setScore(new BigDecimal(dictMap.get(dimValue)));
                } else {
                    uslr.setScore(new BigDecimal(dimValue));
                }

                uslrMap.get(dimId).add(uslr);
            }
        }
    }

    /**
     * 根据维度分组计算
     *
     * @param projectId 校准会所属项目id
     * @param orgId     机构id
     * @param uslrMap   Map<key,List<UserScoreLevelResult>> key = 维度Id ，待计算数据，计算完成结果结果存入指定字段
     */
    private void doCalc(
            String projectId, String orgId, Map<String, List<UserScoreLevelResultDTO>> uslrMap) {
        // 查询出每个维度的 等级设置类型（0-枚举，1-百分比，2-绝对值）
        List<PrjDimRuleDTO> rule4GetList = prjDimRuleAppService.findByProjectId(projectId, orgId);
        // Map的value虽然是list 但正常情况只有一个值。
        Map<String, List<PrjDimRuleDTO>> ruleMap =
                rule4GetList.stream().collect(Collectors.groupingBy(PrjDimRuleDTO::getDimensionId));

        // 规则分级条件
        List<PrjDimRuleCondPO> prjRuleCondList =
                prjDimRuleCondAppService.findByProjectId(projectId, orgId);
        Map<String, List<PrjDimRuleCondPO>> conditionMap = prjRuleCondList.stream()
                .collect(Collectors.groupingBy(PrjDimRuleCondPO::getDimensionId));

        // 按维度计算
        for (Map.Entry<String, List<UserScoreLevelResultDTO>> entry : uslrMap.entrySet()) {
            List<UserScoreLevelResultDTO> userScoreLevelResultDtos = entry.getValue();
            Integer classType = ruleMap.get(userScoreLevelResultDtos.get(0).getDimensionId())
                    .get(0)
                    .getClassType();
            if (classType == null) {
                // 说明没有配置计算规则，放弃这个维度的计算(导入类型的就没有规则)
                continue;
            }
            List<PrjDimRuleCondPO> prjRuleConds =
                    conditionMap.get(userScoreLevelResultDtos.get(0).getDimensionId());
            if (classType == 0) {
                // 枚举, 与dict表保存一致，dict是int类型 (uslrMap中维度值是整型、ruleConditions是浮点，调用计算方法报错，所以转换一下)
                prjRuleConds.forEach(
                        a -> a.setSymbolValue(new BigDecimal(a.getSymbolValue().intValue())));
                // 如果导入的值在项目的分级规则中不存在，则不参与运算；
                Map<BigDecimal, List<PrjDimRuleCondPO>> ruleConditionMap = prjRuleConds.stream()
                        .collect(Collectors.groupingBy(PrjDimRuleCondPO::getSymbolValue));
                userScoreLevelResultDtos = userScoreLevelResultDtos.stream()
                        .filter(a -> ruleConditionMap.containsKey(a.getScore()))
                        .collect(Collectors.toList());
            }
            prjCalculator.score2LevelCalibration(
                    userScoreLevelResultDtos, classType, prjRuleConds, projectId, orgId);
        }
    }

    /**
     * 准备好入库的数据【CalibrationMeetingResult、MeetingUser】【type=2 维度得分导入】
     *
     * @param importDatas 已完成验证的导入数据
     * @param userNameMap Map<key,value>，key=userName value=用户id
     * @param uslrMap     Map<key,List<UserScoreLevelResult>> key = 维度Id ，已计算数据
     * @param cmrList
     * @param muList
     */
    private void prepareResultDataForScore(
            List<CaliMeetUserImportDTO> importDatas, Map<String, String> userNameMap,
            Map<String, List<UserScoreLevelResultDTO>> uslrMap, List<CaliMeetUserResultPO> cmrList,
            List<CaliMeetUserPO> muList) {
        // 设置发展建议
        Map<String, CaliMeetUserPO> muMap =
                muList.stream().collect(Collectors.toMap(CaliMeetUserPO::getUserId, a -> a));
        for (CaliMeetUserImportDTO mu : importDatas) {
            if (!StringUtils.isEmpty(mu.getErrorMsg())) {
                continue;
            }
            muMap.get(userNameMap.get(mu.getUserName())).setSuggestion(mu.getSuggestion());
        }

        // 设置维度结果
        Map<String, CaliMeetUserResultPO> prepareCmrMap =
                cmrList.stream().collect(Collectors.toMap(a -> {
                    // 使用用户id + 维度id 作为唯一key
                    return a.getUserId() + a.getDimensionId();
                }, b -> b));
        for (Map.Entry<String, List<UserScoreLevelResultDTO>> entry : uslrMap.entrySet()) {
            entry.getValue().forEach(uslr -> {
                // 使用用户id + 维度id
                String prepareCmrMapKey = uslr.getUserId() + uslr.getDimensionId();
                if (prepareCmrMap.containsKey(prepareCmrMapKey)) {
                    prepareCmrMap.get(prepareCmrMapKey)
                            .setCalibrationLevel(uslr.getConditionLevel());
                    prepareCmrMap.get(prepareCmrMapKey).setCalibrationScore(uslr.getScore());
                }
            });
        }
    }

    /**
     * 准备好入库的数据【CalibrationMeetingResult、MeetingUser】 【type=1 维度等级导入】
     *
     * @param importDatas 已完成验证的导入数据
     * @param userNameMap Map<key,value>，key=userName value=用户id
     * @param dimIdMap    Map<key,value> key = 维度Id value = 维度名称
     * @param cmrList
     * @param muList
     */
    private void prepareResultDataForLevel(
            List<CaliMeetUserImportDTO> importDatas, Map<String, String> userNameMap,
            Map<String, String> dimIdMap, List<CaliMeetUserResultPO> cmrList,
            List<CaliMeetUserPO> muList, Map<String, Integer> gridLevelMap) {

        Map<String, CaliMeetUserPO> muMap =
                muList.stream().collect(Collectors.toMap(CaliMeetUserPO::getUserId, a -> a));
        Map<String, List<CaliMeetUserResultPO>> cmrMapList =
                cmrList.stream().collect(Collectors.groupingBy(CaliMeetUserResultPO::getUserId));
        for (CaliMeetUserImportDTO mu : importDatas) {
            if (!StringUtils.isEmpty(mu.getErrorMsg())) {
                continue;
            }
            String userId = userNameMap.get(mu.getUserName());
            // 设置发展建议
            muMap.get(userId).setSuggestion(mu.getSuggestion());
            // 设置维度等级
            cmrMapList.get(userId).forEach(a -> {
                String dimName = dimIdMap.get(a.getDimensionId());
                String level = mu.getDimensionMap().get(dimName);
                if (level == null) {
                    level = "";
                }
                a.setCalibrationLevel(gridLevelMap.get(level));
            });
        }
    }

    /**
     * 保存数据
     *
     * @param cmrList
     * @param muList
     */
    private void saveResultData(
            List<CaliMeetUserResultPO> cmrList, List<CaliMeetUserPO> muList) {
        for (CaliMeetUserPO caliMeetUserPo : muList) {
            caliMeetUserMapper.updateById(caliMeetUserPo);
        }

        cmrList.forEach( x -> {
            if (x.getCalibrationLevel() != null) {
                x.setInitType(1);
            }

        });
        for (CaliMeetUserResultPO caliMeetUserResultPo : cmrList) {
            caliMeetUserResultMapper.updateById(caliMeetUserResultPo);
        }
    }


    @jakarta.annotation.Nonnull
    private List<CaliMeetUserImportDTO> generateMeetingUser4ExcelImportList(
            EasyExcelListener listener) {

        List<Map<Integer, String>> list = listener.getData();
        List<CaliMeetUserImportDTO> muList = new ArrayList<>(list.size());

        // 表头
        Map<Integer, String> headMap = list.get(0);
        ArrayList<String> headNames = new ArrayList<>();
        for (int i = 0; i < headMap.size(); i++) {
            headNames.add(headMap.get(i));
        }
        // 校准后翻译
        String caliAfter = i18nComponent.getI18nValue("apis.sptalentrv.meeting.user.export.header.after");
        // 内容
        for (int j = 1; j < list.size(); j++) {
            Map<Integer, String> map = list.get(j);
            int i = 0;
            CaliMeetUserImportDTO temp = new CaliMeetUserImportDTO();
            temp.setFullName(map.get(i++));
            temp.setUserName(map.get(i++));
            temp.setDeptName(map.get(i++));
            temp.setPositionName(map.get(i++));
            temp.setSuggestion(map.get(i++));
            for (; i < headMap.size(); i++) {
                String head = headMap.get(i);
                temp.getOriginalMap().put(head, map.get(i));
                if (head.contains(caliAfter)) {
                    temp.getDimensionMap().put(head.replace(caliAfter, ""), map.get(i));
                }
            }
            temp.setHeadNames(headNames);
            muList.add(temp);
        }
        return muList;
    }

    private static class ErrorMessageConstants {
        /**
         * 姓名为必填项
         */
        static final String FULLNAME_EMPTY = "姓名为空";

        /**
         * 账号为必填项
         */
        static final String USERNAME_EMPTY = "账号为空";

        /**
         * 账号不存在
         */
        static final String USERNAME_NOT_EXIST = "人员不存在";

        /**
         * 用户在校准人员列表
         */
        static final String USER_NOT_EXIST = "用户不在校准人员列表内";

        /**
         * 发展建议不可超过800个字符
         */
        static final String SUGGESTION_SIZE = "发展建议不可超过800个字符";

        /**
         * 校准后维度只能是高、中、低
         */
        static final String DIMENSION_LEVEL_RANGE = "等级填写信息有误";

        /**
         * 校准后绩效维度有误，可填写【S+、S、S-、A+、A、A- 等】
         */
        static final String DIMENSION_PERF_RANGE = "评分填写信息有误";

        /**
         * 维度平分有误，只可填写数值
         */
        static final String DIMENSION_RANGE = "评分填写信息有误";

        /**
         * 维度【{0}】不存
         */
        static final String DIMENSION_NOT_EXIST = "维度不存在";

        /**
         * 盘点结果外部导入，无评分信息
         */
        static final String DIMENSION_TOOLTYPE_3 = "盘点结果外部导入，无评分信息";

        private ErrorMessageConstants() {
        }
    }

    @RequiredArgsConstructor
    private class CliMeetResultFileReader extends FileReader<CaliMeetUserImportDTO> {

        private final MultipartFile file;

        private final String fileId;

        @Override
        @jakarta.annotation.Nonnull
        public List<CaliMeetUserImportDTO> read() {
            return doReadExcel();
        }

        /**
         * 由于导入文件中有部分表头是动态生成的, 所以这里没办法使用静态导入的方式, 而是采用动态解析的方式来处理导入的数据
         */
        @jakarta.annotation.Nonnull
        private List<CaliMeetUserImportDTO> doReadExcel() {
            ExcelReader excelReader = null;
            List<CaliMeetUserImportDTO> importDataList;
            try (
                    InputStream inputStream = this.getInputStream(file, fileId)
            ) {
                excelReader = EasyExcelFactory.read(inputStream).build();
                ReadSheet sheet = excelReader.excelExecutor().sheetList().get(0);

                // 读取sheet
                EasyExcelListener listener = new EasyExcelListener();
                sheet.setCustomReadListenerList(Collections.singletonList(listener));
                sheet.setHeadRowNumber(0);
                excelReader.read(sheet);

                // 提取Excel数据
                importDataList = generateMeetingUser4ExcelImportList(listener);
            } catch (IOException e) {
                throw new ApiException(e.getMessage());
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
            }
            return importDataList;
        }
    }
}
