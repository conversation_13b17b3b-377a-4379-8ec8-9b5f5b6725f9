package com.yxt.talent.rv.application.xpd.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.talent.rv.application.common.dto.UserBaseInfoDTO;
import lombok.*;

/**
 * 人员宫格维度组视角下的结果
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class XpdDimCombGridResultDTO extends UserBaseInfoDTO {

    // 格子id
    private String cellId;

    // 格子编号
    private Integer cellIndex;

    @JsonProperty("xIndex")
    // x轴对应的层级编号，取自 rv_xpd_grid_level.order_index
    private Integer xIndex;

    @JsonProperty("yIndex")
    // y轴对应的层级编号，取自 rv_xpd_grid_level.order_index
    private Integer yIndex;

}
