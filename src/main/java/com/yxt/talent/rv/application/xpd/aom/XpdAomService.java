package com.yxt.talent.rv.application.xpd.aom;

import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.manager.arrange.ArrangeManager;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireBaseInfo;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.application.xpd.common.enums.XpdImportTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimRuleCalcRefEnum;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfResultConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XpdAomService {
    private final ArrangeManager arrangeManager;
    private final ActivityPerfResultConfMapper activityPerfResultConfMapper;
    private final XpdMapper xpdMapper;
    private final XpdImportMapper xpdImportMapper;
    private final SpsdAclService spsdAclService;
    private final XpdRuleConfMapper xpdRuleConfMapper;

    /**
     * aom 项目活动列表
     *
     * @param actvId 项目ID
     */
    public List<ActivityArrangeItem> activityList(String orgId, String actvId) {
        if (StringUtils.isBlank(actvId)) {
            return Lists.newArrayList();
        }
        //盘点项目itemType固定传0
        return arrangeManager.listArrangeItem(orgId, actvId, 0);
    }

    /**
     * aom 项目活动
     *
     * @param actvId 项目ID
     * @param refIds 活动ID
     */
    public List<ActivityArrangeItem> activityList(String orgId, String actvId, List<String> refIds) {

        List<ActivityArrangeItem> ret = new ArrayList<>();
        if (CollectionUtils.isEmpty(refIds)) {
            return new ArrayList<>();
        }
        List<ActivityArrangeItem> items = activityList(orgId, actvId);
        for (ActivityArrangeItem item : items) {
            if (refIds.contains(item.getRefId())) {
                ret.add(item);
            }
        }
        return ret;
    }

    /**
     * 查询项目下所有活动的扩展数据
     *
     * @param actvId 对应rv_xpd.aom_prj_id
     * @param refIds 活动的ID
     * @return list
     */
    public List<AomActvExtBO> activityIndicatorList(String orgId, String actvId, List<String> refIds) {
        List<ActivityArrangeItem> activityList = activityList(orgId, actvId, refIds);
        return BeanCopierUtil.convertList(activityList, actv -> {
            AomActvExtBO actvDto = new AomActvExtBO();
            actvDto.setRefRegId(actv.getRefRegId());
            actvDto.setActvRefId(actv.getRefId());
            actvDto.setActvRefName(actv.getRefName());
            AomActvExtDto actvExt = CommonUtils.tryParseObject(actv.getExt(), AomActvExtDto.class);
            if (actvExt != null) {
                actvDto.setIndicators(actvExt.getIndicators());
                actvDto.setPerfExtDto(actvExt.getPerfExtDto());
                actvDto.setEvalTypes(actvExt.getEvalTypes());
            }
            return actvDto;
        });
    }

    /**
     * 查询项目下所有活动的扩展数据
     *
     * @param actvId 对应rv_xpd.aom_prj_id
     * @return 活动列表
     */
    public List<AomActvExtBO> activityIndicatorList(String orgId, String actvId) {
        List<ActivityArrangeItem> activityList = activityList(orgId, actvId);
        return BeanCopierUtil.convertList(activityList, actv -> {
            AomActvExtBO actvDto = new AomActvExtBO();
            actvDto.setRefRegId(actv.getRefRegId());
            actvDto.setActvRefId(actv.getRefId());
            actvDto.setActvRefName(CommonUtils.firstNotEmpty(actv.getItemName(), actv.getRefName()));
            actvDto.setCreateTime(actv.getCreateTime());
            AomActvExtDto actvExt = CommonUtils.tryParseObject(actv.getExt(), AomActvExtDto.class);
            if (actvExt != null) {
                actvDto.setIndicators(actvExt.getIndicators());
                actvDto.setPerfExtDto(actvExt.getPerfExtDto());
                actvDto.setEvalTypes(actvExt.getEvalTypes());
            }
            return actvDto;
        });
    }

    public List<ActivityPerfResultConfPO> listPerfConfs(String orgId, String actvPerfId) {
        return activityPerfResultConfMapper.selectByActivityId(orgId, actvPerfId);
    }

    /**
     * 获取绑定指标的活动列表
     *
     * @param indicatorIds 指标IDs
     */
    public Map<String, List<ActivityInfoDTO>> listActivityByIndicators(String orgId, String xpdId, List<String> indicatorIds) {
        XpdPO xpd = xpdMapper.selectById(xpdId);
        Validate.isNotNull(xpd, ExceptionKeys.XPD_NOT_EXIST);
        String actvId = xpd.getAomPrjId();
        List<AomActvExtBO> aomActvExt = activityIndicatorList(orgId, actvId);
        if (CollectionUtils.isEmpty(aomActvExt)) {
            return new HashMap<>();
        }

        Map<String, List<ActivityInfoDTO>> results = new HashMap<>();
        for (String indicatorId : indicatorIds) {
            results.put(indicatorId, new ArrayList<>());
            List<ActivityInfoDTO> activities = new ArrayList<>();

            for (AomActvExtBO aomActvExtBO : aomActvExt) {
                if (CollectionUtils.isNotEmpty(aomActvExtBO.getIndicators())) {
                    for (AomActvIndicatorDto indicator : aomActvExtBO.getIndicators()) {
                        if (Objects.equals(indicator.getSdIndicatorId(), indicatorId)) {
                            ActivityInfoDTO activityInfoDto = new ActivityInfoDTO();
                            activityInfoDto.setActvId(aomActvExtBO.getActvRefId());
                            activityInfoDto.setActvName(aomActvExtBO.getActvRefName());
                            activityInfoDto.setActvType(0);
                            activityInfoDto.setIndicatorTotalScore(indicator.getTotalScore());
                            activities.add(activityInfoDto);
                            break;
                        }
                    }
                }
            }
            results.get(indicatorId).addAll(activities);
        }

        return results;
    }

    public Map<String, List<ActivityInfoDTO>> listImportsByIndicators(String orgId, XpdPO xpd, List<String> indicatorIds) {
        if (CollectionUtils.isEmpty(indicatorIds)) {
            return new HashMap<>();
        }

        // 根据指标ID反查指标是否挂在导入的维度下
        List<IndicatorDto> indicatorInfos = spsdAclService.getIndicatorInfo(orgId, xpd.getModelId(), indicatorIds);
        if (CollectionUtils.isEmpty(indicatorInfos)) {
            return new HashMap<>();
        }
        ModelInfo modelInfo = spsdAclService.getModelInfo(orgId, xpd.getModelId());
        if (modelInfo == null || CollectionUtils.isEmpty(modelInfo.getDms())) {
            return new HashMap<>();
        }

        List<XpdImportPO> imports = xpdImportMapper.findByXpdIdAndImportType(orgId, xpd.getId(), XpdImportTypeEnum.DIM_INDICATOR.getCode(), false);
        Map<String, XpdImportPO> sdDimIdImportsMap = StreamUtil.list2map(imports, XpdImportPO::getSdDimId);
        if (sdDimIdImportsMap.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, List<ActivityInfoDTO>> ret = new HashMap<>();
        for (IndicatorDto indicatorInfo : indicatorInfos) {
            List<String> upperDimIds = getUpperDimIds(indicatorInfo.getDmId(), modelInfo);
            List<ActivityInfoDTO> activities = new ArrayList<>();
            upperDimIds.forEach(sdDimId -> {
                if (sdDimIdImportsMap.containsKey(sdDimId)) {
                    XpdImportPO xpdImport = sdDimIdImportsMap.get(sdDimId);
                    ActivityInfoDTO activityInfoDto = new ActivityInfoDTO();
                    activityInfoDto.setActvId(xpdImport.getId());
                    activityInfoDto.setActvType(1);
                    activities.add(activityInfoDto);
                }
            });
            if (CollectionUtils.isNotEmpty(activities)) {
                ret.put(indicatorInfo.getItemId(), activities);
            }
        }
        return ret;
    }

    /**
     * 获取指定入参的活动列表
     */
    public List<ActivityInfoDTO> listActivityByReq(String orgId, String xpdId, ActvReqBean reqBean) {
        XpdPO xpd = xpdMapper.selectByIdAndOrg(orgId, xpdId);
        Validate.isNotNull(xpd, ExceptionKeys.XPD_NOT_EXIST);
        String modelId = xpd.getModelId();
        String actvId = xpd.getAomPrjId();
        List<AomActvExtBO> aomActvExt = activityIndicatorList(orgId, actvId);

        String indicatorId = reqBean.getIndicatorId();
        boolean searchPerfActv = reqBean.isSearchPerfActv();
        List<ActivityInfoDTO> activities = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(aomActvExt)) {
            List<String> indicatorIds = new ArrayList<>();
            for (AomActvExtBO aomActvExtBO : aomActvExt) {
                if (!UacdTypeEnum.ACTV_PERF.getRegId().equals(aomActvExtBO.getRefRegId()) && CollectionUtils.isNotEmpty(aomActvExtBO.getIndicators())) {
                    indicatorIds.addAll(aomActvExtBO.getIndicators().stream().map(AomActvIndicatorDto::getSdIndicatorId).toList());
                }
            }
            Map<String, IndicatorDto> indicatorMap = new HashMap<>();
            if (!searchPerfActv && CollectionUtils.isNotEmpty(indicatorIds)) {
                List<IndicatorDto> indicators = spsdAclService.getIndicatorInfo(orgId, modelId, indicatorIds);
                if (CollectionUtils.isNotEmpty(indicators)) {
                    indicatorMap = StreamUtil.list2map(indicators, IndicatorDto::getItemId);
                }
            }

            // 仅查询绩效活动
            if (searchPerfActv) {
                activities.addAll(buildPerfActivities(aomActvExt));
            } else {
                //查询配置
                XpdRuleConfPO ruleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
                if (Objects.nonNull(ruleConf) && Integer.valueOf(1).equals(ruleConf.getResultType())) {
                    //TODO 调用测评获取测评的解释体系
                    //                //获取测评的活动ID
                    //                List<String> evalIds = aomActvExt.stream()
                    //                        .filter(el -> UacdTypeEnum.ACTV_SPEVAL.getRegId().equals(el.getRefRegId()))
                    //                        .map(AomActvExtBO::getActvRefId).collect(Collectors.toList());
                    //                if (CollectionUtils.isNotEmpty(evalIds)) {
                    //                    //获取有解释体系的测评活动
                    //                    List<String> explainEvalIds = new ArrayList<>();
                    //                    //过滤掉不含测评体系的测评活动
                    //                    aomActvExt.removeIf(el -> UacdTypeEnum.ACTV_SPEVAL.getRegId().equals(el.getRefRegId())
                    //                            && !explainEvalIds.contains(el.getActvRefId()));
                    //                }
                }
                for (AomActvExtBO aomActvExtBO : aomActvExt) {
                    activities.addAll(buildAomActivities(indicatorId, aomActvExtBO, indicatorMap));
                }
            }
            //            for (AomActvExtBO aomActvExtBO : aomActvExt) {
            //                // 仅查询绩效活动
            //                if (searchPerfActv) {
            //                    activities.addAll(buildPerfActivities(aomActvExtBO));
            //                } else {
            //                    activities.addAll(buildAomActivities(indicatorId, aomActvExtBO, indicatorMap));
            //                }
            //            }
        }

        if (reqBean.isNeedArchive()) {
            activities.addAll(buildArchiveActivities());
        }

        if (reqBean.isNeedImport()) {
            //            activities.addAll(buildImportActivities(orgId, xpdId));
            activities.addAll(buildImportActivities(orgId, xpd, indicatorId));
        }
        return activities;
    }

    private List<ActivityInfoDTO> buildPerfActivities(List<AomActvExtBO> aomActvExt) {
        List<ActivityInfoDTO> activities = new ArrayList<>();
        for (AomActvExtBO aomActvExtBO : aomActvExt) {
            if (UacdTypeEnum.ACTV_PERF.getRegId().equals(aomActvExtBO.getRefRegId())) {
                ActivityInfoDTO activityInfoDto = new ActivityInfoDTO();
                activityInfoDto.setActvType(DimRuleCalcRefEnum.AOM_ACT.getCode());
                activityInfoDto.setActvId(aomActvExtBO.getActvRefId());
                activityInfoDto.setActvName(aomActvExtBO.getActvRefName());
                activityInfoDto.setCreateTime(aomActvExtBO.getCreateTime());
                if (aomActvExtBO.getPerfExtDto() != null) {
                    activityInfoDto.setEvalType(aomActvExtBO.getPerfExtDto().getEvalType());
                }
                activities.add(activityInfoDto);
            }
        }
        return activities;
    }

    private List<ActivityInfoDTO> buildAomActivities(String indicatorId, AomActvExtBO aomActvExtBO,
            Map<String, IndicatorDto> indicatorMap) {
        List<ActivityInfoDTO> activities = new ArrayList<>();
        // 排除掉绩效活动
        if (UacdTypeEnum.ACTV_PERF.getRegId().equals(aomActvExtBO.getRefRegId())) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(aomActvExtBO.getIndicators())) {
            return new ArrayList<>();
        }
        boolean hasLevelActv = UacdTypeEnum.hasLevelActv(aomActvExtBO.getRefRegId());
        for (AomActvIndicatorDto indicator : aomActvExtBO.getIndicators()) {
            if (indicatorMap.containsKey(indicator.getSdIndicatorId())) {
                IndicatorDto indicatorDto = indicatorMap.get(indicator.getSdIndicatorId());
                boolean levelNotEquals = (indicatorDto.getMaxLevel() > 1) && hasLevelActv
                        && !Objects.equals(indicatorDto.getStandardLevel(), indicator.getStandardLevel());

                if (Objects.equals(indicator.getSdIndicatorId(), indicatorId) && !levelNotEquals) {
                    ActivityInfoDTO activityInfoDto = new ActivityInfoDTO();
                    activityInfoDto.setActvType(DimRuleCalcRefEnum.AOM_ACT.getCode());
                    activityInfoDto.setActvId(aomActvExtBO.getActvRefId());
                    activityInfoDto.setActvName(aomActvExtBO.getActvRefName());
                    activityInfoDto.setIndicatorTotalScore(indicator.getTotalScore());
                    activities.add(activityInfoDto);
                    break;
                }
            }
        }
        return activities;
    }

    private List<ActivityInfoDTO> buildArchiveActivities() {
        ActivityInfoDTO activityInfoDto = new ActivityInfoDTO();
        activityInfoDto.setActvType(DimRuleCalcRefEnum.PRI_PROFILE.getCode());
        activityInfoDto.setActvId(String.valueOf(DimRuleCalcRefEnum.PRI_PROFILE.getCode()));
        activityInfoDto.setActvName(DimRuleCalcRefEnum.PRI_PROFILE.getName());
        return Lists.newArrayList(activityInfoDto);
    }

    private List<ActivityInfoDTO> buildImportActivities(String orgId, XpdPO xpd, String indicatorId) {

        List<ActivityInfoDTO> activities = new ArrayList<>();

        // 由末级维度溯源找父级及祖先节点

        List<String> upperDimIds = getUpperDimIds(orgId, indicatorId, xpd.getModelId());
        if (CollectionUtils.isEmpty(upperDimIds)) {
            return Lists.newArrayList();
        }

        List<XpdImportPO> imports = xpdImportMapper.listBySdDimIdsAndImportType(orgId, xpd.getId(), upperDimIds, XpdImportTypeEnum.DIM_INDICATOR.getCode());
        List<String> sdDimIds = imports.stream().map(XpdImportPO::getSdDimId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sdDimIds)) {
            return Lists.newArrayList();
        }
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, sdDimIds);
        Map<String, DimensionList4Get> dimInfoMap = StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId);
        for (XpdImportPO anImport : imports) {
            ActivityInfoDTO activityInfoDto = new ActivityInfoDTO();
            activityInfoDto.setActvType(DimRuleCalcRefEnum.IMPORT_DATA.getCode());
            activityInfoDto.setActvId(anImport.getId());
            activityInfoDto.setActvName("导入数据-" + (dimInfoMap.containsKey(anImport.getSdDimId()) ? dimInfoMap.get(anImport.getSdDimId()).getDmName() : StringUtils.EMPTY));
            activities.add(activityInfoDto);
        }
        return activities;
    }

    public List<String> getUpperDimIds(String orgId, String indicatorId, String modelId) {

        // 根据指标ID反查指标是否挂在导入的维度下
        List<IndicatorDto> indicatorInfos = spsdAclService.getIndicatorInfo(orgId, modelId, Lists.newArrayList(indicatorId));
        if (CollectionUtils.isEmpty(indicatorInfos)) {
            return Lists.newArrayList();
        }

        String dmId = indicatorInfos.get(0).getDmId();
        ModelInfo modelInfo = spsdAclService.getModelInfo(orgId, modelId);
        return getUpperDimIds(dmId, modelInfo);
    }

    public List<String> getUpperDimIds(String dmId, ModelInfo modelInfo) {
        if (modelInfo == null || CollectionUtils.isEmpty(modelInfo.getDms())) {
            return Lists.newArrayList();
        }

        // 末级维度ID
        List<String> upperDimIds = new ArrayList<>();
        // 上级的维度IDs
        for (ModelRequireBaseInfo root : modelInfo.getDms()) {
            if (isUpperDimThenAdd(dmId, root, upperDimIds)) {
                upperDimIds.add(root.getDmId());
            }
            // 一个维度只可能存在于一个根维度下，找到了父亲节点就不需要再找了
            if (CollectionUtils.isNotEmpty(upperDimIds)) {
                break;
            }
        }

        return upperDimIds;
    }

    private boolean isUpperDimThenAdd(String dmId, ModelRequireBaseInfo dmInfo, List<String> uppers) {

        if (Objects.equals(dmId, dmInfo.getDmId())) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(dmInfo.getChilds())) {
            for (ModelRequireBaseInfo dm : dmInfo.getChilds()) {
                if (isUpperDimThenAdd(dmId, dm, uppers)) {
                    uppers.add(dm.getDmId());
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 获取第一个绩效活动
     */
    public AomActvExtBO getFirstPerfActv(String orgId, XpdPO xpd) {
        List<AomActvExtBO> perfActvs = activityIndicatorList(orgId, xpd.getAomPrjId())
                .stream().filter(r -> UacdTypeEnum.ACTV_PERF.getRegId().equals(r.getRefRegId())).collect(Collectors.toList());

        AomActvExtBO perfActv = null;
        if (CollectionUtils.isNotEmpty(perfActvs)) {
            perfActv = perfActvs.get(0);
        }

        return perfActv;
    }
}
