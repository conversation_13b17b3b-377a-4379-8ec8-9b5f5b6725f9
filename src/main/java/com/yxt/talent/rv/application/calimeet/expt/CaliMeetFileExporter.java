package com.yxt.talent.rv.application.calimeet.expt;

import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExporter;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CaliMeetFileExporter {
    private static final String[] HEADER_KEYS =
            {"fullName", "userName", "deptName", "positionName", "suggestion"};
    private static final String HEADER_PREFIX = "apis.sptalentrv.meeting.user.export.header.";
    private static final int MAX_LEASE_TIME = 100;
    private final CaliMeetAppService caliMeetAppService;
    private final ILock lockService;
    private final DlcComponent dlcComponent;
    private final AuthService authService;
    private final MessageSourceService msgSource;
    private final CaliMeetUserByScoreExportStrategy caliMeetUserByScoreExportStrategy;
    private final CaliMeetUserByLevelExportStrategy caliMeetUserByLevelExportStrategy;
    private final CaliMeetResultExportStrategy caliMeetResultExportStrategy;
    private final PrjDimConfMapper prjDimConfMapper;
    private final XpdDimMapper xpdDimMapper;
    private final I18nComponent i18nComponent;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final SpsdAclService spsdAclService;

    /**
     * 导出校准结果模板
     *
     * @param meetingId
     * @param type
     * @param userCache
     */
    public Map<String, String> exportCaliMeetResultTpl(
            String meetingId, int type, UserCacheDetail userCache) {
        String lockKey = String.format(
                RedisKeys.LK_CALI_MEET_USER_TMPL_EXPT,
                userCache.getOrgId(), meetingId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                return doExportTemplate(meetingId, type, userCache);
            } catch (Exception e) {
                log.error("LOG62540:exportTemplateException", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }

    private Map<String, String> doExportTemplate(
            String meetingId, int type, UserCacheDetail userCache) {
        String path;
        // 查询项目Id
        CaliMeetVO meeting4Get =
                caliMeetAppService.getMeeting4GetById(
                        meetingId, userCache.getOrgId(), userCache.getLocale());
        if (null == meeting4Get) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        String subName = (type == 1 ? "导入校准结果（维度等级）" : "导入校准结果（维度评分）");
        String fileName =
                subName +
                DateTimeUtil.dateToString(new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS);
        OutputStrategy outputStrategy = (type == 1 ? caliMeetUserByLevelExportStrategy :
                                                 caliMeetUserByScoreExportStrategy);

        DynamicExcelExportContent prjResult =
                handleMeetingUserTemplate(meetingId, meeting4Get.getProjectId(), type, userCache);

        long taskId = dlcComponent.prepareExport(
                fileName + FileConstants.FILE_SUFFIX_XLSX,
                outputStrategy);
        path = dlcComponent.upload2Disk(fileName + FileConstants.FILE_SUFFIX_XLSX, prjResult,
                outputStrategy, taskId);

        Map<String, String> result = new HashMap<>(1);
        result.put("filePath", path);
        return result;
    }

    private DynamicExcelExportContent handleMeetingUserTemplate(
            String meetingId, String projectId, int type, UserCacheDetail userCache) {
        // 查询维度
        String orgId = userCache.getOrgId();
        List<PrjDimConfPO> prjDimConfs =
                prjDimConfMapper.listActiveDimConfByPrjId(orgId, projectId);

        List<XpdDimPO> dimList = xpdDimMapper.listByXpdId(orgId, projectId);
        if (type == 2) { // 过滤掉【3-盘点数据导入】
            prjDimConfs = prjDimConfs.stream()
                    .filter(a -> a.getToolType() != 3)
                    .collect(Collectors.toList());
        }

        // 查询校准结果
        PageRequest pageRequest = new PageRequest();
        pageRequest.setCurrent(0);
        pageRequest.setSize(Long.MAX_VALUE);

        CaliMeetUserQuery search = new CaliMeetUserQuery();
        search.setKeyword("");
        search.setDeptIds(new ArrayList<>());

        List<CaliMeetUserVO> datas =
                caliMeetAppService.findPage(pageRequest, meetingId, search, userCache).getDatas();
        if (null == datas) {
            datas = new ArrayList<>();
        }

        // 封装 DynamicHeaderExport
        DynamicExcelExportContent prjResult = new DynamicExcelExportContent();
        prjResult.setHeaders(initHeaders1(prjDimConfs, dimList, orgId));
        prjResult.setSheets(
                FileExporter.buildSingleSheets(FileConstants.SHEET_1, FileConstants.SHEET_1));


        prjResult.setData(initData3(datas, type, userCache.getOrgId(), dimList, projectId));
        return prjResult;
    }

    private Map<String, List<List<String>>> initHeaders1(List<PrjDimConfPO> prjDimConfs, List<XpdDimPO> dimList, String orgId) {
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        List<DimensionList4Get> baseDimList = spsdAclService.getBaseDimDetail(orgId, dimIds);
        Map<String, String> dimNameMap =
            StreamUtil.list2map(baseDimList, DimensionList4Get::getId, DimensionList4Get::getDmName);
        Map<String, List<List<String>>> headerMap = new HashMap<>(8);
        List<List<String>> headers = new ArrayList<>();
        // 固定头
        Locale locale = authService.getLocale();
        Arrays.stream(HEADER_KEYS).forEach(key -> {
            String header = ApiUtil.getL10nString(msgSource, HEADER_PREFIX + key, locale);
            List<String> strings = new ArrayList<>();
            strings.add(header);
            headers.add(strings);
        });
        // 动态头-维度
        // 校准后翻译
        String caliAfter = i18nComponent.getI18nValue("apis.sptalentrv.meeting.user.export.header.after");
        for (String dimId : dimIds) {
            // 校准前维度等级
            String dimName = dimNameMap.getOrDefault(dimId, "");
            List<String> original = new ArrayList<>();
            original.add(dimName);
            headers.add(original);
            // 校准后维度等级
            List<String> calibration = new ArrayList<>();
            calibration.add(caliAfter + dimName);
            headers.add(calibration);
        }


        /*prjDimConfs.forEach(dimen -> {
            // 校准前维度等级
            List<String> original = new ArrayList<>();
            original.add(dimen.getDimensionName());
            headers.add(original);
            // 校准后维度等级
            List<String> calibration = new ArrayList<>();
            calibration.add("校准后" + dimen.getDimensionName());
            headers.add(calibration);
        });*/

        headerMap.put(FileConstants.SHEET_1, headers);
        return headerMap;
    }

    private Map<String, List<Object>> initData3(
            List<CaliMeetUserVO> list, int type, String orgId, List<XpdDimPO> dimList, String projectId) {
        List<XpdResultUserDimPO> userDimList = xpdResultUserDimMapper.findByXpdId(orgId, projectId);
        Map<String, List<XpdResultUserDimPO>> userDimMap =
            userDimList.stream().collect(Collectors.groupingBy(XpdResultUserDimPO::getUserId));

        // 维度名称
        List<XpdGridLevelPO> gridLevelList = xpdGridLevelMapper.listByXpdId(orgId, projectId);
        Map<String, String> gridLevelMap =
            StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getId, XpdGridLevelPO::getLevelName);
        Map<String, List<Object>> result = new HashMap<>(8);
        List<Object> data = new ArrayList<>();

        /*Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgId(orgId);
        Map<Integer, String> performanceGradesMap = perfGrades.stream()
                .collect(Collectors.toMap(PerfGradePO::getGradeValue, PerfGradePO::getGradeName));*/
        //String projectId = prjDimConfs.get(0).getProjectId();
        /*List<PrjDimRuleDTO> prjDimRuleDTOS = prjDimRuleMapper.selectSimpleByPrjId(orgId, projectId);
        Map<String, PrjDimRuleDTO> ruleMap =
                StreamUtil.list2map(prjDimRuleDTOS, PrjDimRuleDTO::getDimensionId);*/

       /* Map<Integer, String> map = new HashMap<>(8);
        map.putAll(performanceGradesMap);*/

        list.forEach(e -> {
            List<String> every = new ArrayList<>();
            every.add(e.getFullname());
            every.add(e.getUserName());
            every.add(e.getDeptName());
            every.add(e.getPositionName());
            every.add(e.getSuggestion());
            List<XpdResultUserDimPO> xpdResultUserDimPOS = userDimMap.get(e.getUserId());
            if (CollectionUtils.isEmpty(xpdResultUserDimPOS)) {
                xpdResultUserDimPOS = new ArrayList<>();
            }
            Map<String, String> resultUserMap = StreamUtil.list2map(xpdResultUserDimPOS, XpdResultUserDimPO::getSdDimId,
                XpdResultUserDimPO::getGridLevelId);
            for (XpdDimPO xpdDimPO : dimList) {
                String gridLevelId = resultUserMap.get(xpdDimPO.getSdDimId());
                if (gridLevelId == null) {
                    every.add("");
                } else {
                    every.add(gridLevelMap.getOrDefault(gridLevelId, ""));
                }
                //  校准后
                every.add("");
            }

            /*prjDimConfs.forEach(dimen -> {
                PrjDimRuleDTO prjDimRuleDTO = ruleMap.get(dimen.getDimensionId());
                if (type == 1) { // 维度前等级
                    every.add(e.getOriginalLevelMap().containsKey(dimen.getDimensionId()) ?
                            e.getOriginalLevelMap().get(dimen.getDimensionId()) : "");
                } else { // 维度前评分
                    String value = e.getOriginalScoreMap().containsKey(dimen.getDimensionId()) ?
                            e.getOriginalScoreMap().get(dimen.getDimensionId()).toString() : "";
                    // 维度是 绩效 需要转换为 ABC形式
                    if (dimen.getToolType() == 1 && StringUtils.isNotEmpty(value)) {
                        if (prjDimRuleDTO != null && prjDimRuleDTO.getClassType() != 0) {
                            value = "";
                        } else {
                            value = map.get(Double.valueOf(value).intValue());
                        }
                    }
                    if ("-1.00".equals(value)) {
                        value = "";
                    }
                    every.add(value);
                }
                every.add(""); // 校准后
            });*/
            data.add(every);
        });

        result.put(FileConstants.SHEET_1, data);
        return result;
    }

    /**
     * 导出校准结果
     *
     * @param meetingId 会议Id
     * @param search
     * @param userCache
     */
    public Map<String, String> exportCaliMeetResult(
            String meetingId, CaliMeetUserQuery search, UserCacheDetail userCache) {
        String lockKey = String.format(
                RedisKeys.LK_CALI_MEET_USER_RESULT_EXPT,
                userCache.getOrgId(), meetingId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                return doExportResult(meetingId, search, userCache);
            } catch (Exception e) {
                log.error("LOG62560:exportResultException", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }

    private Map<String, String> doExportResult(
            String meetingId, CaliMeetUserQuery search, UserCacheDetail userCache) {
        String path;
        // 查询项目Id
        CaliMeetVO meeting4Get =
                caliMeetAppService.getMeeting4GetById(
                        meetingId, userCache.getOrgId(), userCache.getLocale());
        if (null == meeting4Get) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        DynamicExcelExportContent prjResult =
                handleMeetingUserResult(meetingId, search, userCache, meeting4Get.getProjectId());

        String name = meeting4Get.getMeetName() + "##" + FileConstants.FILE_SUFFIX_XLSX;
        long taskId = dlcComponent.prepareExport(name, caliMeetResultExportStrategy);
        path = dlcComponent.upload2Disk(name, prjResult,
                caliMeetResultExportStrategy, taskId);

        Map<String, String> result = new HashMap<>(1);
        result.put("filePath", path);
        return result;
    }

    private DynamicExcelExportContent handleMeetingUserResult(
            String meetingId, CaliMeetUserQuery search, UserCacheDetail userCache,
            String projectId) {
        // 查询维度
        String orgId = userCache.getOrgId();
        List<PrjDimConfPO> prjDimConfs =
                prjDimConfMapper.listActiveDimConfByPrjId(orgId, projectId);

        List<XpdDimPO> dimList = xpdDimMapper.selectByXpdId(orgId, projectId, null);
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        List<DimensionList4Get> baseDimList = spsdAclService.getBaseDimDetail(orgId, dimIds);
        Map<String, String> dimNameMap =
            StreamUtil.list2map(baseDimList, DimensionList4Get::getId, DimensionList4Get::getDmName);

        // 查询校准结果
        PageRequest pageRequest = new PageRequest();
        pageRequest.setCurrent(0);
        pageRequest.setSize(Long.MAX_VALUE);
        List<CaliMeetUserVO> datas =
                caliMeetAppService.findPage(pageRequest, meetingId, search, userCache).getDatas();
        if (null == datas) {
            datas = new ArrayList<>();
        }

        // 封装 DynamicHeaderExport
        DynamicExcelExportContent prjResult = new DynamicExcelExportContent();
        prjResult.setHeaders(initHeaders(dimIds, dimNameMap));
        prjResult.setSheets(FileExporter.buildSingleSheets(FileConstants.SHEET_1, "校准结果"));
        List<XpdGridLevelPO> gridLevelPOS = xpdGridLevelMapper.listByXpdId(orgId, projectId);
        Map<Integer, String> gridLevelMap =
            StreamUtil.list2map(gridLevelPOS, XpdGridLevelPO::getOrderIndex, XpdGridLevelPO::getLevelName);
        prjResult.setData(initData3(datas, dimIds));
        return prjResult;
    }

    private Map<String, List<List<String>>> initHeaders(List<String> dimIds, Map<String, String> dimNameMap) {
        Map<String, List<List<String>>> headerMap = new HashMap<>(8);
        List<List<String>> headers = new ArrayList<>();
        // 固定头
        Locale locale = authService.getLocale();
        Arrays.stream(HEADER_KEYS).forEach(key -> {
            String header = ApiUtil.getL10nString(msgSource, HEADER_PREFIX + key, locale);
            List<String> strings = new ArrayList<>();
            strings.add(header);
            headers.add(strings);
        });

        String caliAfter = i18nComponent.getI18nValue("apis.sptalentrv.meeting.user.export.header.after");
        for (String dimId : dimIds) {
            // 校准前维度等级
            List<String> original = new ArrayList<>();
            original.add(dimNameMap.getOrDefault(dimId, "")); // 空值
            headers.add(original);
            // 校准后维度等级
            List<String> calibration = new ArrayList<>();
            calibration.add(caliAfter + dimNameMap.getOrDefault(dimId, ""));
            headers.add(calibration);

        }
        /*// 动态头-维度
        prjDimConfs.forEach(dimen -> {
            // 校准前维度等级
            List<String> original = new ArrayList<>();
            original.add(dimen.getDimensionName()); // 空值
            headers.add(original);
            // 校准后维度等级
            List<String> calibration = new ArrayList<>();
            calibration.add("校准后" + dimen.getDimensionName());
            headers.add(calibration);
        });*/

        headerMap.put(FileConstants.SHEET_1, headers);
        return headerMap;
    }

    private Map<String, List<Object>> initData3(List<CaliMeetUserVO> list, List<String> dimIds) {
        Map<String, List<Object>> result = new HashMap<>(8);
        List<Object> data = new ArrayList<>();
        list.forEach(e -> {
            List<String> every = new ArrayList<>();
            every.add(e.getFullname());
            every.add(e.getUserName());
            every.add(e.getDeptName());
            every.add(e.getPositionName());
            every.add(e.getSuggestion());

            // 校准前等级 和校准后等级
            for (String dimId : dimIds) {
                every.add(e.getOriginalLevelMap().getOrDefault(dimId, ""));
                every.add(e.getCalibrationLevelMap().getOrDefault(dimId, ""));
            }
            /*prjDimConfs.forEach(dimen -> {
                every.add(e.getOriginalLevelMap().getOrDefault(dimen.getDimensionId(), ""));
                every.add(e.getCalibrationLevelMap().getOrDefault(dimen.getDimensionId(), ""));
            });*/
            data.add(every);
        });
        result.put(FileConstants.SHEET_1, data);
        return result;
    }
}
