package com.yxt.talent.rv.application.calimeet.legacy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.util.*;
import com.yxt.enums.DeleteEnum;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.talent.rv.application.calimeet.CaliMeetMsgSender;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetDimDTO;
import com.yxt.talent.rv.application.common.legacy.AttachmentAppService;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjAppService;
import com.yxt.talent.rv.application.prj.user.legacy.PrjUserResultAppService;
import com.yxt.talent.rv.application.xpd.common.dto.XpdInfoDto;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.controller.client.general.calimeet.query.CaliMeetClientQuery;
import com.yxt.talent.rv.controller.client.general.calimeet.viewobj.CaliMeetClientVO;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetCreateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetPrjUserResultAddCmd;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserBatchAddCmd;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserRemarkUpdateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetQuery;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetResultQuery;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.*;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.CliMeetRemarkVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserResultVO;
import com.yxt.talent.rv.controller.manage.xpd.xpd.query.XpdQuery;
import com.yxt.talent.rv.controller.manage.xpd.xpd.viewobj.XpdVO;
import com.yxt.talent.rv.domain.calimeet.calimeet.CaliMeet;
import com.yxt.talent.rv.domain.calimeet.calimeet.CaliMeetAttendee;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityParticipationMemberPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.auth.AuthorizationService;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.OrginitAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * 人才盘点校准会 Application Service
 */
@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class CaliMeetAppService {
    private static final String DATA_PERMISSION_CODE = "sp_talentrv_calibrationlist_get_extent";
    private static final String NAV_CODE = "sp_gwnl_talentrv_calibration";
    private static final long CACHE_TIME = 2;
    private static final String CACHE_VAL_SPLIT = "#split#";

    private final PrjUserResultAppService prjUserResultAppService;
    private final OrginitAclService orginitAclService;
    private final AttachmentAppService attachmentAppService;
    private final AuthorizationService authorizationService;
    private final RedisRepository talentRedisRepository;
    private final CaliMeetMapper calMeetMapper;
    private final CaliMeetMsgSender caliMeetMsgSender;
    private final CaliMeetAttendeeMapper caliMeetAttendeeMapper;
    private final CaliMeetUserRemarkMapper caliMeetUserRemarkMapper;
    private final CaliMeetUserMapper caliMeetUserMapper;
    private final PrjMapper prjMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final PrjDimConfMapper prjDimConfMapper;
    private final PrjUserMapper prjUserMapper;
    private final CaliMeetUserResultMapper caliMeetUserResultMapper;
    private final CaliMeetMapper caliMeetMapper;
    private final L10nAclService l10nAclService;
    private final UdpAclService udpAclService;

    private final ActivityParticipationService activityParticipationService;
    private final RvActivityParticipationMemberMapper activityParticipationMemberMapper;
    private final XpdMapper xpdMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdDimMapper xpdDimMapper;
    private final XpdGridLevelMapper gridLevelMapper;
    private final SpsdAclService spsdAclService;
    private final XpdUserExtMapper xpdUserExtMapper;
    private final XpdResultUserDimMapper resultUserDimMapper;
    private final XpdResultCalcService resultCalcService;


    /**
     * 批量保存参与人员<br>
     *
     * @param userIds      参会人员
     * @param type         参会人员类型 （1-会议组织者，2-人才委员会）
     * @param calimeetId   会议id
     * @param orgId        机构Id
     * @param createUserId 当前用户Id
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void saveBatchDelOldBy(List<String> userIds, CaliMeetAttendee.UserTypeEnum type, String calimeetId,
            String createUserId, String orgId) {

        // 删除老数据
        caliMeetAttendeeMapper.deleteByOrgIdAndCalimeetIdAndUserType(orgId, calimeetId, type.getCode());

        List<CaliMeetAttendeePO> userList = new ArrayList<>();
        userIds.forEach(a -> {
            CaliMeetAttendeePO temp = new CaliMeetAttendeePO();
            temp.setId(ApiUtil.getUuid());
            temp.setUserId(a);
            temp.setUserType(type.getCode());
            temp.setOrgId(orgId);
            temp.setMeetingId(calimeetId);
            EntityUtil.setAuditFields(temp, createUserId);
            userList.add(temp);
        });

        if (!userList.isEmpty()) {
            caliMeetAttendeeMapper.insertList(userList);
        }
    }

    public List<CaliMeetUserResultPO> findByMeetingIdAndUserIds(String orgId, String meetingId, List<String> userIds) {
        return caliMeetUserResultMapper.listByOrgIdAndMeetIdAndUserIdIn(orgId, meetingId, userIds);
    }

    public PagingList<PrjUserResultVO> findByXYDimensionPage(CaliMeetResultQuery search, String orgId, Integer xValue,
            Integer yValue, PageRequest pageRequest, String lang, UserCacheDetail currentUser) {
        Page<PrjUserResultVO> requestPage = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(orgId),
                ResourceTypeEnum.USER, search.getSearchKey());
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            search.setUserIds(new ArrayList<>(l10nUserIds));
        }

        // 当前查询人，管辖范围内的人
        /*List<String> allPrjUserIds = caliMeetUserMapper.selectByMeetingId4UserIds(orgId, search.getMeetingId());
        List<String> authUserIds = authorizationService.getAuthUserIdsRange(currentUser, NAV_CODE,
            DATA_PERMISSION_CODE, allPrjUserIds);
        if (!"1".equals(currentUser.getAdmin()) && CollectionUtils.isEmpty(authUserIds)) {
            int offset = (int) ((pageRequest.getCurrent() - 1) * pageRequest.getSize());
            return new PagingList<>(new ArrayList<>(), new Paging(pageRequest.getSize(), offset, 0, 0));
        }
        search.setAuthUserIds(authUserIds);*/
        IPage<PrjUserResultVO> page = caliMeetUserResultMapper.paging(orgId, xValue, yValue, search, requestPage);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            l10nAclService.translateList(enableLocalization, List.of(orgId), lang, PrjUserResultVO.class,
                    page.getRecords());
            page.getRecords().forEach(item -> {
                item.setYAixs(search.getAxisY());
                if (item.getYCheckValue() == 0) {
                    item.setYCheckValue(yValue);
                }
                item.setXAxis(search.getAxisX());
                if (item.getXCheckValue() == 0) {
                    item.setXCheckValue(xValue);
                }
                long total = page.getTotal();
                if (Integer.valueOf((int) total) > pageRequest.getSize() * pageRequest.getCurrent()) {
                    item.setPage(1);
                }
            });
        }
        return BeanCopierUtil.toPagingList(page);
    }

    public CaliMeetResultDetailVO getCheckPrjCalcList(CaliMeetResultQuery search, String orgId, PageRequest pageRequest,
            String lang, UserCacheDetail currentUser) {
        CaliMeetResultDetailVO detail = new CaliMeetResultDetailVO();
        List<PrjUserResultVO> result = new ArrayList<>();
        CaliMeetPO meetingDetail = caliMeetMapper.selectByIdAndOrgId(search.getMeetingId(), orgId);
        if (meetingDetail == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        detail.setMeetRouteStatus(meetingDetail.getMeetRouteStatus());
        // 当前查询人，管辖范围内的人
        /*List<String> allPrjUserIds = caliMeetUserMapper.selectByMeetingId4UserIds(orgId, search.getMeetingId());
        List<String> authUserIds = authorizationService.getAuthUserIdsRange(currentUser, NAV_CODE,
            DATA_PERMISSION_CODE, allPrjUserIds);
        if (!"1".equals(currentUser.getAdmin()) && CollectionUtils.isEmpty(authUserIds)) {
            detail.setCalibrationList(result);
            return detail;
        }*/

        Map<String, BigDecimal> map = new HashMap<>(8);
        BigDecimal sumTotalUser = BigDecimal.ZERO;
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        for (int x = 1; x <= 3; x++) {
            for (int y = 1; y <= 3; y++) {
                Page<PrjUserResultVO> requestPage = new Page<>(pageRequest.getCurrent(), 50);

                Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(orgId),
                        ResourceTypeEnum.USER, search.getSearchKey());
                if (CollectionUtils.isNotEmpty(l10nUserIds)) {
                    search.setUserIds(new ArrayList<>(l10nUserIds));
                }
                IPage<PrjUserResultVO> page = caliMeetUserResultMapper.paging(orgId, x, y, search, requestPage);
                List<PrjUserResultVO> list = page.getRecords();
                if (CollectionUtils.isNotEmpty(list)) {
                    l10nAclService.translateList(enableLocalization, List.of(orgId), lang, PrjUserResultVO.class, list);
                    long total = page.getTotal();
                    BigDecimal tu = new BigDecimal(String.valueOf(total));
                    sumTotalUser = sumTotalUser.add(tu);
                    map.put(String.format("%s%s%s", x, "&", y), tu);
                    list.forEach(item -> {
                        item.setXAxis(search.getAxisX());
                        item.setYAixs(search.getAxisY());
                        item.setTotalUser(total);
                        if (Integer.valueOf((int) total) > requestPage.getSize()) {
                            item.setPage(1);
                        }
                    });
                    result.addAll(list);
                }
            }
        }
        // 计算百分比
        BigDecimal finalSumTotalUser = sumTotalUser;
        map.keySet().forEach(key -> {
            BigDecimal divide = map.get(key).divide(finalSumTotalUser, 4, RoundingMode.HALF_UP);
            map.put(key, divide);
        });
        result.forEach(e -> {
            String format = String.format("%s%s%s", e.getXCheckValue(), "&", e.getYCheckValue());
            BigDecimal bigDecimal = map.get(format);
            e.setPercentStr(bigDecimal.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + "%");
        });
        detail.setCalibrationList(result);
        return detail;
    }

    public void checkPrjCalcResult(String userId, String orgId, String projectId, String caliMeetId,
            List<CaliMeetPrjUserResultAddCmd> caliMeetPrjUserResultAddCmds) {
        if (CollectionUtils.isEmpty(caliMeetPrjUserResultAddCmds)) {
            return;
        }
        //PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, projectId);
        XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, projectId);
        if (xpdPO == null) {
            throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
        }
        // 根据维度分组
        CaliMeetPrjUserResultAddCmd currentIndexResult = caliMeetPrjUserResultAddCmds.get(0);
        String xDimensionId = currentIndexResult.getDimensionIdx();
        String yDimensionId = currentIndexResult.getDimensionIdy();
        List<String> dimIds = new ArrayList<>();
        dimIds.add(xDimensionId);
        dimIds.add(yDimensionId);
        List<String> userIds = StreamUtil.mapList(caliMeetPrjUserResultAddCmds, CaliMeetPrjUserResultAddCmd::getUserId);
        Map<String, CaliMeetPrjUserResultAddCmd> userMap = StreamUtil.list2map(caliMeetPrjUserResultAddCmds,
                CaliMeetPrjUserResultAddCmd::getUserId);
        List<CaliMeetUserResultPO> userResult = caliMeetUserResultMapper.listByMeetIdAndPrjIdAndDimIdInAndUserIdIn(
                orgId, caliMeetId, projectId, dimIds, userIds);
        List<CaliMeetUserResultPO> updatePrj = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userResult)) {
            userResult.forEach(userCal -> {
                CaliMeetPrjUserResultAddCmd prjUsercalcResult = userMap.get(userCal.getUserId());
                if (userCal.getDimensionId().equals(prjUsercalcResult.getDimensionIdx())) {
                    userCal.setCalibrationLevel(prjUsercalcResult.getLevelx());
                }
                if (userCal.getDimensionId().equals(prjUsercalcResult.getDimensionIdy())) {
                    userCal.setCalibrationLevel(prjUsercalcResult.getLevely());
                }
                userCal.setInitType(1);
                EntityUtil.setUpdate(userCal, userId);
                updatePrj.add(userCal);
            });
        }
        if (CollectionUtils.isNotEmpty(updatePrj)) {
            for (CaliMeetUserResultPO caliMeetUserResultPo : updatePrj) {
                caliMeetUserResultMapper.updateById(caliMeetUserResultPo);
            }
        }
    }

    public CaliMeetUserDimResultVO getCalibrationMeetingResultUserDetail(String userId, String meetingId, String orgId,
            String projectId, String lang) {
        /*PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, projectId);
        if (prj == null) {
            throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
        }*/
        CaliMeetPO meeting = caliMeetMapper.selectByIdAndOrgId(meetingId, orgId);
        Validate.isNotNull(meeting, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        CaliMeetUserDimResultVO result = new CaliMeetUserDimResultVO();
        UdpLiteUserPO udpUser = udpLiteUserMapper.selectById(userId);
        if (udpUser == null) {
            throw new ApiException(ExceptionKeys.USER_NOT_EXISTED);
        }
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, UdpLiteUserPO.class, List.of(udpUser));
        result.setUserId(userId);
        result.setDeptName(udpUser.getDeptName());
        result.setImgUrl(udpUser.getImgUrl());
        result.setPosition(udpUser.getPositionName());
        // 获取项目下的维度
        /*List<PrjDimConfPO> list =
                prjDimConfMapper.selectByPrjIdAndOrgIdAndIfStatus(projectId, orgId,
                        EnableEnum.ENABLED.getCode());*/

        List<XpdDimPO> list = xpdDimMapper.listByXpdId(orgId, projectId);
        List<CaliMeetDimDTO> dimList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> dimIds = list.stream().map(XpdDimPO::getSdDimId).toList();
            List<DimensionList4Get> baseDimDetailList = spsdAclService.getBaseDimDetail(orgId, dimIds);
            Map<String, String> dimensionNameMap = StreamUtil.list2map(baseDimDetailList, DimensionList4Get::getId,
                    DimensionList4Get::getDmName);

            Map<String, Integer> dimensionTypeMap = StreamUtil.list2map(list, XpdDimPO::getSdDimId,
                    XpdDimPO::getDimType);
            // 查询项目下的结果
            List<CaliMeetUserResultPO> userMeetingList = caliMeetUserResultMapper.listByOrgIdAndMeetIdAndUserIdAndDimIdIn(
                    orgId, meetingId, userId, dimIds);
            // 维度等级
            List<XpdGridLevelPO> gridLevelPOS = gridLevelMapper.listByXpdId(orgId, projectId);
            Map<Integer, String> gridLevelMap = StreamUtil.list2map(gridLevelPOS, XpdGridLevelPO::getOrderIndex,
                    XpdGridLevelPO::getLevelName);
            if (CollectionUtils.isNotEmpty(userMeetingList)) {
                Map<String, CaliMeetUserResultPO> dimMap = StreamUtil.list2map(userMeetingList,
                        CaliMeetUserResultPO::getDimensionId);
                list.forEach(config -> {
                    CaliMeetDimDTO dim = new CaliMeetDimDTO();
                    CaliMeetUserResultPO calMeetUserResult = dimMap.get(config.getSdDimId());
                    if (calMeetUserResult != null) {
                        dim.setDimensionName(dimensionNameMap.get(config.getSdDimId()));
                        dim.setInitLevel(calMeetUserResult.getOriginalLevel());
                        dim.setInitLevelName(gridLevelMap.get(calMeetUserResult.getOriginalLevel()));
                        dim.setLastLevel(calMeetUserResult.getCalibrationLevel());
                        dim.setLastLevelName(gridLevelMap.get(calMeetUserResult.getCalibrationLevel()));
                        dim.setDimensionType(dimensionTypeMap.get(config.getSdDimId()));
                        dimList.add(dim);
                    }
                });
            }
        }

        setRemarkAndSuggestion(result, orgId, projectId, meetingId, userId);

        if (CollectionUtils.isNotEmpty(dimList)) {
            List<CaliMeetDimDTO> collect = dimList.stream()
                    .sorted(Comparator.comparing(CaliMeetDimDTO::getDimensionType)).collect(Collectors.toList());
            result.setResult(collect);
            return result;
        }
        result.setResult(dimList);
        return result;
    }

    private void setRemarkAndSuggestion(CaliMeetUserDimResultVO result, String orgId, String projectId,
            String meetingId, String userId) {
        List<CaliMeetUserPO> calMeetUsers = caliMeetUserMapper.list(orgId, meetingId,
                Collections.singletonList(userId));
        // 设置工作场备注
        List<CaliMeetUserRemarkPO> calMeetUserRemarks = caliMeetUserRemarkMapper.listByPrjIdAndMeetingIdAndUserId(orgId,
                projectId, null, userId);
        List<CliMeetRemarkVO> cliMeetRemarkVos = new ArrayList<>();
        String currRemark = null;
        for (CaliMeetUserRemarkPO remark : calMeetUserRemarks) {
            CliMeetRemarkVO cliMeetRemarkVO = new CliMeetRemarkVO();
            cliMeetRemarkVO.setProjectId(remark.getProjectId());
            cliMeetRemarkVO.setMeetingId(remark.getMeetingId());
            cliMeetRemarkVO.setMeetingName(remark.getMeetingName());
            cliMeetRemarkVO.setRemark(remark.getRemark());
            cliMeetRemarkVO.setCreateTime(remark.getCreateTime());
            if (Objects.equals(cliMeetRemarkVO.getMeetingId(), meetingId)) {
                currRemark = cliMeetRemarkVO.getRemark();
            } else {
                cliMeetRemarkVos.add(cliMeetRemarkVO);
            }
        }
        result.setCurrRemark(currRemark);
        result.setRemarks(cliMeetRemarkVos);
        result.setSuggestion(
                CollectionUtils.isEmpty(calMeetUsers) ? null : calMeetUsers.iterator().next().getSuggestion());
    }

    public void updateRemarkAndSuggestion(String orgId, String projectId, String meetingId,
            CaliMeetUserRemarkUpdateCmd update, String operator) {
        PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, projectId);
        if (prj == null) {
            throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
        }
        CaliMeetPO meeting = caliMeetMapper.selectByIdAndOrgId(meetingId, orgId);
        Validate.isNotNull(meeting, ExceptionKeys.CALI_MEET_NOT_EXISTED);

        if (StringUtils.isBlank(update.getRemark()) && StringUtils.isBlank(update.getSuggestion())) {
            log.warn("LOG50150:orgId={}, projectId={}, meetingId={}, operator={}", orgId, projectId, meetingId,
                    operator);
            return;
        }
        // 保存remark
        boolean isNewItem = false;
        CaliMeetUserRemarkPO calMeetUserRemark;
        List<CaliMeetUserRemarkPO> existRemarks = caliMeetUserRemarkMapper.listByPrjIdAndMeetingIdAndUserId(orgId,
                projectId, meetingId, update.getUserId());
        if (CollectionUtils.isNotEmpty(existRemarks)) {
            calMeetUserRemark = existRemarks.iterator().next();
        } else {
            calMeetUserRemark = new CaliMeetUserRemarkPO();
            calMeetUserRemark.setId(ApiUtil.getUuid());
            EntityUtil.setAuditFields(calMeetUserRemark, operator);
            isNewItem = true;
        }
        calMeetUserRemark.setOrgId(orgId);
        calMeetUserRemark.setProjectId(projectId);
        calMeetUserRemark.setMeetingId(meetingId);
        calMeetUserRemark.setUserId(update.getUserId());
        calMeetUserRemark.setRemark(update.getRemark());
        calMeetUserRemark.setUpdateUserId(operator);
        calMeetUserRemark.setUpdateTime(new Date());
        log.debug("LOG50120:{}", BeanHelper.bean2Json(calMeetUserRemark, ALWAYS));
        if (isNewItem) {
            if (StringUtils.isBlank(calMeetUserRemark.getRemark())) {
                calMeetUserRemark.setRemark("");
            }
            caliMeetUserRemarkMapper.insertOrUpdate(calMeetUserRemark);
        } else {
            caliMeetUserRemarkMapper.updateById(calMeetUserRemark);
        }
        // 保存建议
        List<CaliMeetUserPO> calMeetUsers = caliMeetUserMapper.list(orgId, meetingId,
                Collections.singletonList(update.getUserId()));
        Validate.isNotEmpty(calMeetUsers, ExceptionKeys.CALI_MEET_USER_NOT_EXISTED);
        CaliMeetUserPO calMeetUser = calMeetUsers.iterator().next();
        calMeetUser.setSuggestion(update.getSuggestion());
        caliMeetUserMapper.updateById(calMeetUser);
    }

    /**
     * 校准会列表
     */
    public PagingList<CaliMeetListVO> findPage(PageRequest pageRequest, UserCacheDetail currentUser,
            CaliMeetQuery search, boolean dataPermisson) {

        String orgId = currentUser.getOrgId();
        if (dataPermisson) {
            if (search.getDataRange() == null || search.getDataRange() == 1) {
                search.setUserIds(Collections.singletonList(currentUser.getUserId()));
            } else {
                //查询校准会所有项目的创建人ID。处理权限返回人员过多大对象
                List<String> allPrjUserIds = caliMeetMapper.getAllPrjCreators(orgId);
                // 获取组织人范围
                List<String> orgUserIds = caliMeetAttendeeMapper.listAllUserIds(orgId, 1);
                if (CollectionUtils.isNotEmpty(orgUserIds)) {
                    allPrjUserIds.addAll(orgUserIds);
                }
                if (CollectionUtils.isEmpty(allPrjUserIds)) {
                    int offset = (int) ((pageRequest.getCurrent() - 1) * pageRequest.getSize());
                    return new PagingList<>(new ArrayList<>(), new Paging(pageRequest.getSize(), offset, 0, 0));
                }


                List<String> authUserIds = authorizationService.getAuthUserIdsRange(currentUser, NAV_CODE,
                    DATA_PERMISSION_CODE, allPrjUserIds);
                // 包含自己
                allPrjUserIds.add(currentUser.getUserId());

                if (CollectionUtils.isEmpty(authUserIds) && !"1".equalsIgnoreCase(currentUser.getAdmin())) {
                    return new PagingList<>();
                }
                search.setUserIds(authUserIds);
                // 把管辖范围人员当成组织人，查询校准会id
                if (CollectionUtils.isNotEmpty(search.getUserIds())) {
                    List<String> meetingIds = caliMeetAttendeeMapper.listByOrgIdAndUserId4Org(orgId, search.getUserIds());
                    if (CollectionUtils.isNotEmpty(meetingIds)) {
                        search.setMeetingIds(meetingIds);
                    }
                }

            }
        }


        Page<CaliMeetPO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<CaliMeetPO> page = caliMeetMapper.paging(pageable, search, orgId,
                com.yxt.common.util.DateUtil.currentTime());

        PagingList<CaliMeetListVO> result = BeanCopierUtil.toPagingList(page, CaliMeetPO.class, CaliMeetListVO.class);

        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            result.getDatas().forEach(cm -> {
                // 设置所属盘点项目名称
                //                PrjPO prj = prjAppService.getProjectById(cm.getProjectId(), orgId);
                //                if (prj == null) {
                //                    throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
                //                }
                //                cm.setProjectName(prj.getProjectName());

                boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);

                // 设置主持人
                List<CaliMeetUserSimpleVO> organizerList = caliMeetAttendeeMapper.listByMeetingIdAndUserType(orgId,
                        cm.getId(), CaliMeetAttendee.UserTypeEnum.ORGANIZER.getCode());
                List<CaliMeetUserSimpleVO> translateList = new ArrayList<>(organizerList);

                // 设置参会人员
                List<CaliMeetUserSimpleVO> talentCommitteeList = caliMeetAttendeeMapper.listByMeetingIdAndUserType(
                        orgId, cm.getId(), CaliMeetAttendee.UserTypeEnum.TALENT_COMMITTEE.getCode());
                translateList.addAll(talentCommitteeList);

                l10nAclService.translateList(enableLocalization, List.of(orgId), currentUser.getLocale(),
                        CaliMeetUserSimpleVO.class, translateList);

                cm.setOrganizerList(organizerList);
                cm.setTalentCommitteeList(talentCommitteeList);
                // 处理会议状态  废弃
                //cm.setMeetStatus(calcMeetingStatus(cm.getMeetStatus(), cm.getMeetTime()));

                // 处理创建人
                UdpLiteUserPO udpUser = udpLiteUserMapper.selectByUserId(orgId, cm.getCreateUserId());
                if (udpUser != null) {
                    cm.setCreateUserName(udpUser.getFullname());
                }

                //校准人数
                cm.setUserCount(caliMeetUserMapper.countByMeetingId(orgId, cm.getId()));
            });
        }

        return result;
    }

    /**
     * 客户端校准会列表【校准会委员专用】
     *
     * @param pageRequest
     * @param search
     * @param orgId
     * @param userId
     */
    public PagingList<CaliMeetClientVO> findClientPage(PageRequest pageRequest, CaliMeetClientQuery search,
            String orgId, String userId) {

        Page<CaliMeetClientVO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        IPage<CaliMeetClientVO> page = caliMeetMapper.pagingVO(pageable, search, orgId, userId,
                com.yxt.common.util.DateUtil.currentTime());

        PagingList<CaliMeetClientVO> result = BeanCopierUtil.toPagingList(page);

        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            result.getDatas().forEach(cm -> {
                // 设置所属盘点项目名称
                XpdQuery criteria = new XpdQuery();
                criteria.setXpdId(cm.getProjectId());
                List<XpdVO> xpdList = xpdMapper.search(orgId, criteria);
                if (CollectionUtils.isEmpty(xpdList)) {
                    throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
                }

                cm.setProjectName(xpdList.get(0).getXpdName());
//                // 处理会议状态
//                cm.setMeetStatus(calcMeetingStatus(cm.getMeetStatus(), cm.getMeetTime()));
            });
        }

        return result;
    }

    public CaliMeetVO getMeeting4GetById(String id, String orgId, String lang) {
        CaliMeetVO meeting4Get = new CaliMeetVO();

        CaliMeetPO meeting = caliMeetMapper.selectByIdAndOrgId(id, orgId);
        if (meeting != null) {
            BeanHelper.copyProperties(meeting, meeting4Get);
            // 设置所属盘点项目名称
            XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, meeting.getProjectId());
            /*PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, meeting.getProjectId());
            if (prj == null) {
                throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
            }*/
            meeting4Get.setProjectId(xpdPO.getId());
            XpdInfoDto xpdMsg = xpdMapper.findXpdMsg(orgId, xpdPO.getId());
            if (xpdMsg != null) {
                meeting4Get.setProjectName(xpdMsg.getXpdName());
            }


            //meeting4Get.setProjectName(prj.getProjectName());
            meeting4Get.setMeetTime(com.yxt.common.util.DateUtil.formatDate(meeting.getMeetTime()));
            boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
            // 设置主持人
            List<CaliMeetUserSimpleVO> organizerList = caliMeetAttendeeMapper.listByMeetingIdAndUserType(orgId,
                    meeting.getId(), CaliMeetAttendee.UserTypeEnum.ORGANIZER.getCode());
            List<CaliMeetUserSimpleVO> translateList = new ArrayList<>(organizerList);

            // 设置参会人员
            List<CaliMeetUserSimpleVO> talentCommitteeList = caliMeetAttendeeMapper.listByMeetingIdAndUserType(orgId,
                    meeting.getId(), CaliMeetAttendee.UserTypeEnum.TALENT_COMMITTEE.getCode());
            translateList.addAll(talentCommitteeList);
            l10nAclService.translateList(enableLocalization, List.of(orgId), lang, CaliMeetUserSimpleVO.class,
                    translateList);

            meeting4Get.setOrganizerList(organizerList);
            meeting4Get.setTalentCommitteeList(talentCommitteeList);
            //废弃6.1
            //            // 处理会议状态
            //            meeting4Get.setMeetStatus(
            //                    calcMeetingStatus(meeting.getMeetStatus(), meeting.getMeetTime()));

            // 设置附件
            meeting4Get.setAppendixList(attachmentAppService.findByAppSourceId(meeting.getId(), orgId));
        }

        return meeting4Get;
    }

    /**
     * 保存或更新校准会基本信息
     *
     * @param calimeetCmd
     * @param operator
     * @param orgId
     */
    public CaliMeetCreateResultVO saveOrUpdateBasic(CaliMeetCreateCmd calimeetCmd, String operator, String orgId) {
        CaliMeetPO cmEntity = new CaliMeetPO();
        if (StringUtils.isBlank(calimeetCmd.getId())) {
            cmEntity = new CaliMeetPO();
            BeanHelper.copyProperties(calimeetCmd, cmEntity);
        } else {
            cmEntity = caliMeetMapper.selectByIdAndOrgId(calimeetCmd.getId(), orgId);
            if (cmEntity == null) {
                throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
            }
            BeanHelper.copyProperties(calimeetCmd, cmEntity);
        }


        boolean isNew = false;
        if (StringUtils.isBlank(cmEntity.getId())) {
            cmEntity.setId(ApiUtil.getUuid());
            cmEntity.setOrgId(orgId);
            EntityUtil.setAuditFields(cmEntity, operator);
            isNew = true;
        } else {
            EntityUtil.setUpdate(cmEntity, operator);
        }

        if (isNew) {
            if (StringUtils.isBlank(cmEntity.getMeetMinutes())) {
                cmEntity.setMeetMinutes("");
            }
            if (cmEntity.getDuration() == null) {
                cmEntity.setDuration(0);
            }
            if (cmEntity.getMeetRouteStatus() == null) {
                cmEntity.setMeetRouteStatus(1);
            }
            if (cmEntity.getMeetStatus() == null) {
                cmEntity.setMeetStatus(0);
            }
            caliMeetMapper.insertOrUpdate(cmEntity);
        } else {
            caliMeetMapper.updateById(cmEntity);
        }

        CaliMeetCreateResultVO result = new CaliMeetCreateResultVO();
        result.setId(cmEntity.getId());
        result.setProjectId(cmEntity.getProjectId());

        return result;
    }

    public void deleteMeeting(String id, String userId, String orgId) {
        int count = caliMeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        CaliMeetPO cmEntity = caliMeetMapper.selectByIdAndOrgId(id, orgId);
        if (cmEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
//        // 处于进行中的校准会不允许删除
//        int meetStatus = calcMeetingStatus(cmEntity.getMeetStatus(), cmEntity.getMeetTime());
//        if (CaliMeet.CaliMeetStatusEnum.UNDERWAY.getCode() == meetStatus) {
//            throw new ApiException(ExceptionKeys.CALI_MEET_DEL_REJECT);
//        }

        cmEntity.setDeleted(DeleteEnum.DELETED.getCode());
        EntityUtil.setUpdate(cmEntity, userId);
        caliMeetMapper.updateById(cmEntity);
    }

    /**
     * 未结束的校准会数量
     *
     * @param orgId
     * @param projectId 项目id
     */
    public CaliMeetQtyVO checkResultBy(String orgId, String projectId) {
        int underwayCount = caliMeetMapper.countByOrgIdAndProjectIdAndMeetStatus(orgId, projectId);
        int rfCount = caliMeetMapper.countByOrgIdAndProjectId(orgId, projectId);
        CaliMeetQtyVO result = new CaliMeetQtyVO();
        result.setRfCount(rfCount);
        result.setUnderwayCount(underwayCount);
        return result;
    }

    /**
     * 关闭校准会，并同步数据至盘点项目
     *
     * @param id
     * @param userId
     * @param orgId
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void closeMeeting(String id, String userId, String orgId) {
        // 验证
        int count = caliMeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CaliMeetPO cmEntity = caliMeetMapper.selectByIdAndOrgId(id, orgId);
        if (cmEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        cmEntity.setMeetStatus(CaliMeet.CaliMeetStatusEnum.FINISHED.getCode());
        EntityUtil.setUpdate(cmEntity, userId);
        caliMeetMapper.updateById(cmEntity);

        // 数据同步
        syncCalibrationData(cmEntity, userId);
    }

    /**
     * 将校准后的数据同步至盘点项目
     *
     * @param cmEntity
     */
    private void syncCalibrationData(CaliMeetPO cmEntity, String currentUserId) {

        List<String> userIds = new ArrayList<>();
        // 参与校准的用户
        List<CaliMeetUserPO> calMeetUserList = caliMeetUserMapper.listByOrgIdAndMeetingId(cmEntity.getOrgId(),
                cmEntity.getId());

        if (calMeetUserList.isEmpty()) {
            return;
        }
        String orgId = cmEntity.getOrgId();
        String xpdId = cmEntity.getProjectId();
        calMeetUserList.forEach(a -> userIds.add(a.getUserId()));
        // 用户各维度校准结果
        List<CaliMeetUserResultPO> resultList = findByMeetingIdAndUserIds(cmEntity.getOrgId(), cmEntity.getId(),
                userIds);
        Map<String, List<CaliMeetUserResultPO>> userResultMap = resultList.stream()
                .collect(Collectors.groupingBy(CaliMeetUserResultPO::getUserId));

        // 查询项目用户数据
        List<PrjUserPO> prjUser = prjUserMapper.selectByOrgIdAndPrjIdAndUserIds(cmEntity.getOrgId(),
                cmEntity.getProjectId(), userIds);
        List<XpdUserExtPO> xpdUserExtList = xpdUserExtMapper.selectByXpdIdAndUserIds(cmEntity.getOrgId(),
                cmEntity.getProjectId(), userIds);
        Map<String, XpdUserExtPO> userExtMap = StreamUtil.list2map(xpdUserExtList, XpdUserExtPO::getUserId);

        List<XpdResultUserDimPO> resultUserDimList = resultUserDimMapper.findByXpdIdAndUserIds(cmEntity.getOrgId(),
                cmEntity.getProjectId(), userIds);
        Map<String, List<XpdResultUserDimPO>> userDimMap = resultUserDimList.stream()
                .collect(Collectors.groupingBy(XpdResultUserDimPO::getUserId));


        // 查询项目结果数据
        List<PrjUserResultPO> prjResult = prjUserResultAppService.findByProjectIdAndUserIds(cmEntity.getOrgId(),
                cmEntity.getProjectId(), userIds);

        // 盘点维度结果数据

        Map<String, List<PrjUserResultPO>> prjResultMap = prjResult.stream()
                .collect(Collectors.groupingBy(PrjUserResultPO::getUserId));

        XpdPO xpdPO = xpdMapper.selectById(cmEntity.getProjectId());

        List<RvActivityParticipationMemberPO> xpdUsers = activityParticipationMemberMapper.listByActvId(
                cmEntity.getOrgId(), xpdPO.getAomPrjId(), userIds);
        Map<String, Long> memberUserMap = StreamUtil.list2map(xpdUsers, RvActivityParticipationMemberPO::getUserId,
                RvActivityParticipationMemberPO::getId);

        List<XpdGridLevelPO> gridLevelPOS = gridLevelMapper.listByXpdId(orgId, xpdId);
        Map<Integer, String> gridLevelMap = StreamUtil.list2map(gridLevelPOS, XpdGridLevelPO::getOrderIndex,
                XpdGridLevelPO::getId);

        // 待更新列表
        List<PrjUserPO> prePrjUsers = new ArrayList<>();
        List<XpdUserExtPO> preAddUsers = new ArrayList<>();
        List<XpdUserExtPO> updateUsers = new ArrayList<>();
        List<XpdResultUserDimPO> addUserDimList = new ArrayList<>();
        List<XpdResultUserDimPO> updateUserDimList = new ArrayList<>();
        List<PrjUserResultPO> prePrjResults = new ArrayList<>();
        // 准备待更新数据
        calMeetUserList.forEach(mu -> {
            XpdUserExtPO xpdUserExtPO = userExtMap.get(mu.getUserId());
            if (xpdUserExtPO == null) {
                XpdUserExtPO addUserExtPO = new XpdUserExtPO();
                addUserExtPO.setId(ApiUtil.getUuid());
                addUserExtPO.setOrgId(mu.getOrgId());
                addUserExtPO.setXpdId(mu.getProjectId());
                addUserExtPO.setUserId(mu.getUserId());
                addUserExtPO.setSuggestion(mu.getSuggestion());
                addUserExtPO.setActvMemberId(memberUserMap.get(mu.getUserId()));
                addUserExtPO.setDeleted(0);
                EntityUtil.setAuditFields(addUserExtPO, currentUserId);
                preAddUsers.add(addUserExtPO);
            } else {
                xpdUserExtPO.setSuggestion(mu.getSuggestion());
                EntityUtil.setUpdate(xpdUserExtPO, currentUserId);
                updateUsers.add(xpdUserExtPO);

            }


            // 设置校准建议
            /*prjUser.forEach(pu -> {
                if (mu.getUserId().equals(pu.getUserId())) {
                    pu.setSuggestion(mu.getSuggestion());
                    EntityUtil.setUpdate(pu, currentUserId);
                    prePrjUsers.add(pu);
                }
            });*/
            List<XpdResultUserDimPO> xpdResultUserDimList = userDimMap.get(mu.getUserId());
            if (CollectionUtils.isEmpty(xpdResultUserDimList)) {
                xpdResultUserDimList = Lists.newArrayList();
            }
            Map<String, XpdResultUserDimPO> resultUserDimMap = StreamUtil.list2map(xpdResultUserDimList,
                    XpdResultUserDimPO::getSdDimId);
            List<CaliMeetUserResultPO> caliMeetUserResultList = userResultMap.get(mu.getUserId());
            if (CollectionUtils.isEmpty(caliMeetUserResultList)) {
                return;
            }
            caliMeetUserResultList.forEach(x -> {
                if (x.getCalibrationLevel() == null || x.getCalibrationLevel() < 1) {
                    return;
                }
                String dimensionId = x.getDimensionId();
                XpdResultUserDimPO xpdResultUserDimPO = resultUserDimMap.get(dimensionId);
                if (xpdResultUserDimPO == null) {
                    XpdResultUserDimPO newResultUserDim = new XpdResultUserDimPO();
                    newResultUserDim.setId(ApiUtil.getUuid());
                    newResultUserDim.setOrgId(orgId);
                    newResultUserDim.setXpdId(xpdId);
                    newResultUserDim.setUserId(x.getUserId());
                    newResultUserDim.setSdDimId(dimensionId);
                    newResultUserDim.setGridLevelId(gridLevelMap.get(x.getCalibrationLevel()));
                    newResultUserDim.setScoreValue(BigDecimal.ZERO);
                    newResultUserDim.setQualifiedPtg(BigDecimal.ONE);
                    newResultUserDim.setDeleted(0);
                    newResultUserDim.setCalcBatchNo(0);
                    EntityUtil.setAuditFields(newResultUserDim, currentUserId);
                    addUserDimList.add(newResultUserDim);
                } else {
                    xpdResultUserDimPO.setGridLevelId(gridLevelMap.get(x.getCalibrationLevel()));
                    EntityUtil.setUpdate(xpdResultUserDimPO, currentUserId);
                    updateUserDimList.add(xpdResultUserDimPO);
                }
            });

            /*List<PrjUserResultPO> someUserCalcResults = prjResultMap.get(mu.getUserId());
            userResultMap.get(mu.getUserId()).forEach(ur -> {
                if (someUserCalcResults != null) { // 正常情况，校准前项目已有用户各维度计算结果
                    Map<String, List<PrjUserResultPO>> someUserCalcResultMap =
                            someUserCalcResults.stream()
                                    .collect(
                                            Collectors.groupingBy(PrjUserResultPO::getDimensionId));
                    if (someUserCalcResultMap.containsKey(ur.getDimensionId())) {
                        PrjUserResultPO sur = someUserCalcResultMap.get(ur.getDimensionId()).get(0);
                        Integer cl = ur.getCalibrationLevel() == 0 ? ur.getOriginalLevel() :
                                ur.getCalibrationLevel();
                        BigDecimal defaultValue = new BigDecimal("-1.00");
                        BigDecimal cs = ur.getCalibrationScore().compareTo(defaultValue) == 0 ?
                                ur.getOriginalScore() :
                                ur.getCalibrationScore();

                        sur.setLastLevel(cl);
                        sur.setLastScore(cs);
                        EntityUtil.setUpdate(sur, currentUserId);
                        prePrjResults.add(sur);
                    } else {
                        prePrjResults.add(generateCalcResult(ur, currentUserId));
                    }
                } else {
                    // 异常情况 ， 校准前项目只加了用户，用户没有计算结果。
                    prePrjResults.add(generateCalcResult(ur, currentUserId));
                }
            });*/
        });

        // 批量更新同步
        if (CollectionUtils.isNotEmpty(preAddUsers)) {
            xpdUserExtMapper.insertBatch(preAddUsers);
        }
        if (CollectionUtils.isNotEmpty(updateUsers)) {
            xpdUserExtMapper.batchUpdateRvXpdUserExt(updateUsers);
        }
        if (CollectionUtils.isNotEmpty(addUserDimList)) {
            resultUserDimMapper.batchInsert(addUserDimList);
        }

        if (CollectionUtils.isNotEmpty(updateUserDimList)) {
            resultUserDimMapper.batchUpdateResult(updateUserDimList);
        }
        //prjUserMapper.insertOrUpdateBatch(prePrjUsers);
        //prjUserResultMapper.insertOrUpdateBatch(prePrjResults);
        resultCalcService.calcXpdResultOnlyByDim(orgId, xpdId);
    }

    private PrjUserResultPO generateCalcResult(CaliMeetUserResultPO ur, String currentUserId) {
        PrjUserResultPO tempCalcResult = new PrjUserResultPO();
        tempCalcResult.setId(ApiUtil.getUuid());
        tempCalcResult.setOrgId(ur.getOrgId());
        tempCalcResult.setDimensionId(ur.getDimensionId());
        tempCalcResult.setProjectId(ur.getProjectId());
        tempCalcResult.setUserId(ur.getUserId());
        tempCalcResult.setLastLevel(ur.getCalibrationLevel());
        tempCalcResult.setLastScore(ur.getCalibrationScore());
        EntityUtil.setAuditFields(tempCalcResult, currentUserId);
        return tempCalcResult;
    }

    /**
     * 设置校准会路径显示
     *
     * @param meetingId
     * @param orgId
     * @param userId
     * @param routeStatus
     */
    public void routeMeeting(String meetingId, String orgId, String userId, Integer routeStatus) {
        CaliMeetPO caliMeet = caliMeetMapper.selectByIdAndOrgId(meetingId, orgId);
        if (caliMeet == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        caliMeet.setMeetRouteStatus(routeStatus);
        EntityUtil.setUpdate(caliMeet, userId);
        caliMeetMapper.updateById(caliMeet);
    }

    /**
     * 计算会议状态是否是进行中
     *
     * @param meetStatus
     * @param meetTime
     * @return 状态值
     */
    public int calcMeetingStatus(int meetStatus, Date meetTime) {
        if (meetStatus == CaliMeet.CaliMeetStatusEnum.FINISHED.getCode()) {
            // 已结束
            return meetStatus;
        }
        if (meetTime != null && meetTime.getTime() < com.yxt.common.util.DateUtil.currentTime().getTime()) {
            // 进行中
            return CaliMeet.CaliMeetStatusEnum.UNDERWAY.getCode();
        } else {
            // 未开始
            return CaliMeet.CaliMeetStatusEnum.DEFAULT.getCode();
        }
    }

    /**
     * 1. 编辑保存校准会， 比对时间，如果是会议开始一小时内的时间， 且缓存中不存在【或存在，但实际不一致】，则加入缓存中等待处理； 2. xxljob， 每小时扫描 符合
     * 这一小时的校准会，加入到缓存中待处理。【即使过了开始时间，比如会议开始时间是00，01分扫描的也要加】 3.xxl job 每分钟扫描一次 缓存， 将符合条件的数据
     * 更改状态为进行中，并发送校准会开始消息。
     */
    public int countByOrgIdAndProjectIdAndMeetNameAndIdNot(String orgId, String projectId, String meetingName,
            String id) {
        return caliMeetMapper.countByOrgIdAndProjectIdAndMeetNameAndIdNot(orgId, projectId, meetingName, id);
    }


    /**
     * 每分钟执行1次，扫描缓存中的数据，修改状态为进行中，并发送校准会开始消息
     */
    public void changeMeetingStatus() {

        // 从缓存中查询时间为当前分钟的会议
        // 取该当前时间之前30分钟的key所有的key进行查找，以免遗漏（对于卡点情况，时间刚好错过定时任务的情况）
        List<String> keys = new ArrayList<>();
        Date nowDate = com.yxt.common.util.DateUtil.currentTime();
        String key = DateTimeUtil.dateToString(nowDate, DateTimeUtil.YYYY_MM_DD_HH_MM);
        keys.add(key);
        for (int i = 1; i <= 30; i++) {
            Date tempDate = new Date(nowDate.getTime() - (1000 * 60 * i));
            String tempKey = DateTimeUtil.dateToString(tempDate, DateTimeUtil.YYYY_MM_DD_HH_MM);
            keys.add(tempKey);
        }

        List<Object> list = talentRedisRepository.opsForHash().multiGet(RedisKeys.CK_CALI_MEET_CHANGE_STATUS, keys);
        List<String> ids = new ArrayList<>();
        list.forEach(a -> {
            if (a == null) {
                return;
            }

            String[] splitList = a.toString().split(CACHE_VAL_SPLIT);
            ids.addAll(Arrays.asList(splitList));
        });

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 根据Ids 查询处于 未开始 状态的会议 -- 对于消息（利用数据库中状态的一致性，避免重复发送消息）
        List<CaliMeetPO> meetingLis = caliMeetMapper.listByMeetStatusAndIdIn(
                CaliMeet.CaliMeetStatusEnum.DEFAULT.getCode(), ids);
        if (CollectionUtils.isEmpty(meetingLis)) {
            return;
        }
        // 修改会议状态 进行中
        meetingLis.forEach(a -> a.setMeetStatus(CaliMeet.CaliMeetStatusEnum.UNDERWAY.getCode()));
        for (CaliMeetPO caliMeet : meetingLis) {
            caliMeetMapper.updateById(caliMeet);
        }

        // 删除已处理的key
        talentRedisRepository.opsForHash().delete(RedisKeys.CK_CALI_MEET_CHANGE_STATUS, keys.toArray());

        // 发送会议开始消息
        caliMeetMsgSender.sendTemplateMessageForMeetingStart(meetingLis);
    }

    /**
     * 立即发送的情形
     *
     * @param cm
     */
    @Async
    public void changeMeetingStatusAndSendMsg(CaliMeetPO cm) {
        // 修改会议状态为进行中
        cm.setMeetStatus(CaliMeet.CaliMeetStatusEnum.UNDERWAY.getCode());

        if (StringUtils.isNotBlank(cm.getId())) {
            // 如果ID存在，更新或插入数据
            updateOrInsertCaliMeet(cm);
        } else {
            // 如果ID不存在，直接插入数据
            if (StringUtils.isBlank(cm.getMeetMinutes())) {
                cm.setMeetMinutes("");
            }
            if (cm.getDuration() == null) {
                cm.setDuration(0);
            }
            if (cm.getMeetRouteStatus() == null) {
                cm.setMeetRouteStatus(1);
            }
            if (cm.getMeetStatus() == null) {
                cm.setMeetStatus(0);
            }
            caliMeetMapper.insertOrUpdate(cm);
        }

        // 发送会议开始消息
        caliMeetMsgSender.sendTemplateMessageForMeetingStart(Collections.singletonList(cm));
    }

    private void updateOrInsertCaliMeet(CaliMeetPO cm) {
        CaliMeetPO caliMeetPo = caliMeetMapper.selectByIdAndOrgId(cm.getId(), cm.getOrgId());
        if (caliMeetPo != null) {
            caliMeetMapper.updateById(cm);
        } else {
            if (StringUtils.isBlank(cm.getMeetMinutes())) {
                cm.setMeetMinutes("");
            }
            if (cm.getDuration() == null) {
                cm.setDuration(0);
            }
            if (cm.getMeetRouteStatus() == null) {
                cm.setMeetRouteStatus(1);
            }
            if (cm.getMeetStatus() == null) {
                cm.setMeetStatus(0);
            }
            caliMeetMapper.insertOrUpdate(cm);
        }
    }


    /**
     * 每小时执行一次，扫描校准会表，将符合条件的数据放入缓存中，等待分钟扫描器处理。
     */
    public void putCache() {
        Date startTime = this.getNowHourStartTime(); // 当前小时 2022-02-02 12:00:00
        Date endTime = this.getNextHourStartTime(); // 下一小时  2022-02-02 13:00:00
        List<CaliMeetPO> meetList = calMeetMapper.listByMeetTimeRange(startTime, endTime);
        Set<String> orgIds = meetList.stream().map(CaliMeetPO::getOrgId).collect(Collectors.toSet());
        log.info("LOG14145:putCache orgIds={}", orgIds);

        Map<String, String> tempMap = new HashMap<>(8);
        meetList.forEach(meet -> {
            String key = DateTimeUtil.dateToString(meet.getMeetTime(), DateTimeUtil.YYYY_MM_DD_HH_MM);
            String value = meet.getId();
            log.info("LOG64390:{}", meet.getMeetName());
            if (tempMap.containsKey(key)) {
                tempMap.put(key, tempMap.get(key) + CACHE_VAL_SPLIT + value);
            } else {
                tempMap.put(key, value);
            }
        });

        if (!tempMap.isEmpty()) {
            talentRedisRepository.opsForHash().putAll(RedisKeys.CK_CALI_MEET_CHANGE_STATUS, tempMap);
            talentRedisRepository.expire(RedisKeys.CK_CALI_MEET_CHANGE_STATUS, CACHE_TIME, TimeUnit.HOURS);
        }
    }

    /**
     * 会议有编辑、保存等动作时 （将数据重新放入缓存中）
     */
    public void putCache(@Nullable CaliMeetPO meet) {
        if (meet == null) {
            return;
        }

        Date nowDate = com.yxt.common.util.DateUtil.currentTime();
        // 会议时间 小于 当前时间 + 1分钟
        if (meet.getMeetTime().getTime() < (nowDate.getTime() + (1000 * 60))) {
            // 立即发送消息、立即变更会议状态 （为何立即发送：1.时间已接近，2.放入缓存可能会错过分钟扫描器）
            log.info("LOG61600:编辑保存动作putCache-> 立即发送消息、立即变更会议状态{},{}", meet.getMeetName(),
                    meet.getId());
            changeMeetingStatusAndSendMsg(meet);
            return;
        }

        // 只缓存当前小时的数据 （对于卡时间，刚好59分59秒的情况(错过了分钟扫描器)，上个判断+1分钟 已解决）
        Date nowHourStartTime = getHourStartTime(nowDate);
        Date meetHourStartTime = getHourStartTime(meet.getMeetTime());
        if (nowHourStartTime.getTime() != meetHourStartTime.getTime()) {
            return;
        }

        // 缓存数据
        Map<String, String> tempMap = new HashMap<>(8);
        String key = DateTimeUtil.dateToString(meet.getMeetTime(), DateTimeUtil.YYYY_MM_DD_HH_MM);
        String value = meet.getId();
        tempMap.put(key, value);

        log.info("LOG61610:编辑保存动作putCache->{},{}", meet.getMeetName(), meet.getId());

        talentRedisRepository.opsForHash().putAll(RedisKeys.CK_CALI_MEET_CHANGE_STATUS, tempMap);
        talentRedisRepository.expire(RedisKeys.CK_CALI_MEET_CHANGE_STATUS, CACHE_TIME, TimeUnit.HOURS);
    }

    private Date getNowHourStartTime() {
        return getHourStartTime(com.yxt.common.util.DateUtil.currentTime());
    }

    private Date getHourStartTime(Date date) {
        Calendar nd = Calendar.getInstance();
        nd.setTime(date);
        nd.set(Calendar.MINUTE, 0);
        nd.set(Calendar.SECOND, 0);
        nd.set(Calendar.MILLISECOND, 0);
        return nd.getTime();
    }

    private Date getNextHourStartTime() {
        Date nextHour = new Date(com.yxt.common.util.DateUtil.currentTime().getTime() + (1000 * 60 * 60));
        Calendar nd = Calendar.getInstance();
        nd.setTime(nextHour);
        nd.set(Calendar.MINUTE, 0);
        nd.set(Calendar.SECOND, 0);
        nd.set(Calendar.MILLISECOND, 0);
        return nd.getTime();
    }

    /**
     * 校准人员分页列表 -多部门查询
     */
    public PagingList<CaliMeetUserVO> findPage(PageRequest pageRequest, String meetingId, CaliMeetUserQuery search,
            UserCacheDetail userCache) {
        Page<CaliMeetUserVO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        /*List<String> allPrjUserIds = caliMeetUserMapper.selectByMeetingId4UserIds(userCache.getOrgId(), meetingId);
        List<String> authUserIds = authorizationService.getAuthUserIdsRange(userCache, NAV_CODE,
            DATA_PERMISSION_CODE, allPrjUserIds);
        if (!"1".equals(userCache.getAdmin()) && CollectionUtils.isEmpty(authUserIds)) {
            int offset = (int) ((pageRequest.getCurrent() - 1) * pageRequest.getSize());
            return new PagingList<>(new ArrayList<>(), new Paging(pageRequest.getSize(), offset, 0, 0));
        }*/
        boolean enableLocalization = l10nAclService.isEnableLocalization(userCache.getOrgId());
        List<String> qwUserIds = orginitAclService.searchContactList(userCache.getOrgId(), search.getKeyword(), 1,
                userCache.getSourceCode());
        Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(userCache.getOrgId()),
                ResourceTypeEnum.USER, search.getKeyword());
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            qwUserIds.addAll(l10nUserIds);
        }
        IPage<CaliMeetUserVO> iPageList = caliMeetUserMapper.pagingVo(pageable, userCache.getOrgId(), meetingId, search,
                qwUserIds);
        l10nAclService.translateList(enableLocalization, List.of(userCache.getOrgId()), userCache.getLocale(),
                CaliMeetUserVO.class, iPageList.getRecords());

        return transform(iPageList, userCache.getOrgId(), meetingId, enableLocalization);
    }

    private PagingList<CaliMeetUserVO> transform(IPage<CaliMeetUserVO> iPageList, String orgId, String meetingId,
            boolean enableLocalization) {

        PagingList<CaliMeetUserVO> pageList = BeanCopierUtil.toPagingList(iPageList);
        if (CollectionUtils.isNotEmpty(pageList.getDatas())) {
            List<String> userIds = pageList.getDatas().stream().map(CaliMeetUserVO::getUserId)
                    .collect(Collectors.toList());

            CaliMeetPO caliMeet = caliMeetMapper.selectByIdAndOrgId(meetingId, orgId);
            String xpdId = caliMeet.getProjectId();
            // 校准会结果
            List<CaliMeetUserResultPO> tempList = caliMeetUserResultMapper.listByOrgIdAndMeetIdAndUserIdIn(orgId,
                    meetingId, userIds);
            Map<String, List<CaliMeetUserResultPO>> tempMap = tempList.stream()
                    .collect(Collectors.groupingBy(CaliMeetUserResultPO::getUserId));
            Map<String, IdName> idNameMap = new HashMap<>(8);
            if (enableLocalization) {
                Set<String> deptIds = pageList.getDatas().stream().map(CaliMeetUserVO::getDeptId)
                        .collect(Collectors.toSet());
                List<IdName> idNames = udpAclService.getDeptInfoByIds(orgId, new ArrayList<>(deptIds));
                if (CollectionUtils.isNotEmpty(idNames)) {
                    idNameMap = StreamUtil.list2map(idNames, IdName::getId);
                }
            }

            List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByXpdId(orgId, xpdId);
            Map<Integer, String> gridLevelMap = StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getOrderIndex,
                    XpdGridLevelPO::getLevelName);

            // 设置校准前、校准后值
            Map<String, IdName> finalIdNameMap = idNameMap;
            pageList.getDatas().forEach(result -> {
                Map<String, String> calibrationLevelMap = new HashMap<>(8);
                Map<String, BigDecimal> calibrationScoreMap = new HashMap<>(8);
                Map<String, String> originalLevelMap = new HashMap<>(8);
                Map<String, BigDecimal> originalScoreMap = new HashMap<>(8);
                if (StringUtils.isNotBlank(result.getUserId()) && Objects.nonNull(tempMap.get(result.getUserId()))) {
                    tempMap.get(result.getUserId()).forEach(a -> {
                        originalLevelMap.put(a.getDimensionId(), gridLevelMap.get(a.getOriginalLevel()));
                        originalScoreMap.put(a.getDimensionId(), a.getOriginalScore());
                        // 校准后
                        if (a.getInitType() == 0) {
                            calibrationLevelMap.put(a.getDimensionId(), null);
                            calibrationScoreMap.put(a.getDimensionId(), null);
                        } else {
                            calibrationLevelMap.put(a.getDimensionId(), gridLevelMap.get(a.getCalibrationLevel()));
                            calibrationScoreMap.put(a.getDimensionId(), a.getCalibrationScore());
                        }
                    });
                }
                result.setCalibrationLevelMap(calibrationLevelMap);
                result.setCalibrationScoreMap(calibrationScoreMap);
                result.setOriginalScoreMap(originalScoreMap);
                result.setOriginalLevelMap(originalLevelMap);

                if (MapUtils.isNotEmpty(finalIdNameMap) && null != finalIdNameMap.get(result.getDeptId())) {
                    result.setDeptName(finalIdNameMap.get(result.getDeptId()).getName());
                }

            });
        }

        return pageList;
    }

    /**
     * 获取详情
     *
     * @param id
     * @param orgId
     */
    public CaliMeetUserResultVO getMeetingUser4GetById(String id, String orgId, String lang) {
        CaliMeetUserPO calMeetUser = caliMeetUserMapper.selectByIdAndOrgId(id, orgId);
        if (calMeetUser == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_USER_NOT_EXISTED);
        }
        CaliMeetUserVO caliMeetUserVO = caliMeetUserMapper.selectVoByIdAndOrgId(id, orgId);
        if (caliMeetUserVO == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_USER_NOT_EXISTED);
        }
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, CaliMeetUserVO.class,
                List.of(caliMeetUserVO));

        CaliMeetUserResultVO detail = new CaliMeetUserResultVO();
        BeanCopierUtil.copy(caliMeetUserVO, detail);

        // 绩效计算类型
       /* List<PrjDimConfPO> prjDimConfPOS =
                prjDimConfMapper.queryByPrjAndType(orgId, calMeetUser.getProjectId(),
                        Collections.singletonList(1));
        if (CollectionUtils.isEmpty(prjDimConfPOS)) {
            return detail;
        }*/
        //PrjDimConfPO prjDimConfPO = prjDimConfPOS.get(0);
       /* PrjDimRulePO prjDimRulePO = prjDimRuleMapper.selectByDimConfId(orgId, prjDimConfPO.getId());
        if (prjDimRulePO != null) {
            detail.setClassType(prjDimRulePO.getClassType());
        }*/
        String xpdId = calMeetUser.getProjectId();
        List<XpdGridLevelPO> gridLevelPOS = gridLevelMapper.listByXpdId(orgId, xpdId);
        Map<Integer, String> gridLevelMap = StreamUtil.list2map(gridLevelPOS, XpdGridLevelPO::getOrderIndex,
                XpdGridLevelPO::getLevelName);
        List<String> userIds = new ArrayList<>();
        userIds.add(calMeetUser.getUserId());

        List<CaliMeetUserResultPO> tempList = caliMeetUserResultMapper.listByOrgIdAndMeetIdAndUserIdIn(orgId,
                calMeetUser.getMeetingId(), userIds);
        tempList.forEach(a -> {
            detail.getOriginalLevelMap().put(a.getDimensionId(), gridLevelMap.get(a.getOriginalLevel()));
            detail.getOriginalScoreMap().put(a.getDimensionId(), a.getOriginalScore());
            detail.getCalibrationLevelMap().put(a.getDimensionId(), gridLevelMap.get(a.getCalibrationLevel()));
            detail.getCalibrationScoreMap().put(a.getDimensionId(), a.getCalibrationScore());
        });

        return detail;
    }

    @Nullable
    public CaliMeetUserPO getByIdAndOrgId(String id, String orgId) {
        return caliMeetUserMapper.selectByIdAndOrgId(id, orgId);
    }

    /**
     * 添加待校准人员
     *
     * @param meetingUser4BatchAdd 主数据
     * @param currentUserId        当前用户Id
     * @param orgId                当前机构Id
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void batchAddUser(CaliMeetUserBatchAddCmd meetingUser4BatchAdd, String currentUserId, String orgId) {

        if (meetingUser4BatchAdd.getUserIdList().isEmpty()) {
            throw new ApiException(ExceptionKeys.CALI_MEET_USER_BATCH_ADD_LIST_EMPTY);
        }
        // 查询会议所属项目id
        CaliMeetPO meeting = caliMeetMapper.selectByIdAndOrgId(meetingUser4BatchAdd.getMeetingId(), orgId);
        if (null == meeting) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        String projectId = meeting.getProjectId();
        String meetingId = meeting.getId();

        // 过滤掉已经添加过的用户
        List<CaliMeetUserPO> oldCaliMeetUsers = caliMeetUserMapper.listByOrgIdAndMeetingId(orgId, meeting.getId());
        if (!oldCaliMeetUsers.isEmpty()) {
            Map<String, String> oldMeetingUserMap = oldCaliMeetUsers.stream()
                    .collect(Collectors.toMap(CaliMeetUserPO::getUserId, CaliMeetUserPO::getUserId, (a, b) -> b));
            List<String> userIds = meetingUser4BatchAdd.getUserIdList().stream()
                    .filter(m -> !oldMeetingUserMap.containsKey(m)).collect(Collectors.toList());
            meetingUser4BatchAdd.setUserIdList(userIds);
            if (meetingUser4BatchAdd.getUserIdList().isEmpty()) {
                log.warn(
                        "LOG63050: 过滤掉已经添加过的用户 -> 用户id过滤后list为空[meetingUser4BatchAdd.getUserIdList().isEmpty()]");
                return;
            }
        }

        // 查询被添加人员【项目盘点人员数据】
        /*List<PrjUserPO> prjUserList =
                prjUserMapper.selectByOrgIdAndPrjIdAndUserIds(orgId, meeting.getProjectId(),
                        meetingUser4BatchAdd.getUserIdList());*/

        // 查询盘点人员
        String xpdId = meeting.getProjectId();
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);

        Long partId = activityParticipationService.getParticipationId(orgId, xpd.getAomPrjId());
        // 项目人数

        List<String> realUserIds = activityParticipationMemberMapper.findUserIdByActIdAndUserIds(orgId,
                xpd.getAomPrjId(), partId, meetingUser4BatchAdd.getUserIdList());
        if (realUserIds.isEmpty()) {
            throw new ApiException(ExceptionKeys.CALI_MEET_USER_NOT_IN_PRJ_USER);
        }

        /*if (prjUserList.isEmpty()) {
            throw new ApiException(ExceptionKeys.CALI_MEET_USER_NOT_IN_PRJ_USER);
        }*/
        // 查询被添加人员【项目盘点维度数据】
        List<PrjUserResultPO> clcResultList = prjUserResultAppService.findByProjectIdAndUserIds(orgId,
                meeting.getProjectId(), meetingUser4BatchAdd.getUserIdList());
        // 根据项目盘点人员数据 -> 组合、构造校准会盘点人员数据
        List<CaliMeetUserPO> calMeetUsers = generateMeetingUserList(orgId, meetingUser4BatchAdd.getMeetingId(),
                currentUserId, realUserIds, projectId);
        // 根据项目项目盘点维度数据 -> 组合、构造校准会盘点人员维度数据
        /*List<CaliMeetUserResultPO> resultList = generateCalibrationMeetingResultList(orgId,
                meetingUser4BatchAdd.getMeetingId(), calMeetUsers, clcResultList, currentUserId);*/

        List<CaliMeetUserResultPO> resultList = dealCalibrationResult(orgId, projectId, meetingId, realUserIds,
                currentUserId);

        // 批量保存
        if (CollectionUtils.isNotEmpty(calMeetUsers)) {
            caliMeetUserMapper.insertList(calMeetUsers);
        }
        if (CollectionUtils.isNotEmpty(resultList)) {
            caliMeetUserResultMapper.insertList(resultList);
        }
        log.warn("LOG50160:{}", BeanHelper.bean2Json(meeting, ALWAYS));
    }

    private List<CaliMeetUserResultPO> dealCalibrationResult(String orgId, String projectId, String meetingId,
            List<String> realUserIds, String currentUserId) {
        List<CaliMeetUserResultPO> resList = new ArrayList<>();
        List<XpdResultUserDimPO> resultUserDimList = xpdResultUserDimMapper.findByXpdIdAndUserIds(orgId, projectId,
                realUserIds);
        Map<String, List<XpdResultUserDimPO>> resultUserDimsMap = resultUserDimList.stream()
                .collect(Collectors.groupingBy(XpdResultUserDimPO::getUserId));

        List<XpdDimPO> xpdDimList = xpdDimMapper.listByXpdId(orgId, projectId);
        List<XpdGridLevelPO> gridLevelPOS = gridLevelMapper.listByXpdId(orgId, projectId);
        Map<String, Integer> levelMap = StreamUtil.list2map(gridLevelPOS, XpdGridLevelPO::getId,
                XpdGridLevelPO::getOrderIndex);
        for (String userId : realUserIds) {
            if (!resultUserDimsMap.containsKey(userId)) {
                for (XpdDimPO xpdDimPO : xpdDimList) {
                    CaliMeetUserResultPO caliMeetUserResult = new CaliMeetUserResultPO();
                    caliMeetUserResult.setId(ApiUtil.getUuid());
                    caliMeetUserResult.setOrgId(orgId);
                    caliMeetUserResult.setMeetingId(meetingId);
                    caliMeetUserResult.setProjectId(projectId);
                    caliMeetUserResult.setUserId(userId);
                    caliMeetUserResult.setDimensionId(xpdDimPO.getSdDimId());
                    EntityUtil.setAuditFields(caliMeetUserResult, currentUserId);
                    resList.add(caliMeetUserResult);
                }

            } else {
                List<XpdResultUserDimPO> userDimList = resultUserDimsMap.get(userId);
                Map<String, XpdResultUserDimPO> userDimMap = StreamUtil.list2map(userDimList,
                        XpdResultUserDimPO::getSdDimId);
                for (XpdDimPO xpdDimPO : xpdDimList) {
                    String dimId = xpdDimPO.getSdDimId();
                    XpdResultUserDimPO resultUserDim = userDimMap.get(dimId);
                    if (resultUserDim == null) {
                        CaliMeetUserResultPO caliMeetUserResult = new CaliMeetUserResultPO();
                        caliMeetUserResult.setId(ApiUtil.getUuid());
                        caliMeetUserResult.setOrgId(orgId);
                        caliMeetUserResult.setMeetingId(meetingId);
                        caliMeetUserResult.setProjectId(projectId);
                        caliMeetUserResult.setUserId(userId);
                        caliMeetUserResult.setDimensionId(xpdDimPO.getSdDimId());
                        EntityUtil.setAuditFields(caliMeetUserResult, currentUserId);
                        resList.add(caliMeetUserResult);
                    } else {
                        CaliMeetUserResultPO caliMeetUserResult = new CaliMeetUserResultPO();
                        caliMeetUserResult.setId(ApiUtil.getUuid());
                        caliMeetUserResult.setOrgId(orgId);
                        caliMeetUserResult.setMeetingId(meetingId);
                        caliMeetUserResult.setProjectId(projectId);
                        caliMeetUserResult.setUserId(userId);
                        caliMeetUserResult.setDimensionId(xpdDimPO.getSdDimId());
                        EntityUtil.setAuditFields(caliMeetUserResult, currentUserId);
                        caliMeetUserResult.setOriginalScore(resultUserDim.getScoreValue());
                        caliMeetUserResult.setOriginalLevel(levelMap.get(resultUserDim.getGridLevelId()));
                        caliMeetUserResult.setCalibrationScore(resultUserDim.getScoreValue());
                        caliMeetUserResult.setCalibrationLevel(levelMap.get(resultUserDim.getGridLevelId()));
                        resList.add(caliMeetUserResult);
                    }
                }

            }
        }
        return resList;

    }


    @Nullable
    public CaliMeetUserPO getById(String id, String orgId) {
        return caliMeetUserMapper.selectByIdAndOrgId(id, orgId);
    }

    public void updateById(CaliMeetUserPO calMeetUser) {
        caliMeetUserMapper.updateById(calMeetUser);
    }

    /**
     * 批量删除人员及人员下维度数据
     *
     * @param ids       id主键列表
     * @param meetingId 会议id
     * @param orgId     机构id
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void batchRemove(List<String> ids, String meetingId, String orgId) {
        List<CaliMeetUserPO> calMeetUserList = caliMeetUserMapper.listByOrgIdAndMeetingIdAndIdIn(orgId, meetingId, ids);
        List<String> userIds = calMeetUserList.stream().map(CaliMeetUserPO::getUserId).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return;
        }
        // 维度删除
        caliMeetUserResultMapper.deleteByOrgIdAndMeetIdAndUserIdIn(orgId, meetingId, userIds);
        // 人员删除
        caliMeetUserMapper.deleteByOrgIdAndIdIn(orgId, ids);
    }

    /**
     * 根据指定参数 构造校准会盘点人员数据<br>
     * 用于添加校准会人员
     *
     * @param orgId         当前机构Id
     * @param meetingId     会议id
     * @param currentUserId 当前登录人id
     * @param realUserIds   项目人员
     * @return List<MeetingUser>
     */
    private List<CaliMeetUserPO> generateMeetingUserList(String orgId, String meetingId, String currentUserId,
            List<String> realUserIds, String projectId) {
        List<CaliMeetUserPO> resultList = new ArrayList<>();
        realUserIds.forEach(pu -> {
            CaliMeetUserPO temp = new CaliMeetUserPO();
            temp.setId(ApiUtil.getUuid());
            temp.setMeetingId(meetingId);
            temp.setOrgId(orgId);
            temp.setUserId(pu);
            temp.setProjectId(projectId);
            temp.setSuggestion("");
            // temp.setSuggestion(pu.getSuggestion()); // 业务没有要求、暂不设置
            EntityUtil.setAuditFields(temp, currentUserId);
            resultList.add(temp);
        });
        /*prjUserList.forEach(pu -> {
            CaliMeetUserPO temp = new CaliMeetUserPO();
            temp.setId(ApiUtil.getUuid());
            temp.setMeetingId(meetingId);
            temp.setOrgId(orgId);
            temp.setUserId(pu.getUserId());
            temp.setProjectId(pu.getProjectId());
            temp.setSuggestion("");
            // temp.setSuggestion(pu.getSuggestion()); // 业务没有要求、暂不设置
            EntityUtil.setAuditFields(temp, currentUserId);
            resultList.add(temp);
        });*/

        return resultList;
    }

    /**
     * 根据指定参数 构造校准会盘点人员维度数据<br>
     * 用于添加校准会人员
     *
     * @param orgId          当前机构Id
     * @param meetingId      会议id
     * @param calcResultList 项目计算结果集合
     * @param currentUserId  当前登录人id
     * @return List<CalibrationMeetingResult>
     */
    private List<CaliMeetUserResultPO> generateCalibrationMeetingResultList(String orgId, String meetingId,
            List<CaliMeetUserPO> calMeetUsers, List<PrjUserResultPO> calcResultList, String currentUserId) {
        List<CaliMeetUserResultPO> resultList = new ArrayList<>();
        Map<String, List<PrjUserResultPO>> calcResultMap = calcResultList.stream()
                .collect(Collectors.groupingBy(PrjUserResultPO::getUserId));
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.listActiveDimConfByPrjId(orgId,
                calMeetUsers.get(0).getProjectId());
        calMeetUsers.forEach(mu -> {
            if (calcResultMap.containsKey(mu.getUserId())) {
                // 项目中有这个人的计算结果
                generateCalibrationMeetingResultListSub(orgId, meetingId, resultList, calcResultMap.get(mu.getUserId()),
                        prjDimConfs, currentUserId);
            } else {
                // 项目中没有计算结果
                generateCalibrationMeetingResultListSub(orgId, meetingId, resultList, mu, prjDimConfs, currentUserId);
            }
        });
        return resultList;
    }

    /**
     * sub1 *
     */
    private void generateCalibrationMeetingResultListSub(String orgId, String meetingId,
            List<CaliMeetUserResultPO> resultList, CaliMeetUserPO mu, List<PrjDimConfPO> prjDimConfs,
            String currentUserId) {
        prjDimConfs.forEach(dc -> {
            CaliMeetUserResultPO temp = new CaliMeetUserResultPO();
            temp.setMeetingId(meetingId);
            temp.setOrgId(orgId);
            temp.setUserId(mu.getUserId());
            temp.setProjectId(mu.getProjectId());
            temp.setDimensionId(dc.getDimensionId());
            temp.setId(ApiUtil.getUuid());
            EntityUtil.setAuditFields(temp, currentUserId);
            resultList.add(temp);
        });
    }

    /**
     * sub2 *
     */
    private void generateCalibrationMeetingResultListSub(String orgId, String meetingId,
            List<CaliMeetUserResultPO> resultList, List<PrjUserResultPO> calcResultList, List<PrjDimConfPO> prjDimConfs,
            String currentUserId) {

        Map<String, List<PrjUserResultPO>> resultMap = calcResultList.stream()
                .collect(Collectors.groupingBy(PrjUserResultPO::getDimensionId));

        BigDecimal bd = new BigDecimal("0");
        prjDimConfs.forEach(dc -> {
            CaliMeetUserResultPO temp = new CaliMeetUserResultPO();
            if (resultMap.containsKey(dc.getDimensionId())) {
                PrjUserResultPO cr = resultMap.get(dc.getDimensionId()).get(0);
                temp.setId(ApiUtil.getUuid());
                temp.setMeetingId(meetingId);
                temp.setOrgId(orgId);
                temp.setUserId(cr.getUserId());
                temp.setProjectId(cr.getProjectId());
                temp.setDimensionId(cr.getDimensionId());
                EntityUtil.setAuditFields(temp, currentUserId);
                temp.setOriginalScore(cr.getLastScore().compareTo(bd) < 0 ? cr.getInitScore() : cr.getLastScore());
                temp.setOriginalLevel(cr.getLastLevel() == 0 ? cr.getInitLevel() : cr.getLastLevel());
                // 初始化时候校准前和校准后保持一致
                temp.setCalibrationScore(temp.getOriginalScore());
                temp.setCalibrationLevel(temp.getOriginalLevel());
                resultList.add(temp);
            } else {
                temp.setMeetingId(meetingId);
                temp.setOrgId(orgId);
                temp.setUserId(calcResultList.get(0).getUserId());
                temp.setProjectId(calcResultList.get(0).getProjectId());
                temp.setDimensionId(dc.getDimensionId());
                temp.setId(ApiUtil.getUuid());
                EntityUtil.setAuditFields(temp, currentUserId);
                resultList.add(temp);
            }
        });
    }

    /**
     * 开启校准会
     *
     * @param id
     * @param userId
     * @param orgId
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void openMeeting(String id, String userId, String orgId, UserCacheDetail operator, String token) {
        // 验证
        int count = caliMeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CaliMeetPO cmEntity = caliMeetMapper.selectByIdAndOrgId(id, orgId);
        if (cmEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        cmEntity.setMeetStatus(CaliMeet.CaliMeetStatusEnum.UNDERWAY.getCode());
        EntityUtil.setUpdate(cmEntity, userId);
        caliMeetMapper.updateById(cmEntity);
        // 发送消息
        CommonUtil.execAfterCommitIfHas(() -> caliMeetMsgSender.sendTemplateMessageForJoinUser(token, operator, cmEntity));
    }

    /**
     * 获取项目校准会数量
     *
     * @param orgId
     * @param projectId
     * @return
     */
    public int getMeetingCount(String orgId, String projectId) {
        return caliMeetMapper.countByOrgIdAndProjectId(orgId, projectId);
    }

    /**
     * 删除盘点项目的校准会
     *
     * @param orgId     机构id
     * @param projectId 项目id
     * @param userId    操作人
     * @return
     */
    public void deleteMeetingByProjectId(String orgId, String projectId, String userId) {
        caliMeetMapper.deleteMeetingByProjectId(orgId, projectId, userId);
    }

}
