package com.yxt.talent.rv.application.dmp.user.expt;

import com.alibaba.excel.EasyExcelFactory;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.service.ILock;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.downfacade.dto.File4CreateFinishMq;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExportMqService;
import com.yxt.export.I18nComponent;
import com.yxt.sptalentapifacade.bean.spjq.SpJqDetail4Get;
import com.yxt.sptalentapifacade.bean.spjq.SpJqItem4Get;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpRuleLayerVO;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpUserPageVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleLayerMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserDimResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserResultPO;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExporter;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericFileExportVO;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SptalentAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SptalentAclServiceImpl;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 管理端-概览页面-导出人员匹配明细
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DmpUserResultExporter extends FileExporter {

    public static final String TASK_NAME = "apis.sptalentrv.dmp.user_result.export.file.name";
    private static final String HEADER_PREFIX = "apis.sptalentrv.dmp.user_result.export.sheet1.";
    private static final String[] HEADER_KEYS =
            {"fullname", "username", "pos_name", "dept_name", "match_type", "actual_score", "if_matched", "match_rate", "score", "match_layer"};


    private static final int MAX_LEASE_TIME = 100;
    private final DmpMapper dmpMapper;
    private final DmpUserMapper dmpUserMapper;
    private final DmpTaskDimMapper dmpTaskDimMapper;
    private final DmpTaskMapper dmpTaskMapper;
    private final DmpRuleLayerMapper dmpRuleLayerMapper;
    private final DmpUserResultMapper dmpUserResultMapper;
    private final DmpRuleMapper dmpRuleMapper;
    private final ILock lockService;
    private final DmpUserDimResultMapper userDimResultMapper;
    private final DmpUserExportStrategy dmpUserExportStrategy;
    private final DlcComponent dlcComponent;
    private final ExportMqService exportMqService;
    private final SptalentAclService sptalentAclService;
    private final I18nComponent i18nComponent;
    private final L10nAclService l10nAclService;
    private final UdpAclService udpAclService;

    @Value("${downloadcenter.path:/data/downloadcenter/}")
    private String centerPath;

    /**
     * 动态评价人员导出
     *
     * @param dmpId
     * @param orgId
     */
    public GenericFileExportVO export(
            String dmpId, String orgId, String operator, String lang) {
        String lockKey = String.format(RedisKeys.LK_DMP_USER_RESULT_EXPORT, orgId, dmpId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                return getDmpUserExport(orgId, dmpId, lang);
            } finally {
                lockService.unLock(lockKey);
            }
        }
        return GenericFileExportVO.EMPTY_RESULT;
    }

    private GenericFileExportVO getDmpUserExport(
            String orgId, String dmpId, String lang) {

        // 获取项目人员
        List<DmpUserPageVO> allDmpUser = dmpUserMapper.selectAllDmpUser(orgId, dmpId);
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, DmpUserPageVO.class, allDmpUser);

        Map<String, IdName> idNameMap = new HashMap<>(8);
        if(enableLocalization){
            Set<String> deptIds =
                    allDmpUser.stream().map(DmpUserPageVO::getDeptId).collect(Collectors.toSet());
            List<IdName> idNames =
                    udpAclService.getDeptInfoByIds(
                            orgId, new ArrayList<>(deptIds));
            if (CollectionUtils.isNotEmpty(idNames)) {
                idNameMap = StreamUtil.list2map(idNames, IdName::getId);
            }
        }
        Map<String, IdName> finalIdNameMap = idNameMap;
        allDmpUser.forEach(e -> {
            if(MapUtils.isNotEmpty(finalIdNameMap) && null != finalIdNameMap.get(e.getDeptId())){
                e.setDeptName(finalIdNameMap.get(e.getDeptId()).getName());
            }
        });

        // 获取维度信息
        List<DmpTaskDimPO> dmpTaskDims = dmpTaskDimMapper.selectByDmpId(orgId, dmpId);
        if (CollectionUtils.isEmpty(dmpTaskDims)) {
            return GenericFileExportVO.EMPTY_RESULT;
        }
        DmpPO dmp = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
        if (dmp == null) {
            throw new ApiException(ExceptionKeys.DMP_NOT_EXISTS);
        }

        SpJqDetail4Get spJqDetail4Get =
                sptalentAclService.getSpJqDetail4Get(dmpTaskDims, orgId, dmp);
        // 规则
        DmpRulePO matchRule = dmpRuleMapper.selectRuleByDmpId(orgId, dmpId);
        if (matchRule == null) {
            throw new ApiException(ExceptionKeys.DMP_RULE_NOT_EXISTS);
        }
        Integer matchType = matchRule.getMatchType();

        List<List<String>> heads = initHead(spJqDetail4Get, matchType);
        // 填充值
        List<List<String>> datas = initData(spJqDetail4Get, allDmpUser, orgId, dmpId, matchType);

        String fileName = getFileName();
        String downFileName = fileName + FileConstants.ORIG;
        String path = centerPath;

        String sheetName = dmp.getDmpName() + "(动态)";
        EasyExcelFactory.write(path + fileName).head(heads).sheet(sheetName).doWrite(datas);

        long taskId = dlcComponent.prepareExport(fileName, dmpUserExportStrategy);
        exportMqService.sendStartMq(taskId);
        File4CreateFinishMq file4CreateFinishMq = new File4CreateFinishMq();
        file4CreateFinishMq.setId(String.valueOf(taskId));
        file4CreateFinishMq.setStatus(0);
        file4CreateFinishMq.setFileName(downFileName);
        file4CreateFinishMq.setGenerateFinishTime(DateUtil.now());
        file4CreateFinishMq.setLocalPath(fileName);
        exportMqService.sendMq4FileCreateFinish(file4CreateFinishMq);

        return GenericFileExportVO.byFilePath(downFileName);
    }

    private @Nonnull String getFileName() {
        String dateToString = DateTimeUtil.dateToString(new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS);
        return i18nComponent.getI18nValue(TASK_NAME) + "-" + dateToString + FileConstants.FILE_SUFFIX_XLSX;
    }

    private List<List<String>> initData(
            @Nullable SpJqDetail4Get spJqDetail4Get, List<DmpUserPageVO> allDmpUser, String orgId,
            String dmpId, Integer matchType) {

        List<DmpTaskDimPO> taskDimList = dmpTaskDimMapper.selectByDmpId(orgId, dmpId);
        Map<String, DmpTaskDimPO> taskDimMap =
                StreamUtil.list2map(taskDimList, DmpTaskDimPO::getJqDimId);

        List<DmpTaskPO> dmpTasks = dmpTaskMapper.selectByDmpId(orgId, dmpId);
        Map<String, DmpTaskPO> taskMap = StreamUtil.list2map(dmpTasks, DmpTaskPO::getId);

        List<DmpUserDimResultPO> userDimResults =
                userDimResultMapper.selectByDmpId(orgId, dmpId, "");
        Map<String, List<DmpUserDimResultPO>> userDimMap = userDimResults.stream()
                .collect(Collectors.groupingBy(DmpUserDimResultPO::getUserId));

        List<DmpUserResultPO> userResults = dmpUserResultMapper.selectByDmpId(orgId, dmpId);
        Map<String, DmpUserResultPO> userResultMap =
                StreamUtil.list2map(userResults, DmpUserResultPO::getUserId);

        List<DmpRuleLayerVO> layerRuleVos = dmpRuleLayerMapper.listVoByDmpId(orgId, dmpId);
        Map<String, DmpRuleLayerVO> layerRuleVOMap =
                StreamUtil.list2map(layerRuleVos, DmpRuleLayerVO::getId);

        // 维度 任务关系
        List<SpJqItem4Get> jqList = spJqDetail4Get != null ? spJqDetail4Get.getJqSetting4GetList() : new ArrayList<>();
        List<List<String>> resList = new ArrayList<>();
        for (DmpUserPageVO user : allDmpUser) {
            List<String> list = new ArrayList<>();
            list.add(user.getFullname());
            list.add(user.getUsername());
            list.add(user.getPositionName());
            list.add(user.getDeptName());

            for (SpJqItem4Get item : jqList) {
                String dimId = item.getDimId();
                DmpTaskDimPO dmpTaskDim = taskDimMap.get(dimId);
                if (dmpTaskDim == null) {
                    list.add("");
                    list.add("");
                    list.add("");
                    continue;
                }
                String taskId = dmpTaskDim.getTaskId();
                DmpTaskPO dmpTask = taskMap.get(taskId);
                if (dmpTask == null) {
                    list.add("");
                    list.add("");
                    list.add("");
                    continue;
                }
                // 匹配方式,实际得分，是否匹配
                getDimResult(userDimMap, user.getUserId(), dimId, list, dmpTask.getTaskType());
            }

            // 匹配率，分值
            DmpUserResultPO dmpUserResult = userResultMap.get(user.getUserId());
            if (dmpUserResult != null) {

                if (matchType == 0) {
                    // 匹配率
                    list.add(dmpUserResult.getScore().setScale(2, RoundingMode.HALF_UP) + "%");
                } else {
                    // 分值
                    list.add(String.valueOf(
                            dmpUserResult.getScore().setScale(2, RoundingMode.HALF_UP)));
                }

                // 匹配等级
                DmpRuleLayerVO dmpRuleLayerVO = layerRuleVOMap.get(dmpUserResult.getLayerId());
                if (dmpRuleLayerVO != null) {
                    list.add(dmpRuleLayerVO.getLayerName());
                } else {
                    list.add("");
                }
            } else {
                list.add("");
                list.add("");
            }
            resList.add(list);
        }

        return resList;
    }

    private void getDimResult(
            Map<String, List<DmpUserDimResultPO>> userDimMap, String userId, String dimId,
            List<String> list, Integer taskType) {

        DmpUserDimResultPO tempResult = null;
        List<DmpUserDimResultPO> userDimResults = userDimMap.get(userId);
        if (CollectionUtils.isNotEmpty(userDimResults)) {
            for (DmpUserDimResultPO result : userDimResults) {
                if (result.getJqDimId().equals(dimId)) {
                    tempResult = result;
                    break;
                }
            }
        }

        if (taskType == 0) {
            list.add("动态");
            list.add("/");
            if (tempResult != null) {
                list.add(tempResult.getMatched() == 1 ? "是" : "否");
            } else {
                list.add("");
            }
        } else {
            list.add("表单");
            if (tempResult != null) {
                list.add(String.valueOf(
                        tempResult.getFormScore().setScale(2, RoundingMode.HALF_UP)));
                list.add(tempResult.getMatched() == 1 ? "是" : "否");
            } else {
                list.add("");
                list.add("");
            }
        }
    }

    private List<List<String>> initHead(@Nullable SpJqDetail4Get spJqDetail4Get, Integer matchType) {
        List<List<String>> heads = new ArrayList<>();
        heads.add(makeHead(i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[0]))));
        heads.add(makeHead(i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[1]))));
        heads.add(makeHead(i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[2]))));
        heads.add(makeHead(i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[3]))));
        // 动态表头
        List<SpJqItem4Get> jqSetting4GetList = spJqDetail4Get != null ? spJqDetail4Get.getJqSetting4GetList() : new ArrayList<>();
        for (SpJqItem4Get item : jqSetting4GetList) {

            heads.add(makeHead(item.getRootCategoryName(), item.getDimCategoryName() + " ",
                    item.getDimName(),
                    i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[4]))));

            heads.add(makeHead(item.getRootCategoryName(), item.getDimCategoryName() + " ",
                    item.getDimName(),
                    i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[5]))));

            heads.add(makeHead(item.getRootCategoryName(), item.getDimCategoryName() + " ",
                    item.getDimName(),
                    i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[6]))));
        }
        if (matchType == 0) {
            heads.add(makeHead(i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[7]))));
        } else {
            heads.add(makeHead(i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[8]))));
        }
        heads.add(makeHead(i18nComponent.getI18nValue(buildHeaderI18nKey(HEADER_KEYS[9]))));

        return heads;
    }

    private static String buildHeaderI18nKey(String headerPrefix) {
        return HEADER_PREFIX + headerPrefix;
    }

    private List<String> makeHead(String... params) {
        return new ArrayList<>(Arrays.asList(params));
    }

}
