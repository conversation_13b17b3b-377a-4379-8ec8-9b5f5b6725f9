package com.yxt.talent.rv.application.prj.dim.legacy;

import com.alibaba.fastjson.JSON;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.spevalfacade.bean.evaluation.Evaluation;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4List;
import com.yxt.spevalfacade.bean.evaluation.EvaluationFacade;
import com.yxt.spevalfacade.bean.evaluation.EvaluationModelResp;
import com.yxt.spevalfacade.bean.standar.StandarReq;
import com.yxt.talent.rv.application.prj.dim.dto.PrjDimConfSimpleDTO;
import com.yxt.talent.rv.controller.manage.prj.dim.viewobj.PrjDimSameModelVO;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimSameModelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimSameModelPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PrjDimSameModelAppService {
    private final PrjDimSameModelMapper prjDimSameModelMapper;
    private final SpevalAclService spevalAclService;
    private final PrjDimConfToolMapper prjDimConfToolMapper;
    private final PrjDimConfMapper prjDimConfMapper;

    /**
     * 同方案下同一维度下测评
     *
     * @param orgId   机构id
     * @param orgId   机构id
     * @param modelId 维度id
     */
    public List<PrjDimSameModelVO> list(String orgId, String modelId, String prjId, String dimConfId) {
        log.info("LOG14335:getEvaluationModelListByModelId param orgId={};modelId={}", orgId, modelId);
        List<EvaluationModelResp> evaluationModelRespList = spevalAclService.getEvaluationModelListByModelId(orgId,
                modelId);
        log.info("LOG14325:getEvaluationModelListByModelId res orgId={};modelId={};res={}", orgId, modelId,
                JSON.toJSONString(evaluationModelRespList));
        //        //对测评方案过滤，暂时不处理倍智测评
        //        if (CollectionUtils.isNotEmpty(evaluationModelRespList)) {
        //            //评估方案类型(1-行为,2-问卷,3-人格)
        //            evaluationModelRespList = evaluationModelRespList.stream()
        //                    .filter(el -> !Integer.valueOf(3).equals(el.getEvaluationType())).collect(Collectors.toList());
        //        }
        List<PrjDimSameModelVO> sameModelList = prjDimSameModelMapper.listByModelIdAndDimConfId(orgId, modelId, prjId);
        List<PrjDimSameModelVO> otherDimSameModelList = sameModelList.stream()
                .filter(el -> !StringUtils.equals(dimConfId, el.getDimensionConfigId())).collect(Collectors.toList());
        //校验哪些已从项目中删除的
        List<PrjDimConfToolPO> prjDimConfToolList = prjDimConfToolMapper.selectByOrgIdAndPrjId(orgId, prjId);
        //剔除其他维度同模已删除的测评
        if (CollectionUtils.isNotEmpty(prjDimConfToolList) && CollectionUtils.isNotEmpty(otherDimSameModelList)) {
            List<String> evalToolIds = prjDimConfToolList.stream()
                    .filter(el -> Integer.valueOf(2).equals(el.getToolType()) || Integer.valueOf(4)
                            .equals(el.getToolType())).map(PrjDimConfToolPO::getToolId).toList();
            otherDimSameModelList = otherDimSameModelList.stream().filter(el -> evalToolIds.contains(el.getEvalId()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(evaluationModelRespList) && CollectionUtils.isEmpty(otherDimSameModelList)) {
            return new ArrayList<>(0);
        }
        List<String> currentPrjEvalIds = sameModelList.stream().map(PrjDimSameModelVO::getEvalId).toList();
        List<String> currPrjCurrDimEvalIds = sameModelList.stream()
                .filter(el -> dimConfId.equals(el.getDimensionConfigId())).map(PrjDimSameModelVO::getEvalId).toList();
        List<PrjDimSameModelVO> resultData = new ArrayList<>();
        setEvalReturnInfo(evaluationModelRespList, currPrjCurrDimEvalIds, resultData);
        List<String> existedEvalIds = resultData.stream().map(PrjDimSameModelVO::getEvalId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        //设置项目创建同模测评。由于是未发布
        setOtherDimSameModel(orgId, otherDimSameModelList, resultData, existedEvalIds);
        if (CollectionUtils.isEmpty(resultData)) {
            return new ArrayList<>(0);
        }
        //设置测评来源；来源包含：当前盘点项目、测评中心； 当前盘点项目中的测评，来源为当前盘点项目；其他非当前项目中的测评来源为测评中心
        resultData.forEach(item -> {
            item.setEvalSource(2);
            if (currentPrjEvalIds.contains(item.getEvalId())) {
                item.setEvalSource(1);
            }
        });
        List<String> prjDimConfIds = sameModelList.stream().map(PrjDimSameModelVO::getDimensionConfigId)
                .collect(Collectors.toList());
        // 获取当前维度方案存在的同模
        List<PrjDimConfToolPO> currentModelSame = prjDimConfToolMapper.selectByOrgIdAndDimConfIdAndToolSourceAndPrjId(
                orgId, prjId, dimConfId, 1);
        Map<String, PrjDimConfToolPO> currentMap = StreamUtil.list2map(currentModelSame, PrjDimConfToolPO::getToolId);

        List<PrjDimConfSimpleDTO> prjDimConfSimpleDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(prjDimConfIds)) {
            prjDimConfSimpleDtos = prjDimConfMapper.listSimplePrjDimConf(orgId, prjDimConfIds);
        }
        Map<String, PrjDimConfSimpleDTO> dimConf4BeanMap = StreamUtil.list2map(prjDimConfSimpleDtos,
                PrjDimConfSimpleDTO::getConfigId);
        // 数据组装
        resultData.forEach(data -> {
            PrjDimConfToolPO modelSame = currentMap.get(data.getEvalId());
            if (modelSame != null) {
                data.setReference(1);
            }
            if (StringUtils.isNotBlank(data.getDimensionConfigId())) {
                PrjDimConfSimpleDTO prjDimConfSimpleDTO = dimConf4BeanMap.get(data.getDimensionConfigId());
                if (prjDimConfSimpleDTO != null) {
                    data.setModelName(prjDimConfSimpleDTO.getDimensionName());
                }
            }
        });

        // 获取surveyId
        fillSurveyId(orgId, resultData);

        return resultData;
    }

    private void fillSurveyId(String orgId, List<PrjDimSameModelVO> resultData) {
        List<String> evalIds =
                resultData.stream().map(PrjDimSameModelVO::getEvalId).toList();
        StandarReq req = new StandarReq();
        req.setOrgId(orgId);
        req.setEvalIds(evalIds);
        List<Evaluation> batchEvaluation = spevalAclService.getBatchEvaluation(req);
        log.info("fillSurveyId batch={}", JSON.toJSONString(batchEvaluation));
        Map<String, Evaluation> evaluationMap =
                StreamUtil.list2map(batchEvaluation, Evaluation::getId);
        for (PrjDimSameModelVO resultDatum : resultData) {
            String evalId = resultDatum.getEvalId();
            Evaluation evaluation = evaluationMap.get(evalId);
            if (evaluation != null) {
                resultDatum.setSurveyId(evaluation.getTargetId());
            }
        }
    }

    private void setEvalReturnInfo(List<EvaluationModelResp> evaluationModelRespList,
            List<String> currPrjCurrDimEvalIds, List<PrjDimSameModelVO> resultData) {
        if (CollectionUtils.isNotEmpty(evaluationModelRespList)) {
            evaluationModelRespList.forEach(evaluationModelResp -> {
                if (currPrjCurrDimEvalIds.contains(evaluationModelResp.getEvaluationId())) {
                    return;
                }
                PrjDimSameModelVO prjDimSameModelVO = new PrjDimSameModelVO();
                prjDimSameModelVO.setEvalId(evaluationModelResp.getEvaluationId());
                prjDimSameModelVO.setEvalName(evaluationModelResp.getEvaluationName());
                resultData.add(prjDimSameModelVO);
            });
        }
    }

    private void setOtherDimSameModel(String orgId, List<PrjDimSameModelVO> otherDimSameModelList,
            List<PrjDimSameModelVO> resultData, List<String> existedEvalIds) {
        if (CollectionUtils.isNotEmpty(otherDimSameModelList)) {
            List<String> evalIds = otherDimSameModelList.stream().map(PrjDimSameModelVO::getEvalId)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            // 获取测评信息
            EvaluationFacade evaluationFacade = new EvaluationFacade();
            evaluationFacade.setOrgId(orgId);
            evaluationFacade.setEvalIds(evalIds);
            CommonList<Evaluation4List> commonList = spevalAclService.searchEvalList(evaluationFacade);
            List<Evaluation4List> evaluation4Lists = commonList.getDatas();
            Map<String, Evaluation4List> sameEvalNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(commonList.getDatas())) {
                sameEvalNameMap = StreamUtil.list2map(evaluation4Lists, Evaluation4List::getId);
            }
            Map<String, Evaluation4List> finalSameEvalNameMap = sameEvalNameMap;
            otherDimSameModelList.forEach(otherDimSameModel -> {
                //去重，防止测评已返回
                if (existedEvalIds.contains(otherDimSameModel.getEvalId())) {
                    return;
                }
                PrjDimSameModelVO prjDimSameModelVO = new PrjDimSameModelVO();
                prjDimSameModelVO.setEvalId(otherDimSameModel.getEvalId());
                Evaluation4List evaluation4List = finalSameEvalNameMap.get(otherDimSameModel.getEvalId());
                prjDimSameModelVO.setEvalName(Objects.nonNull(evaluation4List) ? evaluation4List.getName() : "");
                prjDimSameModelVO.setDimensionConfigId(otherDimSameModel.getDimensionConfigId());
                prjDimSameModelVO.setModelId(otherDimSameModel.getModelId());
                resultData.add(prjDimSameModelVO);
            });
        }
    }

    /**
     * 绑定同模维度测评
     *
     * @param projectId 项目id
     * @param modelId   维度id
     * @param evalId    测评id
     */
    public void bindSameModelEval(String orgId, String projectId, String modelId, String evalId, String userId,
            String configId) {
        if (StringUtils.isBlank(evalId)) {
            return;
        }
        PrjDimSameModelPO prjDimSameModel = prjDimSameModelMapper.selectByPrjIdAndModelIdAndEvalIdAndDimConfId(orgId,
                projectId, modelId, evalId, configId);
        if (prjDimSameModel != null) {
            return;
        }
        prjDimSameModel = new PrjDimSameModelPO();
        prjDimSameModel.setId(ApiUtil.getUuid());
        prjDimSameModel.setOrgId(orgId);
        prjDimSameModel.setProjectId(projectId);
        prjDimSameModel.setDimensionConfigId(configId);
        prjDimSameModel.setModelId(modelId);
        prjDimSameModel.setEvalId(evalId);
        EntityUtil.setAuditFields(prjDimSameModel, userId);
        prjDimSameModelMapper.insertOrUpdateBatch(List.of(prjDimSameModel));
    }

    /**
     * 批量删除同一个配置方案下同模测评
     *
     * @param orgId     机构id
     * @param projectId 项目id
     * @param configId  配置方案id
     */
    public void deleteSameModelEval(String orgId, String projectId, String configId) {
        List<PrjDimSameModelPO> sames = prjDimSameModelMapper.selectByPrjIdAndDimConfId(orgId, projectId, configId);
        List<String> ids = sames.stream().map(PrjDimSameModelPO::getId).collect(Collectors.toList());
        prjDimSameModelMapper.deleteBatch(orgId, ids);
    }
}
