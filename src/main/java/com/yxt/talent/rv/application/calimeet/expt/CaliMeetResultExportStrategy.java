package com.yxt.talent.rv.application.calimeet.expt;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;

@Component
@RequiredArgsConstructor
public class CaliMeetResultExportStrategy extends AbstractExportStrategy {
    private static final String TASK_NAME = "apis.sptalentrv.meeting.user.export.file.name";
    private final I18nComponent i18nComponent;

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        fileName = fileName.replace("##", "");
        DynamicExcelExportContent prjResult = (DynamicExcelExportContent) data;
        String filePath = path + fileName;
        ExcelUtils.exportWithDynamicHeader(
                prjResult.getHeaders(), prjResult.getSheets(), prjResult.getData(), filePath);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String meetingName) {
        String[] split = meetingName.split("##");
        String fileNameStart = split[0];
        String fileNameEnd = split[1];
        String fileName = "【" + fileNameStart.replace("##", "") + "】结果导出" +
                          DateTimeUtil.dateToString(
                                  new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS) +
                          fileNameEnd;
        String taskName = String.format(i18nComponent.getI18nValue(TASK_NAME), fileNameStart);
        return buildDownInfo(userCache, fileName, taskName);
    }
}
