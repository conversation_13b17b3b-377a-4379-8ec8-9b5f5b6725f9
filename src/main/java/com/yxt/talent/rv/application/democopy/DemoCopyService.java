package com.yxt.talent.rv.application.democopy;

import com.yxt.ApplicationCommandService;
import com.yxt.o2ofacade.bean.project.DemoCopyIDResp;
import com.yxt.spevalfacade.bean.enums.DemoIdMapKeyEnum;
import com.yxt.spmodel.facade.bean.demo.OrgDemoIdMappingVO;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.service.remote.*;
import com.yxt.talentrvfacade.bean.EntityIdMap;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.yxt.common.util.StreamUtil.list2map;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_JQ_BASE_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_JQ_DETAIL_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_JQ_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_JQ_SETTING_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_MODEL_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_RESPONSIBILITY_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_RT_MODEL_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_SKILL_ID;
import static com.yxt.spsdfacade.constants.SdDemoConstants.SD_SKILL_LEVEL_MAP_ID;
import static com.yxt.sptalentapifacade.constants.SptalentConstants.TALENT_PLAN_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.O2O_TRAINING_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPEVAL_EVAL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPEVAL_FORM_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_LABEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_LABEL_VALUE_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_RULE_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_JQ_CATA_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_JQ_DIM_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_JQ_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_JQ_TPL_CATA_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_RT_MODEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_SKILL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_SKILL_LEVEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_SKILL_MODEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPTALENT_PLAN_ID;


@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class DemoCopyService {

    private final SptalentAclService sptalentAclService;
    private final DemoTableProvider demoTableProvider;
    private final AppProperties appProperties;
    private final SpmodelAclService spmodelAclService;
    private final O2oAclService o2oAclService;
    private final SpsdAclService spsdAclService;
    private final SpevalAclService spevalAclService;

    /**
     * 奇点机构复制第一步
     *
     * @param orgInit
     */
    public void preGenIdMap(OrgInit4Mq orgInit) {
        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
        runner.preSetIdMapGen();
        List<String> strings = runner.deleteEntitySQL(orgInit.getTargetOrgId());
        log.info("LOG21303:delete entity sql:{}", strings);
    }

    /**
     * 奇点机构复制第二步
     *
     * @param orgInit
     */
    public void demoCopy(OrgInit4Mq orgInit) {
        String sourceOrgId = orgInit.getSourceOrgId();
        String targetOrgId = orgInit.getTargetOrgId();

        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
//        runner.setSkipInsert(true);

        addO2oIdMap(targetOrgId, runner);
        //        runner.addPreSetIdMap(O2O_TRAINING_ID, new HashMap<>());

        addSptalentIdMap(sourceOrgId, targetOrgId, runner);
        //        runner.addPreSetIdMap(SPTALENT_PLAN_ID, new HashMap<>());

        addSpsdIdMap(sourceOrgId, targetOrgId, runner);
        //        runner.addPreSetIdMap(SPSD_JQ_ID, new HashMap());
        //        runner.addPreSetIdMap(SPSD_JQ_DIM_ID, new HashMap());
        //        runner.addPreSetIdMap(SPSD_SKILL_MODEL_ID, new HashMap());
        //        runner.addPreSetIdMap(SPSD_SKILL_ID, new HashMap());
        //        runner.addPreSetIdMap(SPSD_SKILL_LEVEL_ID, new HashMap());
        //        runner.addPreSetIdMap(SPSD_RT_MODEL_ID, new HashMap());
        //        runner.addPreSetIdMap(SPSD_JQ_CATA_ID, new HashMap());
        //        runner.addPreSetIdMap(SPSD_JQ_TPL_CATA_ID, new HashMap());

        addSpevalIdMap(sourceOrgId, targetOrgId, runner);
        //        runner.addPreSetIdMap(SPEVAL_EVAL_ID, new HashMap());
        //        runner.addPreSetIdMap(SPEVAL_FORM_ID, new HashMap());

        addSpmIdMap(targetOrgId, runner);
        //        runner.addPreSetIdMap(SPM_LABEL_ID, new HashMap<>());
        //        runner.addPreSetIdMap(SPM_INDICATOR_ID, new HashMap<>());
        //        runner.addPreSetIdMap(SPM_LABEL_VALUE_ID, new HashMap<>());
        //        runner.addPreSetIdMap(SPM_RULE_ID, new HashMap<>());

        //执行复制
        runner.copyRun();
    }

    private void addSpevalIdMap(String sourceOrgId, String targetOrgId, DemoCopyRunner runner) {
        Map<String, String> entityIdMapping =
                spevalAclService.getEntityIdMap(sourceOrgId, targetOrgId,
                        DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_ID.getIdMapKey());
        runner.addPreSetIdMap(SPEVAL_EVAL_ID, entityIdMapping);

        Map<String, String> formIdMapping =
                spevalAclService.getEntityIdMap(sourceOrgId, targetOrgId,
                        DemoIdMapKeyEnum.MAP_KEY_SPEVAL_FORM_ID.getIdMapKey());
        runner.addPreSetIdMap(SPEVAL_FORM_ID, formIdMapping);

        Map<String, String> settingIdMapping =
                spevalAclService.getEntityIdMap(sourceOrgId, targetOrgId,
                        DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_DIMENSION_SETTING_ID.getIdMapKey());
        runner.addPreSetIdMap(DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_DIMENSION_SETTING_ID.getIdMapKey(), settingIdMapping);
    }

    private void addSpsdIdMap(String sourceOrgId, String targetOrgId, DemoCopyRunner runner) {
        runner.addPreSetIdMap(
                SPSD_JQ_ID, spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_JQ_ID));
        runner.addPreSetIdMap(SPSD_JQ_DIM_ID, getJqDimMapping(sourceOrgId, targetOrgId));
        runner.addPreSetIdMap(SPSD_SKILL_MODEL_ID,
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_MODEL_ID));
        runner.addPreSetIdMap(SPSD_SKILL_ID,
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_SKILL_ID));
        runner.addPreSetIdMap(SPSD_SKILL_LEVEL_ID,
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_SKILL_LEVEL_MAP_ID));
        runner.addPreSetIdMap(SPSD_RT_MODEL_ID,
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_RT_MODEL_ID));
        runner.addPreSetIdMap(SPSD_JQ_CATA_ID,
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_JQ_BASE_ID));
        runner.addPreSetIdMap(SPSD_JQ_TPL_CATA_ID,
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_JQ_SETTING_ID));
    }

    @Nonnull
    private Map<String, String> getJqDimMapping(String sourceOrgId, String targetOrgId) {
        Map<String, String> jqDimIdMapping = new HashMap<>();
        // 避免返回null的时候putAll报错
        Map<String, String> demoOrgMapping =
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_JQ_DETAIL_ID);
        if (demoOrgMapping != null && !demoOrgMapping.isEmpty()) {
            jqDimIdMapping.putAll(demoOrgMapping);
        }
        Map<String, String> demoOrgMapping1 =
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_SKILL_ID);
        if (demoOrgMapping1 != null && !demoOrgMapping1.isEmpty()) {
            jqDimIdMapping.putAll(demoOrgMapping1);
        }
        Map<String, String> demoOrgMapping2 =
                spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SD_RESPONSIBILITY_ID);
        if (demoOrgMapping2 != null && !demoOrgMapping2.isEmpty()) {
            jqDimIdMapping.putAll(demoOrgMapping2);
        }
        return jqDimIdMapping;
    }

    private void addO2oIdMap(String targetOrgId, DemoCopyRunner runner) {
        // 0:查询项目 1:查询培训计划
        int queryType = 0;
        List<DemoCopyIDResp> demoCopyNewId = o2oAclService.getDemoCopyNewId(targetOrgId, queryType);
        Map<Long, Long> idMapping =
                list2map(demoCopyNewId, DemoCopyIDResp::getOldId, DemoCopyIDResp::getNewId);
        runner.addPreSetIdMap(O2O_TRAINING_ID, idMapping);
    }

    private void addSptalentIdMap(String sourceOrgId, String targetOrgId, DemoCopyRunner runner) {
        Map<String, String> planIdMapping =
                sptalentAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, TALENT_PLAN_ID);
        runner.addPreSetIdMap(SPTALENT_PLAN_ID, planIdMapping);
    }

    private void addSpmIdMap(String targetOrgId, DemoCopyRunner runner) {
        OrgDemoIdMappingVO orgDemoIdMappingVO =
                spmodelAclService.getOrgDemoIdMappingVO(targetOrgId);
        runner.addPreSetIdMap(SPM_LABEL_ID, orgDemoIdMappingVO.getLabelIdMap());
        runner.addPreSetIdMap(SPM_INDICATOR_ID, orgDemoIdMappingVO.getIndicatorIdMap());
        runner.addPreSetIdMap(SPM_LABEL_VALUE_ID, orgDemoIdMappingVO.getLabelValueIdMap());
        runner.addPreSetIdMap(SPM_RULE_ID, orgDemoIdMappingVO.getRuleIdMap());
    }

    @SuppressWarnings("rawtypes")
    public EntityIdMap getEntityIdMap(String sourceOrgId, String targetOrgId) {
        List<String> idMapKeys =
                Optional.ofNullable(appProperties.getIdMapKeys()).orElse(Collections.emptyList());

        OrgInit4Mq orgInit = new OrgInit4Mq();
        orgInit.setSourceOrgId(sourceOrgId);
        orgInit.setTargetOrgId(targetOrgId);
        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
        runner.preSetIdMapGen();

        Map<String, Map> idMap = new HashMap<>();
        idMapKeys.forEach(key -> {
            Map map = runner.queryPreSetIdMap(key);
            idMap.put(key, map);
        });
        return new EntityIdMap(idMap);
    }
}
