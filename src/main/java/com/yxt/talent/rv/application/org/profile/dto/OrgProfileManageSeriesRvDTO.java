package com.yxt.talent.rv.application.org.profile.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class OrgProfileManageSeriesRvDTO {

    @Schema(description = "序列名称")
    private String seriesName;

    @Schema(description = "排序")
    private Integer orderIndex;

    @Schema(description = "序列盘点结果")
    private List<OrgProfileBaseInfoDTO> seriesrvs;
}
