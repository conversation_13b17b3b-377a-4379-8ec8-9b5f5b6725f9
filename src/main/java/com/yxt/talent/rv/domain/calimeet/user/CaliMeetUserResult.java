package com.yxt.talent.rv.domain.calimeet.user;


import com.yxt.AuditDomainEntity;
import jakarta.annotation.Nonnull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盘点校准会用户结果表
 */
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class CaliMeetUserResult extends AuditDomainEntity<String, LocalDateTime> {

    // 机构id
    @Nonnull
    private String orgId;

    // 盘点项目id
    @Nonnull
    private String prjId;

    // 会议Id
    @Nonnull
    private String calMeetId;

    // 维度id
    private String dimId;

    // 用户id
    @Nonnull
    private String userId;

    // 原始结果（0-默认，1-低，2-中，3-高）
    private Integer originalLevel;

    // 原始分数
    private BigDecimal originalScore;

    // 校准结果（0-默认，1-低，2-中，3-高）
    private Integer calLevel;

    // 数据来源（0-初始化 1-校准）
    private Integer initType;

    // 校准分数
    private BigDecimal calScore;

}
