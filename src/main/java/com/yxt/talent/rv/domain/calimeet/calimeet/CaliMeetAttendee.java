package com.yxt.talent.rv.domain.calimeet.calimeet;


import com.yxt.AuditDomainEntity;
import jakarta.annotation.Nonnull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 校准会参与人员表,即负责校准的人,一般是管理员或者部门经理等角色
 */
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class CaliMeetAttendee extends AuditDomainEntity<String, LocalDateTime> {

    // 机构id
    @Nonnull
    private String orgId;

    // 会议id
    private String calMeetId;

    // 干系人id
    private String userId;

    // 干系人类型（1-组织者，2-参会人）
    private Integer userType;

    @Getter
    @RequiredArgsConstructor
    public enum UserTypeEnum {
        /*会议组织者*/
        ORGANIZER("会议组织者", 1),
        /*人才委员会人员*/
        TALENT_COMMITTEE("人才委员会人员", 2);

        private final String name;
        private final int code;
    }
}
