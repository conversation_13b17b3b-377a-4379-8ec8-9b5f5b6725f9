package com.yxt.talent.rv.domain.calimeet.user;

import com.yxt.AuditDomainEntity;
import jakarta.annotation.Nonnull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 盘点校准备注
 */
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class CaliMeetUserRemark extends AuditDomainEntity<String, LocalDateTime> {

    /**
     * 机构id
     */
    @Nonnull
    private String orgId;

    /**
     * 盘点项目id
     */
    @Nonnull
    private String prjId;

    /**
     * 校准会id
     */
    @Nonnull
    private String calMeetId;

    /**
     * 盘点校准会名称 *
     */
    private String catMeetName;

    /**
     * 被校准人id
     */
    @Nonnull
    private String userId;

    /**
     * 校准备注
     */
    private String remark;
}
