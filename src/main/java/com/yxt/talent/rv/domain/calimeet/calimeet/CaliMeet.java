package com.yxt.talent.rv.domain.calimeet.calimeet;

import com.yxt.AuditAggregateRoot;
import com.yxt.common.Constants;
import com.yxt.util.NonRemovableLinkedHashSet;
import jakarta.annotation.Nonnull;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Set;

/**
 * 校准会,主领域
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class CaliMeet extends AuditAggregateRoot<String, LocalDateTime> {

    // 机构id
    @Nonnull
    private String orgId;

    // 盘点项目id
    private String prjId;

    // 校准会状态（0-未开始，1-进行中，2-已结束）
    private Integer meetStatus;

    // 会议名称
    private String meetName;

    // 会议时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private LocalDateTime meetTime;

    // 会议记录
    private String meetMinutes;

    // 是否删除(0-否,1-是)
    private Integer meetRouteStatus;

    // 会议持续时间，用于绚星工作场会议 (单位分钟，最少60分钟，最长1440分钟（即1天）)
    private Integer duration;

    // 校准会会议号（11位号码）
    private String meetNo;

    // 校准会会议id
    private String meetId;

    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<CaliMeetAttendee> caliMeetAttendees = new NonRemovableLinkedHashSet<>();

    public void addCaliMeetAttendee(CaliMeetAttendee entity) {
        if (entity == null) {
            log.warn("LOG10625:orgId={}, prjId={}", orgId, prjId);
            return;
        }
        caliMeetAttendees.add(entity);
    }

    public void addCaliMeetAttendees(Collection<CaliMeetAttendee> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG10645:orgId={}, prjId={}", orgId, prjId);
            return;
        }
        caliMeetAttendees.addAll(entities);
    }

    /**
     * 校准会状态（0-未开始，1-进行中，2-已结束）
     */
    @Getter
    @RequiredArgsConstructor
    public enum CaliMeetStatusEnum {
        /*未开始*/
        DEFAULT("未开始", 0),
        /*进行中*/
        UNDERWAY("进行中", 1),
        /*已结束*/
        FINISHED("已结束", 2);

        private final String name;
        private final int code;
    }

}
