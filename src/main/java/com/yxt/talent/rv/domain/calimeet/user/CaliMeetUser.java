package com.yxt.talent.rv.domain.calimeet.user;


import com.yxt.AuditAggregateRoot;
import com.yxt.util.NonRemovableLinkedHashSet;
import jakarta.annotation.Nonnull;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Set;

/**
 * 被校准人,一般是普通员工
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class CaliMeetUser extends AuditAggregateRoot<String, LocalDateTime> {

    // 机构id
    @Nonnull
    private String orgId;

    // 盘点项目id
    private String prjId;

    // 会议Id
    private String calMeetId;

    // 用户id
    private String userId;

    // 发展建议
    private String suggestion;

    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<CaliMeetUserRemark> caliMeetUserRemarks = new NonRemovableLinkedHashSet<>();

    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<CaliMeetUserResult> caliMeetUserResults = new NonRemovableLinkedHashSet<>();


    public void addCaliMeetUserRemark(CaliMeetUserRemark entity) {
        if (entity == null) {
            log.warn("LOG11105:calMeetId={}, prjId={}", calMeetId, prjId);
            return;
        }
        caliMeetUserRemarks.add(entity);
    }

    public void addCaliMeetUserRemarks(Collection<CaliMeetUserRemark> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG10715:calMeetId={}, prjId={}", calMeetId, prjId);
            return;
        }
        caliMeetUserRemarks.addAll(entities);
    }

    public void removeCaliMeetUserRemark(CaliMeetUserRemark entity) {
        if (entity == null) {
            log.warn("LOG10705:calMeetId={}, prjId={}", calMeetId, prjId);
            return;
        }
        caliMeetUserRemarks.remove(entity);
    }

    public void addCaliMeetUserResult(CaliMeetUserResult entity) {
        if (entity == null) {
            log.warn("LOG10695:calMeetId={}, prjId={}", calMeetId, prjId);
            return;
        }
        caliMeetUserResults.add(entity);
    }

    public void addCaliMeetUserResults(Collection<CaliMeetUserResult> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG11295:calMeetId={}, prjId={}", calMeetId, prjId);
            return;
        }
        caliMeetUserResults.addAll(entities);
    }

    public void removeCaliMeetUserResult(CaliMeetUserResult entity) {
        if (entity == null) {
            log.warn("LOG10685:calMeetId={}, prjId={}", calMeetId, prjId);
            return;
        }
        caliMeetUserResults.remove(entity);
    }

}
