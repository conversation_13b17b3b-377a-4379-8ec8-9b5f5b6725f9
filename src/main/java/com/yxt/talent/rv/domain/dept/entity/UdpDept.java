package com.yxt.talent.rv.domain.dept.entity;

import com.yxt.AuditDomainEntity;
import jakarta.annotation.Nonnull;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class UdpDept extends AuditDomainEntity<String, LocalDateTime> {

    /**
     * 机构ID
     */
    @Nonnull
    private String orgId;

    /**
     * 父部门id
     */
    private String parentId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门编号(内部给每个部门的编号)-不超过20位
     */
    private String code;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门id全路径
     */
    private String idFullPath;

    /**
     * 部门路径,如000001.000002.000003或000004.000005)
     */
    private String routingPath;

    /**
     * 第三方系统ID
     */
    private String thirdId;

    /**
     * 排序顺序
     */
    private Integer orderIndex;

    /**
     * 是否包含虚拟部门(0-否,1-是)
     */
    private Integer hasVirtualDept;

    /**
     * 第三方系统同步时间
     */
    private LocalDateTime thirdSyncTime;

    /**
     * 1.0系统迁移更新时间
     */
    private LocalDateTime mgtSyncTime;

}
