package com.yxt.talent.rv.domain.dmp.entity.user;

import com.yxt.AuditDomainEntity;
import jakarta.annotation.Nonnull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 人岗动态匹配项目-个人胜任结果
 */
@Getter
@Setter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId", "dmpId", "userId"})
public class DmpUserResult extends AuditDomainEntity<String, LocalDateTime> {

    /**
     * 机构id
     */
    @Nonnull
    private String orgId;

    /**
     * 人岗动态匹配项目id
     */
    @Nonnull
    private String dmpId;

    /**
     * 学员id
     */
    @Nonnull
    private String userId;

    /**
     * 匹配的层级id, 指向rv_dmp_rule_layer.id
     */
    private String layerId;

    /**
     * 匹配率or分值
     */
    private BigDecimal score;

    /**
     * 是否胜任, 0-不胜任（低于胜任分层） 1-胜任（位于胜任及以上的分层）
     */
    private Integer competent;

    /**
     * 项目完成状态：0-未完成 1-已完成 2-不在任何任务中
     */
    private Integer complete;

    /**
     * 匹配的层级名称
     */
    private String layerName;
}
