package com.yxt.talent.rv.domain.prj.event;

import com.yxt.event.message.MessageEvent;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper = true)
public class AbstractPrjCalcEvent extends MessageEvent {

    @Nonnull
    private String orgId;

    @Nonnull
    private String prjId;

    // 操作计算的员工
    private String operator;

    // 需要计算的员工id
    private List<String> userIds;

    // 每个盘点项目下参与计算的员工一共拆分成了多少份，方便归拢验证是否全部都计算完成
    private int queueCount;

    private PrjDimConfPO prjDimConf;

}
