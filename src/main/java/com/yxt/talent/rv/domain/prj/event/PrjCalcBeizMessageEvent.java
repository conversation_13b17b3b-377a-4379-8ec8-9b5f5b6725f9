package com.yxt.talent.rv.domain.prj.event;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper = true)
public class PrjCalcBeizMessageEvent extends AbstractPrjCalcEvent {

    public static PrjCalcBeizMessageEvent copyOf(PrjCalcEvalMessageEvent source) {
        return Assembler.INSTANCE.convert(source);
    }

    @Mapper
    interface Assembler {
        Assembler INSTANCE = Mappers.getMapper(Assembler.class);

        PrjCalcBeizMessageEvent convert(PrjCalcEvalMessageEvent source);
    }

}
