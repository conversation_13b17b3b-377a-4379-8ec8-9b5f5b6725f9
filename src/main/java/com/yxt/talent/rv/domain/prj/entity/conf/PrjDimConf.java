package com.yxt.talent.rv.domain.prj.entity.conf;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.AuditDomainEntity;
import com.yxt.EntityLoadConfig;
import com.yxt.enums.DeleteEnum;
import com.yxt.talent.rv.domain.prj.entity.conf.rule.PrjDimRule;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.util.NonRemovableLinkedHashSet;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 维度配置关系表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId", "prjId", "dimId", "modelId"})
public class PrjDimConf extends AuditDomainEntity<String, LocalDateTime> {

    public static final String CK_PRJ_DIM_IMPORT_TYPE = "sprv:ck:prj:dim:impt:%s:%s:%s";

    // 机构id
    @Nonnull
    private String orgId;

    // 项目id
    private String prjId;

    // 维度id
    private String dimId;

    // 维度名称
    private String dimName;

    // 维度状态（0-未配置，1-已配置）
    private Integer dimStatus;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient DimStatus dimStatusEnum;

    public void setDimStatus(Integer dimStatus) {
        this.dimStatus = dimStatus;
        this.dimStatusEnum = DimStatus.byCode(this.dimStatus);
    }

    // 模型id
    private String modelId;

    // 废弃，需查询rv_dimension_config_tool表
    private String toolId;

    // 盘点工具类型（0-未配置，1-绩效，2-测评，3-盘点数据导入,4：倍智测评）
    private Integer toolType;

    // 排序，默认从0开始
    private Integer orderIndex;

    /* 盘点维度配置工具表 */
    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<PrjDimConfTool> prjDimConfTools = new NonRemovableLinkedHashSet<>();

    /* 同模测评，当toolType=2或4时此字段才可以设置同模测评 */
    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<PrjDimSameModel> prjDimSameModels = new NonRemovableLinkedHashSet<>();

    /* 维度规则 */
    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<PrjDimRule> prjDimRules = new NonRemovableLinkedHashSet<>();

    /* 学员的维度结果导入记录 */
    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<PrjImptType> prjImptTypes = new NonRemovableLinkedHashSet<>();

    /**
     * 删除所有导入记录
     */
    public void deletePrjImptTypes() {
        EntityUtil.delete(prjImptTypes);
    }

    /**
     * 获取最后一个维度数据导入的类型
     *
     * @return
     */
    public Optional<PrjImptType> findLastPrjImptType() {
        return this.getPrjImptTypes()
                .stream()
                .filter(e -> DeleteEnum.isNotDeleted(e.getDeleted()))
                .findFirst();
    }

    public void addPrjDimRule(PrjDimRule entity) {
        if (entity == null) {
            log.warn("LOG11845:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjDimRules.add(entity);
    }

    public void addPrjDimRules(Collection<PrjDimRule> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG11855:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjDimRules.addAll(entities);
    }

    public void addPrjDimConfTool(PrjDimConfTool entity) {
        if (entity == null) {
            log.warn("LOG10505:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjDimConfTools.add(entity);
    }

    public void addPrjDimConfTools(Collection<PrjDimConfTool> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG10535:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjDimConfTools.addAll(entities);
    }


    public void addPrjDimSameModel(PrjDimSameModel entity) {
        if (entity == null) {
            log.warn("LOG11765:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjDimSameModels.add(entity);
    }

    public void addPrjDimSameModels(Collection<PrjDimSameModel> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG11775:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjDimSameModels.addAll(entities);
    }

    public void addPrjImptType(PrjImptType entity) {
        if (entity == null) {
            log.warn("LOG12285:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjImptTypes.add(entity);
    }

    public void addPrjImptTypes(Collection<PrjImptType> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG12275:prjId={}, dimId={}", this.prjId, this.dimId);
            return;
        }
        prjImptTypes.addAll(entities);
    }

    public String buildPrjDimImptTypeCK() {
        return String.format(PrjDimConf.CK_PRJ_DIM_IMPORT_TYPE, orgId, prjId, dimId);
    }

    @Builder
    public record LoadConfig(
            boolean loadPrjImptType
    ) implements EntityLoadConfig {
        public static final LoadConfig WITH_ALL = new LoadConfig(true);
    }

    @Getter
    @RequiredArgsConstructor
    public enum DimStatus {
        /**
         * 维度状态
         */
        UNKNOWN(-1, "未知"),
        NOT_CONFIGURE(0, "未配置"),
        CONFIGURED(1, "已配置");

        private final int code;
        private final String desc;

        public static DimStatus byCode(@Nullable Integer dimStatus) {
            for (DimStatus status : DimStatus.values()) {
                if (Objects.equals(status.code, dimStatus)) {
                    return status;
                }
            }
            return UNKNOWN;
        }
    }
}
