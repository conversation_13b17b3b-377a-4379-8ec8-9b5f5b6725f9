package com.yxt.talent.rv.domain.org.entity;

import com.yxt.AuditDomainEntity;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UdpOrg extends AuditDomainEntity<String, LocalDateTime> {

    /**
     * 机构名称
     */
    private String name;

    /**
     * 机构代码
     */
    private String code;

    /**
     * 平台类型: 0-客户试用, 1-签约开通, 2-员工自用, 3-集中交付
     */
    private Integer orgType;

    /**
     * 机构logo
     */
    private String logoUrl;

    /**
     * 机构简介
     */
    private String description;

    /**
     * 域名
     */
    private String domain;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 开始日期
     */
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    private LocalDateTime endDate;

    /**
     * 是否正式机构
     */
    private Integer officialOrg;

    /**
     * 是否模板机构
     */
    private Integer templatedOrg;

    /**
     * 是否预留机构
     */
    private Integer reservedOrg;

    /**
     * 是否是销售机构
     */
    private Integer salesOrg;

    /**
     * 是否是销售机构模板
     */
    private Integer salesOrgTemplate;

    /**
     * 模板机构id
     */
    private String salesOrgTemplateId;

    /**
     * 版本号: 10-工具版,100-基础版,101-渠道基础版,200-标准版,250-过渡版,300-中欧标准版,350-渠道标准版,400-专业版
     */
    private String editionCode;

    /**
     * 渠道来源: 99-关联开通,100-微信isv,101-中欧渠道,102-内容BU,103-线下渠道,104-钉钉isv,105-领带金融,106-飞书isv
     */
    private String sourceCode;

    /**
     * 机构状态: 0-禁用;1-正常;2-停用维护中
     */
    private Integer orgStatus;

    /**
     * 机构站点ICON
     */
    private String iconUrl;

    /**
     * 机构名称国际化Code
     */
    private String nameI18n;

    /**
     * 机构站点名称国际化Code
     */
    private String siteNameI18n;

    /**
     * 机构名称(CSM)
     */
    private String orgName;
}
