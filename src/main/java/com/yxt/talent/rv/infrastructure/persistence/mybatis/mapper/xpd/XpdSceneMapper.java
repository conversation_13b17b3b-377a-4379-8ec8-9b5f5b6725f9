package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene;

import java.util.List;

import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseStatus;

@Mapper
public interface XpdSceneMapper {
    int deleteByPrimaryKey(String id);

    int insert(XpdScene record);

    int insertOrUpdate(XpdScene record);

    int insertOrUpdateSelective(XpdScene record);

    int insertSelective(XpdScene record);

    XpdScene selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(XpdScene record);

    int updateByPrimaryKey(XpdScene record);

    int updateBatch(@Param("list") List<XpdScene> list);

    int batchInsert(@Param("list") List<XpdScene> list);


    IPage<XpdScene> queryPage(@Param("page") IPage<XpdScene> page, @Param("orgId") String orgId);

    XpdScene queryById(@Param("orgId") String orgId,@Param("id") String sceneId);

    XpdScene queryBySceneName(@Param("orgId") String orgId, @Param("sceneName") String sceneName);
}