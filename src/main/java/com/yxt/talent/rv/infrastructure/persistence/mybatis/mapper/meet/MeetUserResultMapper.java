package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.controller.manage.meet.query.MeetResultQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.XpdUserGridResultVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetUserResultPO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Mapper
@Repository
public interface MeetUserResultMapper {

    int insertList(@Nonnull @Param("list") List<MeetUserResultPO> list);

    int updateById(@Nonnull @Param("updated") MeetUserResultPO updated);

    @Nonnull
    List<MeetUserResultPO> listByMeetIdAndPrjIdAndDimIdInAndUserIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetId") String meetId,
            @Param("prjId") String prjId, @Param("dimIds") Collection<String> dimIds,
            @Param("userIds") Collection<String> userIds);

    @Nonnull
    List<MeetUserResultPO> listByOrgIdAndMeetIdAndUserIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userIdCollection") Collection<String> userIdCollection);

    @Nonnull
    List<MeetUserResultPO> listByOrgIdAndMeetIdAndUserIdAndDimIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Nonnull @Param("userId") String userId,
            @Param("dimensionIdCollection") Collection<String> dimensionIdCollection);

    @Nonnull
    IPage<XpdUserGridResultVO> paging(
            @Nonnull @Param("orgId") String orgId, @Param("xValue") int xValue,
            @Param("yValue") int yValue, @Nonnull @Param("search") MeetResultQuery search,
            @Param("page") IPage<XpdUserGridResultVO> page);

    int deleteByOrgIdAndMeetIdAndUserIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userIdCollection") Collection<String> userIdCollection);

    @Nonnull
    List<MeetUserResultPO> listByOrgId(@Nonnull @Param("orgId") String orgId);

    void insertOrUpdate(MeetUserResultPO entity);

    Long countByOrgIdAndProjectIdAndMeetingId(
            @Nonnull @Param("orgId") String orgId,
            @Param("projectId") String projectId, @Param("meetingId") String meetingId);
}
