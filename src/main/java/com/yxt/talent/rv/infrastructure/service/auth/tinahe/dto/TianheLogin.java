package com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "登录入参")
public class TianheLogin {

    @Schema(description = "SSO免登-临时授权码")
    @NotBlank(message = "免登授权码不能为空")
    private String code;

}
