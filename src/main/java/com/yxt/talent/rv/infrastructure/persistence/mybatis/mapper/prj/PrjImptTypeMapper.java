package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjImptTypePO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface PrjImptTypeMapper extends CommonMapper<PrjImptTypePO> {

    long batchInsertOrUpdate(@Param("list") List<PrjImptTypePO> list);

    default void insertOrUpdateBatch(@Nonnull Collection<PrjImptTypePO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    void deleteBatch(
            @Param("orgId") String orgId, @Param("removeIds") Collection<String> removeIds);

    Optional<PrjImptTypePO> selectByPrjIdAndDimId(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("dimId") String dimId);

    Collection<PrjImptTypePO> selectByOrgId(@Param("orgId") String orgId);

    List<PrjImptTypePO> selectAllByOrgId(@Param("orgId") String orgId);
}