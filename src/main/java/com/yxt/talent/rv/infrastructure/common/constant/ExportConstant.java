package com.yxt.talent.rv.infrastructure.common.constant;

import lombok.experimental.UtilityClass;

/**
 * @Description 导出国际化使用
 *
 * <AUTHOR>
 * @Date 2024/7/24 10:21
 **/

@UtilityClass
public class ExportConstant {

    /********************************  盘点校准人员导入  ***********************************/

    // 账号为空
    public static final String RV_CALI_IMPORT_USUERNAME_EMPTY = "apis.sptalentrv.cali.username.empty";

    // 账号不存在
    public static final String RV_CALI_IMPORT_USUERNAME_NOT_EXIST = "apis.sptalentrv.cali.username.not.exist";

    // 人员不在盘点项目中
    public static final String RV_CALI_IMPORT_USUER_NOT_IN_RV = "apis.sptalentrv.cali.user.not.in.rv";

    public static final String RV_CALI_IMPORT_ERROR_FILE = "apis.sptalentrv.cali.user.error.file";

    // 员工账号
    public static final String RV_USERNAME = "apis.sptalentrv.cali.username";

    // 员工姓名
    public static final String RV_FULLNAME = "apis.sptalentrv.cali.fullname";

    // sheet名称
    public static final String RV_CALI_EXCEL_SHEET_NAME ="apis.sptalentrv.cali.excel.sheet.name";

    public static final String RV_CALI_EXCEL_ERR_COL_NAME ="apis.sptalentrv.cali.errorMsg";

    // 用于判断当前使用的什么语言
    public static final String RV_CALI_EXCEL_LANGUAGE ="apis.sptalentrv.cali.excel.language";
}
