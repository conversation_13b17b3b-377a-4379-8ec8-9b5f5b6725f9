package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.calimeet;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import com.yxt.criteria.Result;
import com.yxt.event.EventPublisher;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.calimeet.event.CaliMeetMinutesTimeWaitTaskEvent;
import com.yxt.task.Task;
import com.yxt.task.TaskHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 校准会定时任务执行 1. 每小时扫描校准会表格，将当前小时内开始的校准会放入到缓存中 -- CalibrationMeetingHoursTimeWaitJobHandler 2.
 * 每分钟扫描缓存，将当前分钟开始的校准会状态更改为 进行中； 并且发送校准会开始的消息 -- CalibrationMeetingMinuteTimeWaitJobHandler
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CaliMeetMinutesTimeWaitJob implements TaskHandler<Task<String>> {

    private final EventPublisher eventPublisher;

    @XxlJob(value = "calibrationMeetingMinutesTimeWaitJobHandler")
    public ReturnT<String> execute(String param) {
        Result<Void> taskResult = this.doHandle(Task.of(param));
        return taskResult.isSuccess() ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

    @Override
    public Result<Void> handle(Task<String> task) {
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        eventPublisher.publish(new CaliMeetMinutesTimeWaitTaskEvent(shardingVo, task));
        return Result.success();
    }
}
