package com.yxt.talent.rv.infrastructure.repository.common;

import com.yxt.EmptyLoadConfig;
import com.yxt.talent.rv.domain.common.Category;
import com.yxt.talent.rv.domain.common.CategoryDomainRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.CategoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.CategoryPO;
import com.yxt.talent.rv.infrastructure.repository.common.assembler.CategoryAssembler;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class CategoryDomainRepoImpl implements CategoryDomainRepo {

    private final CategoryMapper categoryMapper;
    private final CategoryAssembler categoryAssembler;

    @Override
    public Optional<Category> load(
            @NonNull String orgId, @NonNull String entityId, @NonNull EmptyLoadConfig loadConfig) {
        return this.load(orgId, entityId);
    }

    @Override
    public Optional<Category> load(
            @NonNull String orgId, @NonNull String entityId) {
        CategoryPO categoryPo = categoryMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(categoryPo).map(categoryAssembler::toCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(@NonNull Category entity) {
        CategoryPO categoryPo = categoryAssembler.toCategoryPo(entity);
        Optional.ofNullable(categoryPo).ifPresent(categoryMapper::insertOrUpdate);
    }

    /**
     * 物理删除
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@NonNull Category entity) {
        String orgId = entity.getOrgId();
        categoryMapper.deleteById(orgId, entity.getId());
    }
}
