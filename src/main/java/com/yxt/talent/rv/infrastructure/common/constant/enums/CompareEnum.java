package com.yxt.talent.rv.infrastructure.common.constant.enums;

import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

@Getter
@RequiredArgsConstructor
public enum CompareEnum {
    /**
     * 符号枚举
     */
    EQUAL(1, "等于"),
    NOT_EQUAL(2, "不等于"),
    GREATER_THAN(3, "大于"),
    LESS_THAN(4, "小于"),
    GREATER_THAN_OR_EQUAL(5, "大于等于"),
    LESS_THAN_OR_EQUAL(6, "小于等于"),
    CONTAINS_ANY(7, "包含其一"),
    NOT_CONTAINS(8, "不包含"),
    ALL_CONTAINS(9, "都包含"),
    TEXT_CONTAINS(10, "文本包含"),
    NOT_TEXT_MATCHES(11, "文本不包含");

    private final int code;
    private final String description;

    @Nullable
    public static CompareEnum getByCode(Integer code) {
        for (CompareEnum compareEnum : CompareEnum.values()) {
            if (Objects.equals(compareEnum.getCode(), code)) {
                return compareEnum;
            }
        }
        return null;
    }

    // 是否是否定运算符
    public boolean isNegative() {
        return this == NOT_EQUAL || this == NOT_CONTAINS || this == NOT_TEXT_MATCHES;
    }

    public boolean isNotContains() {
        return this == NOT_CONTAINS;
    }

    public boolean isAllContains() {
        return this == ALL_CONTAINS;
    }
}
