package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityPerfConfMapper extends CommonMapper<ActivityPerfConfPO> {
    int insert(ActivityPerfConfPO record);

    int insertOrUpdate(ActivityPerfConfPO record);

    ActivityPerfConfPO selectByPrimaryKey(String id);

    int insertList(@Param("list")List<ActivityPerfConfPO> list);

    List<ActivityPerfConfPO> selectByActivityId(@Param("orgId") String orgId,@Param("actvPerfId") String actvPerfId);

    void deleteByActivityId(@Param("orgId") String orgId, @Param("actvPerfId") String perfActivityId);
}