package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface XpdRuleMapper extends CommonMapper<XpdRulePO> {
    int insert(XpdRulePO record);

    int insertOrUpdate(XpdRulePO record);

    XpdRulePO selectByPrimaryKey(String id);

    /**
     * 过滤删除数据
     *
     * @param id 规则ID
     * @return one
     */
    XpdRulePO selectById(String id);

    XpdRulePO getByXpdId(
            @Param("orgId") String orgId,
            @Param("xpdId") String xpdId);

    void deleteByXpdId(@Param("orgId") String orgId,
                        @Param("userId") String userId,
                       @Param("xpdId") String xpdId);

    @Update("update rv_xpd_rule set rule_threshold = #{ruleThreshold},threshold_invalid = #{thresholdInvalid} where id = #{id}")
    void updateRuleThreshold(@Param("id") String id, @Param("ruleThreshold") String ruleThreshold, @Param("thresholdInvalid")int thresholdInvalid);
}