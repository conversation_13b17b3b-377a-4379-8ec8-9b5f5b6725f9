package com.yxt.talent.rv.infrastructure.service.audit.prj;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.bean.EntityChange;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserAddResultVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class PrjUserAddAuditLogStrategy implements AuditLogStrategy {
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final PrjMapper prjMapper;


    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        log.info("LOG64130:{}", BeanHelper.bean2Json(argsNames, ALWAYS));
        if (args == null || args.length == 0) {
            log.error("LOG63790:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        String projectId = (String) args[1];
        PrjUserAddResultVO prjUser = (PrjUserAddResultVO) result;
        List<String> userIds = prjUser.getSuccessUserIds(); // 加人成功的人（收费测评额度不足情况）
        if (userIds == null) {
            return;
        }

        PrjPO prj = prjMapper.selectByOrgIdAndId(auditLog.getOrgId(), projectId);
        log.debug(
                "LOG64160:cls={}, 业务Id={}, orgId={}", this.getClass().getName(), projectId,
                auditLog.getOrgId());
        String bizName = projectId; // 极端情况，从库无数据，就用业务Id做名字
        if (prj != null) {
            bizName = prj.getProjectName();
        }

        // 组装日志
        List<EntityChange> list = getEntityChangeList(userIds, auditLog.getOrgId());
        AuditDetail ad = getCreateAuditDetail(AuditLogHelper.Module.PROJECT.getCode());
        ad.setEntityId(projectId);
        ad.setEntityName(AuditLogHelper.Module.PROJECT.getName() + "-" + bizName + "-添加人员");
        ad.setChanges(list);
        auditLog.setDetails(Collections.singletonList(ad));
        AuditLogContext.asyncCommit(auditLog);
    }

    private List<EntityChange> getEntityChangeList(List<String> userIds, String orgId) {
        List<EntityChange> result = new ArrayList<>();
        result.add(
                new EntityChange().setFieldName("盘点人员")
                        .setNewValue(getUserString(orgId, userIds)));
        return result;
    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
                .append("（")
                .append(a.getUsername())
                .append("）")
                .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }
}
