package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.yxt.AuditAggregateRoot;
import com.yxt.AuditDomainEntity;
import com.yxt.DomainEntity;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.ReflectionUtil;
import com.yxt.idworker.YxtIdWorker;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.GenericTypeResolver;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

import static org.springframework.core.GenericTypeResolver.resolveTypeArguments;
import static org.springframework.util.ReflectionUtils.findField;
import static org.springframework.util.ReflectionUtils.getField;
import static org.springframework.util.ReflectionUtils.makeAccessible;
import static org.springframework.util.ReflectionUtils.setField;

/**
 * 设置审计字段,包括创建人,创建时间,更新人,更新时间等
 */
@Slf4j
@UtilityClass
public class EntityUtil {
    public static final String FIELD_VERSION = "version";
    public static final String FIELD_ID = "id";
    private static final String METHOD_UPDATE_USER_ID = "setUpdateUserId";
    private static final String METHOD_UPDATE_USER_NAME = "setUpdateFullname";
    private static final String METHOD_UPDATE_DATE = "setUpdateTime";
    private static final String METHOD_CREATE_USER_ID = "setCreateUserId";
    private static final String METHOD_CREATE_DATE = "setCreateTime";
    private static final String METHOD_CREATE_USER_NAME = "setCreateFullname";
    private static final String FIELD_DELETED = "deleted";

    /**
     * 逻辑删除: 批量设置删除标记和更新人更新时间
     */
    public static <T> Optional<Collection<T>> delete(@Nullable Collection<T> destColl) {
        if (CollectionUtils.isEmpty(destColl)) {
            return Optional.ofNullable(destColl);
        }
        destColl.forEach(EntityUtil::delete);
        return Optional.of(destColl);
    }

    /**
     * 逻辑删除: 设置删除标记和更新人更新时间
     */
    public static <T> Optional<T> delete(@Nullable T dest) {
        if (dest == null) {
            return Optional.empty();
        }
        Field field = ReflectionUtil.getAccessibleField(dest, FIELD_DELETED);
        if (field != null && field.getType().equals(Integer.class)) {
            ReflectionUtil.invokeSetter(dest, FIELD_DELETED, 1);
        }
        setUpdate(dest);
        return Optional.of(dest);
    }

    /**
     * 逻辑删除: 设置删除标记和更新人更新时间
     */
    public static <T> Optional<T> delete(@Nullable T dest, String operator) {
        if (dest == null) {
            return Optional.empty();
        }
        Field field = ReflectionUtil.getAccessibleField(dest, FIELD_DELETED);
        if (field != null && field.getType().equals(Integer.class)) {
            ReflectionUtil.invokeSetter(dest, FIELD_DELETED, 1);
        }
        setUpdate(dest, operator);
        return Optional.of(dest);
    }

    @jakarta.annotation.Nonnull
    private static Operator getOperator() {
        return extractOperatorFromUserCache().orElse(Operator.UNKNOWN);
    }

    private static Optional<Operator> extractOperatorFromUserCache() {
        AuthService authService = SpringContextHolder.getBean("authService", AuthService.class);
        return Optional.ofNullable(authService.getUserCacheBasic())
            .filter(it -> StringUtils.isNotBlank(it.getUserId()))
            .map(Operator::new)
            .or(() -> Optional.ofNullable(authService.getUserCacheDetail())
                .filter(it -> StringUtils.isNotBlank(it.getUserId()))
                .map(Operator::new));
    }

    /**
     * 设置一些固定常用字段值，createUserId/updateUserId/createTime/updateTime/version/deleted
     *
     * @param dest
     */
    public static void setAuditFields(@Nullable Object dest) {
        if (dest == null) {
            return;
        }

        Operator operator = getOperator();
        setAuditFields(dest, operator.getUserId(), operator.getFullName());
    }

    /**
     * 设置一些固定常用字段值，createUserId/updateUserId/createTime/updateTime/version/deleted
     *
     * @param dest
     * @param userId
     */
    public static void setAuditFields(@Nullable Object dest, String userId) {
        if (dest == null) {
            return;
        }
        setAuditFields(dest, userId, null);
    }

    /**
     * 设置一些固定常用字段值，createUserId/updateUserId/createTime/updateTime/version/deleted
     *
     * @param dest
     */
    public static void setAuditFields(@Nullable Object dest, UserCacheDetail src) {
        if (dest == null) {
            return;
        }
        setAuditFields(dest, src.getUserId(), src.getFullname());
    }

    /**
     * 设置一些固定常用字段值，createUserId/updateUserId/createTime/updateTime/version/deleted
     *
     * @param dest
     */
    public static void setAuditFields(@Nullable Object dest, UserCacheBasic src) {
        if (dest == null) {
            return;
        }
        setAuditFields(dest, src.getUserId(), null);
    }

    public static void setUpdate(@Nullable Object dest) {
        if (dest == null) {
            return;
        }
        Operator operator = getOperator();
        setUpdate(dest, operator.getUserId(), operator.getFullName());
    }

    public static void setUpdate(@Nullable Object dest, @NonNull UserCacheDetail src) {
        if (dest == null) {
            return;
        }
        setUpdate(dest, src.getUserId(), src.getFullname());
    }

    public static void setUpdate(@Nullable Object dest, String operator) {
        if (dest == null) {
            return;
        }
        setUpdate(dest, operator, null);
    }

    public static void setUpdate(@Nullable Object dest, @NonNull UserCacheBasic src) {
        if (dest == null) {
            return;
        }
        setUpdate(dest, src.getUserId(), null);
    }

    public static void setAuditFields(@Nullable Object dest, String userId, @Nullable String fullName) {
        if (dest == null) {
            return;
        }
        // 设置主键属性
        Class<?> aClass = dest.getClass();
        Field field = findField(aClass, FIELD_ID);
        if (field != null) {
            makeAccessible(field);
            Class<?> inferenceFieldClazz = inferenceFieldClazz(field, aClass);
            if (inferenceFieldClazz.equals(String.class)) {
                Object pkValue = getField(field, dest);
                if (pkValue == null || "".equals(pkValue)) {
                    setDefaultValues(dest, field, ApiUtil.getUuid());
                    setField(field, dest, ApiUtil.getUuid());
                }
            } else if (inferenceFieldClazz.equals(Long.class)) {
                Object pkValue = getField(field, dest);
                if (pkValue == null || Objects.equals(pkValue, 0L)) {
                    setField(field, dest, YxtIdWorker.getId());
                }
            }
        }
        // 设置version 默认值
        Field versionField = findField(aClass, FIELD_VERSION);
        if (versionField != null) {
            makeAccessible(versionField);
            Class<?> inferenceFieldClazz = inferenceFieldClazz(versionField, aClass);
            if (Integer.class.equals(inferenceFieldClazz)) {
                setField(versionField, dest, 1);
            } else if (Long.class.equals(inferenceFieldClazz)) {
                setField(versionField, dest, 1L);
            } else if (AtomicLong.class.equals(inferenceFieldClazz)) {
                setField(versionField, dest, (byte) 1);
            }
        }
        // 设置deleted 默认值
        Field deletedField = findField(aClass, FIELD_DELETED);
        if (deletedField != null) {
            makeAccessible(deletedField);
            if (deletedField.getType().equals(Integer.class)) {
                setField(deletedField, dest, 0);
            } else if (deletedField.getType().equals(Boolean.class)) {
                setField(deletedField, dest, false);
            } else if (deletedField.getType().equals(Byte.class)) {
                setField(deletedField, dest, (byte) 0);
            }
        }
        invokeMethods(dest, userId, fullName, METHOD_CREATE_USER_ID, METHOD_CREATE_USER_NAME,
                METHOD_CREATE_DATE, METHOD_UPDATE_USER_ID, METHOD_UPDATE_USER_NAME,
                METHOD_UPDATE_DATE);
    }

    /**
     * 推断字段类型
     *
     * @param field
     * @param aClass
     * @return
     */
    @Nonnull
    private static Class<?> inferenceFieldClazz(Field field, Class<?> aClass) {
        Class<?> inferenceFieldClazz = field.getType();
        if (inferenceFieldClazz.equals(Serializable.class)) {
            inferenceFieldClazz =
                    GenericTypeResolver.resolveTypeArgument(aClass, DomainEntity.class);
            if (inferenceFieldClazz == null) {
                throw new IllegalStateException("无法获取泛型类型");
            }
        }
        return inferenceFieldClazz;
    }

    private static <T> void setDefaultValues(T entity, Field field, Object value) {
        setField(field, entity, value);
    }

    private static void setUpdate(Object dest, String userId, @Nullable String fullName) {
        invokeMethods(dest, userId, fullName, METHOD_UPDATE_USER_ID, METHOD_UPDATE_USER_NAME,
                METHOD_UPDATE_DATE);
    }

    private static void invokeMethods(
            Object dest, String userId, @Nullable String fullName, String... methodNames) {
        Method[] methods = dest.getClass().getMethods();
        try {
            for (Method method : methods) {
                for (String methodName : methodNames) {
                    if (method.getName().equals(methodName)) {
                        invokeMethod(dest, userId, fullName, method);
                    }
                }
            }
        } catch (Exception e) {
            log.error("LOG03920:setCreateAndUpdate4OpUser", e);
        }
    }

    private static void invokeMethod(
            Object dest, String userId, @Nullable String fullName, Method method) throws
            IllegalAccessException, InvocationTargetException {
        makeAccessible(method);
        switch (method.getName()) {
            case METHOD_CREATE_USER_ID, METHOD_UPDATE_USER_ID:
                method.invoke(dest, userId);
                break;
            case METHOD_CREATE_USER_NAME, METHOD_UPDATE_USER_NAME:
                method.invoke(dest, fullName);
                break;
            case METHOD_CREATE_DATE, METHOD_UPDATE_DATE:
                Class<?> aClass = dest.getClass();
                Class<?> supperClass = aClass.getSuperclass();
                if (AuditAggregateRoot.class.isAssignableFrom(aClass)) {
                    supperClass = AuditAggregateRoot.class;
                } else if (AuditDomainEntity.class.isAssignableFrom(aClass)) {
                    supperClass = AuditDomainEntity.class;
                }
                Class<?>[] classes = resolveTypeArguments(aClass, supperClass);
                Class<?> inferenceDateType = method.getParameterTypes()[0];
                if (classes != null) {
                    if (classes.length != 2) {
                        throw new IllegalStateException("无法获取审计时间的泛型类型");
                    }
                    inferenceDateType = classes[1];
                }
                if (LocalDateTime.class.equals(inferenceDateType)) {
                    method.invoke(dest, LocalDateTime.now());
                } else if (Date.class.equals(inferenceDateType)) {
                    method.invoke(dest, new Date());
                } else if (Long.class.equals(inferenceDateType)) {
                    method.invoke(dest, System.currentTimeMillis());
                } else if (String.class.equals(inferenceDateType)) {
                    method.invoke(dest, LocalDateTime.now().toString());
                } else if (Instant.class.equals(inferenceDateType)) {
                    method.invoke(dest, Instant.now());
                }
                break;
            default:
                break;
        }
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    private static final class Operator {
        static final Operator UNKNOWN = new Operator("unknown", "unknown");

        private String userId;
        private String fullName;

        public Operator(UserCacheBasic src) {
            this.userId = src.getUserId();
        }

        public Operator(UserCacheDetail src) {
            this.userId = src.getUserId();
            this.fullName = src.getFullname();
        }
    }

}
