package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 导入结果-导入用户指标明细表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class XpdImportIndicatorUserPO implements Serializable {
    /**
    * 主键id
    */
    private String id;

    /**
    * 导入记录ID，rv_xpd_import.id
    */
    private String importId;

    /**
    * 盘点项目表id
    */
    private String xpdId;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 用户ID
    */
    private String userId;

    /**
    * 总分
    */
    private BigDecimal scoreTotal;

    /**
    * 指标ID
    */
    private String sdIndicatorId;

    /**
    * 指标得分
    */
    private BigDecimal sdIndicatorScore;

    /**
    * 是否达标(0-不达标, 1-达标)
    */
    private Integer qualified;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更新人id
    */
    private String updateUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 是否删除(0-未删除, 1-已删除)
    */
    private Integer deleted;

    @Serial
    private static final long serialVersionUID = 1L;
}