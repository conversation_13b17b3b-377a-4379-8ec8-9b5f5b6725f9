package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 盘点计算规则表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_prj_rule")
public class PrjDimRulePO {
    // 主键
    @TableId
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_RULE_ID)
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 维度配置关系主键id
    @TableField("dimension_config_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_DIM_CONFIG_ID)
    private String dimensionConfigId = "";

    // 规则计算优先级，默认高等级优先，0-高等级优先， 1-低等级优先
    @TableField("compute_priority")
    private Integer computePriority = 0;

    // 规则说明
    @TableField("rule_remark")
    private String ruleRemark = "";

    // 分级类型（0-枚举，1-百分比，2-绝对值）
    @TableField("class_type")
    private Integer classType = 0;

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
