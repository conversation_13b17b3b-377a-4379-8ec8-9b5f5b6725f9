package com.yxt.talent.rv.infrastructure.repository.prj;

import com.yxt.talent.rv.domain.prj.Prj;
import com.yxt.talent.rv.domain.prj.entity.PrjMgr;
import com.yxt.talent.rv.domain.prj.entity.PrjTrainMap;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConf;
import com.yxt.talent.rv.domain.prj.repo.PrjDomainRepo;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMgrMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjTrainMapMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjMgrPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.train.PrjTrainMapPO;
import com.yxt.talent.rv.infrastructure.repository.prj.assembler.PrjAssembler;
import com.yxt.util.OptionalEmpty;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


@Slf4j
@Repository
@RequiredArgsConstructor
public class PrjDomainRepoImpl implements PrjDomainRepo {

    private final PrjMapper prjMapper;
    private final PrjMgrMapper prjMgrMapper;
    private final PrjAssembler prjAssembler;
    private final PrjDimConfRepo prjDimConfRepo;
    private final PrjTrainMapMapper prjTrainMapMapper;

    @Override
    public Optional<Prj> load(
            @NonNull String orgId, @NonNull String entityId, @NonNull Prj.LoadConfig loadConfig) {
        return this.load(orgId, entityId).map(entity -> loadSub(entity, loadConfig));
    }

    @Override
    public Optional<Prj> load(@NonNull String orgId, @NonNull String entityId) {
        PrjPO prjPO = prjMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(prjPO).map(prjAssembler::toPrj);
    }

    @Override
    public void save(@NonNull Prj entity) {
        String orgId = entity.getOrgId();

        // 假删
        convertUpdate(entity, prjAssembler::toPrjPO, prjMapper::insertOrUpdate);

        // 真删，需要处理deleted=1的记录，这些记录不能更新，而是删除掉
        deleteConvertUpdateBatch(orgId, entity.getPrjMgr(), prjAssembler::toPrjMgrPos,
                prjMgrMapper::deleteBatch, prjMgrMapper::insertOrUpdateBatch);

        // 支持假删，所以这里无需处理删除的场景
        convertUpdateBatch(entity.getPrjTrainings(), prjAssembler::toPrjTrainMapPos,
                prjTrainMapMapper::insertOrUpdateBatch);

        OptionalEmpty.of(entity.getPrjDimConf()).ifNotEmpty(prjDimConfRepo::save);
    }

    @Override
    public void delete(@NonNull Prj entity) {
        EntityUtil.delete(entity.getPrjMgr());
        EntityUtil.delete(entity.getPrjTrainings());
        EntityUtil.delete(entity.getPrjDimConf());
        EntityUtil.delete(entity).ifPresent(this::save);
    }

    private Prj loadSub(@NonNull Prj entity, @NonNull Prj.LoadConfig loadConfig) {
        String orgId = entity.getOrgId();
        String entityId = entity.getId();

        if (loadConfig.loadPrjMgr()) {
            loadPrjMgr(orgId, entityId).ifPresent(entity::addPrjMgr);
        }

        if (loadConfig.loadPrjTrainings()) {
            loadPrjTrainMaps(orgId, entityId).ifPresent(entity::addPrjTrainMaps);
        }

        Optional.ofNullable(loadConfig.loadPrjDimConf())
                .flatMap(subLoadConf -> loadPrjDimConfs(orgId, entityId, subLoadConf))
                .ifPresent(entity::addPrjDimConf);

        return entity;
    }

    private Optional<Collection<PrjTrainMap>> loadPrjTrainMaps(
            @NonNull String orgId, @NonNull String entityId) {
        List<PrjTrainMapPO> prjTrainMapPos = prjTrainMapMapper.selectByPrjId(orgId, entityId);
        return Optional.ofNullable(prjAssembler.toPrjTrainMaps(prjTrainMapPos));
    }

    private Optional<Collection<PrjMgr>> loadPrjMgr(
            @NonNull String orgId, @NonNull String entityId) {
        List<PrjMgrPO> prjMgrPos = prjMgrMapper.selectByProjectId(orgId, entityId);
        return Optional.of(prjMgrPos).map(prjAssembler::toPrjMgr);
    }

    private Optional<Collection<PrjDimConf>> loadPrjDimConfs(
            @NonNull String orgId, @NonNull String entityId,
            @NonNull PrjDimConf.LoadConfig loadConfig) {
        return prjDimConfRepo.loadByPrjId(orgId, entityId, loadConfig);
    }

}
