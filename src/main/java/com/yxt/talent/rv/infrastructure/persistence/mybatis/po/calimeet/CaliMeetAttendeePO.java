package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 校准会参与人员表,即负责校准的人,一般是管理员或者部门经理等角色
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_calibration_meeting_user")
public class CaliMeetAttendeePO {
    // 主键
    @TableId
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 会议id
    @TableField("meeting_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_CALIMEET_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String meetingId;

    // 干系人id
    @TableField("user_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String userId;

    // 干系人类型（1-组织者，2-参会人）
    @TableField("user_type")
    private Integer userType = 0;

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
