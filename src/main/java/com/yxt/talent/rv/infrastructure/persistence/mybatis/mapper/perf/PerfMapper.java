package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfUserInfoVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talentrvfacade.bean.UserPerf4Facade;
import com.yxt.talentrvfacade.bean.UserPerfQuery;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Mapper
@Repository
public interface PerfMapper extends CommonMapper<PerfPO> {

    @Nonnull
    IPage<PerfUserInfoVO> selectUserPage(
            Page<PerfUserInfoVO> page,
            @Nonnull @Param("orgId") String orgId,
            @Param("deptIds") List<String> deptIds,
            @Param("authDeptIds") List<String> authDeptIds,
            @Param("searchKey") String searchKey,
            @Param("userIds") List<String> userIds,
            @Param("query") SearchUdpScopeAuthQuery query);

    int countByToolIdAndUserIds(
            @Nonnull @Param("orgId") String orgId, @Param("toolId") String toolId,
            @Param("existUserIds") List<String> existUserIds);

    @Nullable
    PerfPO selectByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    default void insertOrUpdate(PerfPO perfPO) {
        this.insertOrUpdateBatch(List.of(perfPO));
    }

    long batchInsertOrUpdate(@Param("list") List<PerfPO> list);

    default void insertOrUpdateBatch(Collection<PerfPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    void deleteByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    List<PerfPO> selectByOrgIdAndPeriodId(
            @Param("orgId") String orgId, @Param("periodId") String periodId);

    List<PerfPO> selectByPeriodIdsAndUserIds(
            @Param("orgId") String orgId, @Param("periodIds") List<String> periodIds,
            @Param("userIds") List<String> userIds);

    List<PerfPO> selectByPeriodIdAndUserIds(
            @Param("orgId") String orgId, @Param("periodId") String periodId,
            @Param("userIds") List<String> userIds);

    PerfPO selectByPeriodIdAndUserId(
            @Param("orgId") String orgId, @Param("periodId") String periodId,
            @Param("userId") String userId);

    List<PerfPO> getPerfByPeriodAndUserIdIfHas(
            @Param("orgId") String orgId, @Param("periodId") String periodId,
            @Param("userIds") List<String> userIds);

    List<PerfPO> selectByOrgIdAndUseId(
            @Param("orgId") String orgId, @Param("userId") String userId);

    List<PerfPO> selectByOrgId(@Param("orgId") String orgId);

    List<String> selectPeriodIds(@Param("orgId") String orgId);

    int findCountByPeriodId(@Param("orgId") String orgId, @Param("periodId") String periodId);

    List<String> selectPeriodLevel(@Param("orgId") String orgId);

    String findPeriodLevel(@Param("orgId") String orgId, @Param("periodLevel") String periodLevel);

    void deleteBatch(
            @Param("orgId") String orgId, @Param("removeIds") Collection<String> removeIds);

    @Nullable
    PerfPO selectByUserIdAndPerfPeriodId(String orgId, String userId, String perfPeriodId);

    List<PerfPO> selectByUserIdsAndPeriodIds(
            @Param("orgId") String orgId, @Param("userIds") Collection<String> userIds,
            @Param("perfPeriodIds") List<String> perfPeriodIds);

    List<String> selectUserIdsByOrgId(@Param("orgId") String orgId);

    List<String> selectDeptIdsByOrgId(@Param("orgId") String orgId);

    /**
     * 删除机构下所有绩效数据
     *
     * @param orgId 机构ID
     * @return 删除的记录数
     */
    int deleteAllByOrgId(@Param("orgId") String orgId);

    List<UserPerf4Facade> selectUserPerf(@Param("query") UserPerfQuery query);
}