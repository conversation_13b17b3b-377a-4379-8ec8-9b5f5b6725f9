package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Table;
import com.google.common.collect.TreeBasedTable;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.Validate;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import com.yxt.talent.rv.infrastructure.service.remote.SpmodelAclService;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@UtilityClass
public class SqlUtil {

    public static final String PARAM_COUNTS = "counts";

    /**
     * 生成查询sql, 排序字段通过clazz类中的@SqlOrder注解动态解析获取
     *
     * @param clazz       类型
     * @param queryParams 查询参数
     * @return 构建的SQL查询字符串
     */
    public static String buildSqlQuery(@NonNull Class<?> clazz, Map<String, Object> queryParams) {
        // 从类注解获取表名
        String tableName = getTableName(clazz);
        return buildSqlQueryInternal(clazz, tableName, queryParams, null);
    }

    /**
     * 生成查询sql, 表名通过clazz类中的@SqlTable注解动态解析，排序字段直接传递
     *
     * @param clazz         类型
     * @param queryParams   查询参数
     * @param orderByFields 排序字段
     * @return 构建的SQL查询字符串
     */
    @SuppressWarnings({"java:S1319"})
    public static String buildSqlQuery(
            @jakarta.annotation.Nonnull Class<?> clazz,
            @jakarta.annotation.Nonnull Map<String, Object> queryParams,
            LinkedHashMap<String, Boolean> orderByFields) {
        // 从类注解获取表名
        String tableName = getTableName(clazz);
        return buildSqlQueryInternal(clazz, tableName, queryParams, orderByFields);
    }

    /**
     * 生成查询sql, 表名和排序字段直接传递
     *
     * @param clazz         类型
     * @param tableName     表名
     * @param queryParams   查询参数
     * @param orderByFields 排序字段
     * @return 构建的SQL查询字符串
     */
    @SuppressWarnings({"java:S1319"})
    public static String buildSqlQuery(
            @jakarta.annotation.Nonnull Class<?> clazz,
            @jakarta.annotation.Nonnull String tableName, Map<String, Object> queryParams,
            @Nullable LinkedHashMap<String, Boolean> orderByFields) {
        return buildSqlQueryInternal(clazz, tableName, queryParams, orderByFields);
    }

    /**
     * 内部方法，用于构建SQL查询字符串
     *
     * @param clazz         类型
     * @param tableName     表名
     * @param queryParams   查询参数
     * @param orderByFields 排序字段
     * @return 构建的SQL查询字符串
     */
    private static String buildSqlQueryInternal(
            Class<?> clazz, String tableName, Map<String, Object> queryParams,
            @Nullable LinkedHashMap<String, Boolean> orderByFields) {
        StringBuilder sqlBuilder = new StringBuilder("SELECT ");
        // 构建SELECT子句
        buildSelectClause(sqlBuilder, clazz);
        // 构建FROM子句
        buildFromClause(sqlBuilder, tableName);
        // 构建WHERE子句
        buildWhereClause(sqlBuilder, queryParams);
        // 构建ORDER BY子句
        buildOrderByClause(sqlBuilder, clazz, orderByFields);

        log.info("LOG62190:generateSqlQuery={}", sqlBuilder);
        return sqlBuilder.toString();
    }

    public static String buildSqlPageQuery(
            Class<?> clazz, Map<String, Object> queryParams, Paging page) {
        return buildSqlQuery(clazz, queryParams) + buildPageByClause(page);
    }

    private static StringBuilder buildPageByClause(Paging page) {
        Objects.requireNonNull(page, "分页参数不能为空");
        long limit = page.getLimit();
        long offset = page.getOffset();
        return new StringBuilder().append(" LIMIT ").append(offset).append(", ").append(limit);
    }

    private String getTableName(Class<?> clazz) {
        SqlTable sqlTable = clazz.getAnnotation(SqlTable.class);
        Validate.isNotNull(sqlTable, "未找到SqlTable注解");
        return sqlTable.value();
    }

    private static void buildSelectClause(StringBuilder sqlBuilder, Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            Annotation ignoreField = field.getAnnotation(SqlIgnore.class);
            if (ignoreField == null) {
                String formattedFieldName = formatFieldName(fieldName, true);
                sqlBuilder.append(formattedFieldName).append(", ");
            }
        }
        // 移除最后一个逗号和空格
        sqlBuilder.setLength(sqlBuilder.length() - 2);
    }

    private String formatFieldName(String fieldName, boolean appendAs) {
        String lowerCaseField = fieldName.replaceAll("([A-Z])", "_$1").toLowerCase();
        return appendAs ? lowerCaseField + " AS " + fieldName : lowerCaseField;
    }

    private static void buildFromClause(StringBuilder sqlBuilder, String tableName) {
        sqlBuilder.append(" FROM ").append(tableName);
    }

    private static void buildWhereClause(
            StringBuilder sqlBuilder, Map<String, Object> queryParams) {
        if (queryParams != null && !queryParams.isEmpty()) {
            sqlBuilder.append(" WHERE ");
            for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                appendQueryParam(sqlBuilder, paramName, paramValue);
                sqlBuilder.append(" AND ");
            }
            // 移除最后一个 AND
            sqlBuilder.setLength(sqlBuilder.length() - 5);
        }
    }

    @SuppressWarnings("java:S3776")
    private static void appendQueryParam(
            StringBuilder sqlBuilder, String paramName, Object paramValue) {
        if (paramValue == null) {
            sqlBuilder.append(paramName).append(" IS NULL");
        } else {
            if (paramValue instanceof String) {
                sqlBuilder.append(paramName).append(" = '").append(paramValue).append("'");
            } else if (paramValue instanceof Object[] arrayValue) {
                sqlBuilder.append(paramName).append(" IN (");
                for (int i = 0; i < arrayValue.length; i++) {
                    if (i > 0) {
                        sqlBuilder.append(", ");
                    }
                    Object item = arrayValue[i];
                    if (item instanceof String) {
                        sqlBuilder.append("'").append(item).append("'");
                    } else {
                        sqlBuilder.append(item);
                    }
                }
                sqlBuilder.append(")");
            } else if (paramValue instanceof Collection<?> arrayValue) {
                sqlBuilder.append(paramName).append(" IN (");
                int i = 0;
                for (Object item : arrayValue) {
                    if (i > 0) {
                        sqlBuilder.append(", ");
                    }
                    if (item instanceof String) {
                        sqlBuilder.append("'").append(item).append("'");
                    } else {
                        sqlBuilder.append(item);
                    }
                    i++;
                }
                sqlBuilder.append(")");
            } else {
                sqlBuilder.append(paramName).append(" = ").append(paramValue);
            }
        }
    }

    private static String appendCountSql(String query) {
        return "SELECT COUNT(*) as " + PARAM_COUNTS + " FROM (" + query + ") AS t";
    }

    private static void buildOrderByClause(
            StringBuilder sqlBuilder, Class<?> clazz,
            @Nullable LinkedHashMap<String, Boolean> orderByFields) {
        if (orderByFields != null && !orderByFields.isEmpty()) {
            // 使用LinkedHashMap中的排序字段
            sqlBuilder.append(" ORDER BY ");
            orderByFields.forEach((fieldName, isAsc) -> {
                sqlBuilder.append(fieldName);
                if (!isAsc) { // NOSONAR
                    sqlBuilder.append(" DESC");
                }
                sqlBuilder.append(", ");
            });
        } else {
            buildOrderByClause(sqlBuilder, clazz);
        }

        // 移除最后一个逗号和空格，如果有排序字段的话
        if (sqlBuilder.toString().endsWith(", ")) {
            sqlBuilder.setLength(sqlBuilder.length() - 2);
        }
    }

    // 从类的字段注解中获取排序信息
    private static void buildOrderByClause(StringBuilder sqlBuilder, Class<?> clazz) {
        Table<Integer, String, String> orderTables = TreeBasedTable.create();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            SqlOrder orderField = field.getAnnotation(SqlOrder.class);
            if (orderField != null) {
                String fieldName = field.getName();
                orderTables.put(orderField.order(), formatFieldName(fieldName, false),
                        orderField.direction());
            }
        }

        if (!orderTables.isEmpty()) {
            sqlBuilder.append(" ORDER BY ");
            for (Integer order : orderTables.rowKeySet()) {
                orderTables.row(order).forEach((fieldName, direction) -> {
                    sqlBuilder.append(fieldName);
                    if ("DESC".equals(direction)) {
                        sqlBuilder.append(" DESC");
                    }
                    sqlBuilder.append(", ");
                });
            }
        }
    }

    /**
     * 列表查询
     *
     * @param orgId
     * @param clazz
     * @param queryParams
     * @param <T>
     */
    @jakarta.annotation.Nonnull
    public static <T> List<T> queryListFromModel(
            String orgId, Class<T> clazz, Map<String, Object> queryParams) {
        String query = SqlUtil.buildSqlQuery(clazz, queryParams);
        return doExecuteSql(orgId, clazz, query);
    }

    /**
     * 列表查询, 直接传入sql查询
     *
     * @param orgId
     * @param querySql
     * @param clazz
     * @param <T>
     */
    @jakarta.annotation.Nonnull
    public static <T> List<T> queryListFromModel(String orgId, String querySql, Class<T> clazz) {
        return doExecuteSql(orgId, clazz, querySql);
    }

    /**
     * 分页查询
     *
     * @param orgId
     * @param clazz
     * @param queryParams
     * @param page
     * @param <T>
     */
    @jakarta.annotation.Nonnull
    public static <T> PagingList<T> queryPageFromModel(
            String orgId, Class<T> clazz, Map<String, Object> queryParams, Paging page) {
        Objects.requireNonNull(page, "分页参数不能为空");

        // 查询总数
        String countSql = SqlUtil.appendCountSql(SqlUtil.buildSqlQuery(clazz, queryParams));
        List<JSONObject> counts = doExecuteSql(orgId, JSONObject.class, countSql);
        JSONObject countJson = CollectionUtils.isEmpty(counts) ? null : counts.get(0);
        long count = countJson != null ? countJson.getLongValue(SqlUtil.PARAM_COUNTS) : 0;

        if (count == 0) {
            return new PagingList<>(
                    Collections.emptyList(), new Paging(page.getLimit(), page.getOffset(), 0, 0));
        }

        String query = SqlUtil.buildSqlPageQuery(clazz, queryParams, page);
        List<T> datas = doExecuteSql(orgId, clazz, query);
        long totalPages = count % page.getLimit() == 0 ? count / page.getLimit() :
                count / page.getLimit() + 1;

        return new PagingList<>(
                datas, new Paging(page.getLimit(), page.getOffset(), totalPages, count));
    }

    @jakarta.annotation.Nonnull
    public static <T> PagingList<T> queryPage(String orgId, Class<T> clazz, String querySql, String countSql, Paging page) {
        Objects.requireNonNull(page, "分页参数不能为空");

        // 查询总数
        List<JSONObject> counts = doExecuteSql(orgId, JSONObject.class, countSql);
        JSONObject countJson = CollectionUtils.isEmpty(counts) ? null : counts.get(0);
        long count = countJson != null ? countJson.getLongValue(SqlUtil.PARAM_COUNTS) : 0;

        if (count == 0) {
            return new PagingList<>(
                Collections.emptyList(), new Paging(page.getLimit(), page.getOffset(), 0, 0));
        }

        List<T> datas = doExecuteSql(orgId, clazz, querySql);
        long totalPages = count % page.getLimit() == 0 ? count / page.getLimit() :
            count / page.getLimit() + 1;

        return new PagingList<>(
            datas, new Paging(page.getLimit(), page.getOffset(), totalPages, count));
    }

    @jakarta.annotation.Nonnull
    private static <T> List<T> doExecuteSql(String orgId, Class<T> clazz, String query) {
        log.debug("LOG50000:{}", query);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(query);
        List<JSONObject> results = new ArrayList<>();
        try {
            SpmodelAclService spmodelAclService =
                    SpringContextHolder.getBean(SpmodelAclService.class);
            Validate.isNotNull(spmodelAclService, "未找到spmodelSqlService");
            results = spmodelAclService.sql(sqlParam);
        } catch (Exception e) {
            log.error("LOG50010:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        return results.stream().map(data -> data.toJavaObject(clazz)).collect(Collectors.toList());
    }

    /**
     * 处理查询中SQL 通配符的问题
     *
     * @param str
     */
    public static String escapeSql(String str) {
        if (StringUtils.isBlank(str)) {
            return StringUtils.EMPTY;
        }

        StringBuilder temp = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) == '%' || str.charAt(i) == '_' || str.charAt(i) == '\\') {
                temp.append("\\").append(str.charAt(i));
            } else {
                temp.append(str.charAt(i));
            }
        }
        return StringUtils.replace(temp.toString(), "'", "''");
    }

    @jakarta.annotation.Nonnull
    public static <T> List<T> jsonObject2BeanList(Class<T> clazz, List<JSONObject> results) {
        if (null == results) {
            return new ArrayList<>();
        }
        log.info("LOG62200:{}", results);
        return JSON.parseArray(results.toString(), clazz);
        // jackson不支持下划线字段名映射成驼峰名
//        return BeanHelper.json2Bean(jsonArray.toJSONString(), List.class, clazz);
    }

    @jakarta.annotation.Nullable
    public static <T> T jsonObject2Bean(Class<T> clazz, JSONObject result) {
        if (null == result) {
            return null;
        }
        log.info("LOG14075:{}", result);

        return result.toJavaObject(clazz);
//        return BeanHelper.json2Bean(result.toJSONString(), clazz);
    }

    @Documented
    @Target(value = ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface SqlIgnore {
    }

    @Documented
    @Target(value = ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface SqlOrder {
        String DESC = "desc";
        String ASC = "asc";

        @AliasFor("direction") int value() default 0;

        @AliasFor("value") int order() default 0;

        String direction() default ASC;
    }

    @Documented
    @Target(value = ElementType.TYPE)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface SqlTable {
        String value() default "";
    }
}
