package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.calimeet.event;

import com.xxl.job.core.util.ShardingUtil;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.XxlTaskEvent;
import com.yxt.task.Task;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@ToString(callSuper = true)
@SuperBuilder(builderMethodName = "xxlTaskEventBuilder")
public class CaliMeetMinutesTimeWaitTaskEvent extends XxlTaskEvent<String> {

    public CaliMeetMinutesTimeWaitTaskEvent(ShardingUtil.ShardingVO shardingVO, Task<String> task) {
        super(shardingVO, task);
    }

}
