package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.talent.rv.application.xpd.common.dto.ActivityInfo4UserDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.RvActivityArrangeItemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * ActivityArrangeItemMapper
 */
@Mapper
public interface RvActivityArrangeItemMapper extends BaseMapper<RvActivityArrangeItemPO> {
    /**
     * List ref reg id set.
     *
     * @param orgId     the org id
     * @param actvId    the actv id
     * @param actvOrgId the actv org id
     * @return the set
     */
    Set<String> listRefRegId(@Param("orgId") String orgId, @Param("actvId") String actvId,
            @Param("actvOrgId") Long actvOrgId);

    /**
     * List arrange item list.
     *
     * @param orgId     the org id
     * @param actvId    the actv id
     * @param actvOrgId the actv org id
     * @param itemType  the item type
     * @return the list
     */
    List<ActivityArrangeItem> listArrangeItem(@Param("orgId") String orgId, @Param("actvId") String actvId,
            @Param("actvOrgId") Long actvOrgId, @Param("itemType") Integer itemType);

    /**
     * Item by ref id and actv id list.
     *
     * @param orgId  the org id
     * @param actvId the actv id
     * @param refId  the ref id
     * @return the list
     */
    List<ActivityArrangeItem> itemByRefIdAndActvId(@Param("orgId") String orgId, @Param("actvId") String actvId,
            @Param("refId") String refId);

    /**
     * Delete by exclude ids.
     *
     * @param orgId      the org id
     * @param operatorId the operator id
     * @param itemIds    the item ids
     */
    void deleteByIds(@Param("orgId") String orgId, @Param("operatorId") String operatorId,
            @Param("itemIds") Collection<Long> itemIds);

    /**
     *
     * Batch insert.
     *
     * @param list the list
     */
    void batchInsert(@Param("list") Collection<ActivityArrangeItem> list);

    /**
     * Batch update.
     *
     * @param list the list
     */
    void batchUpdate(@Param("list") List<ActivityArrangeItem> list);

    /**
     * Update study hours.
     *
     * @param orgId      the org id
     * @param itemIds    the item ids
     * @param studyHours the study hours
     */
    void updateStudyHours(@Param("orgId") String orgId,
            @Param("itemIds") Collection<Long> itemIds, @Param("studyHours") Integer studyHours);


    /**
     * 获取阶段下的任务ids
     *
     * @param orgId
     * @param actvId
     * @param parentIds
     * @return
     */
    List<ActivityArrangeItem> getItemByParentId(@Param("orgId") String orgId, @Param("actvId") String actvId,
            @Param("parentIds") List<Long> parentIds);

    List<ActivityInfo4UserDto> listInfoByIds(@Param("orgId") String orgId, @Param("ids") Collection<String> ids);

    @Select("""
    select id from rv_activity_arrange_item where org_id = #{orgId}
    and actv_id = #{actvId} and ref_id = #{refId} and deleted = 0 limit 1
    """)
    Long getItemIdByRefId(@Param("orgId") String orgId, @Param("actvId") String actvId, @Param("refId") String refId);

    @Select("""
    select ref_name from rv_activity_arrange_item where org_id = #{orgId}
    and actv_id = #{actvId} and ref_id = #{refId} and deleted = 0 limit 1
    """)
    String getNmeByRefId(@Param("orgId") String orgId, @Param("actvId") String actvId, @Param("refId") String refId);
}
