package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserTaskResultPO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface DmpUserTaskResultMapper extends CommonMapper<DmpUserTaskResultPO> {

    long batchInsertOrUpdate(@Nonnull @Param("list") List<DmpUserTaskResultPO> list);

    int batchInsert(@Nonnull @Param("list") List<DmpUserTaskResultPO> list);

    default void insertBatch(Collection<DmpUserTaskResultPO> list) {
        batchExecute(list, this::batchInsert);
    }

    /**
     * 根据用户ID数组查询
     *
     * @param orgId
     * @param dmpId
     * @param userIds
     */
    @Nonnull
    List<DmpUserTaskResultPO> selectByDmpIdAndUserIds(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("userIds") List<String> userIds);

    void deleteByTaskIdAndUserIds(
            @Nonnull @Param("orgId") String orgId, @Param("taskId") String taskId,
            @Param("userIds") List<String> userIds);

    default void insertOrUpdateBatch(Collection<DmpUserTaskResultPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    long deleteBatch(
            @Nonnull @Param("orgId") String orgId, @Param("ids") Collection<String> ids);

    void deleteByTaskId(
            @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("taskId") String taskId);

    void deleteByUserIds(
            @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("delUserIds") List<String> delUserIds);

    void deleteByDmpId(@Param("orgId") String orgId, @Param("dmpId") String dmpId);

    @Nonnull
    List<DmpUserTaskResultPO> selectByOrgId(@Param("orgId") String orgId);
}
