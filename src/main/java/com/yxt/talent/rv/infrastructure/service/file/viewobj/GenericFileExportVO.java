package com.yxt.talent.rv.infrastructure.service.file.viewobj;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "APass文件导出时的统一响应体")
public class GenericFileExportVO {

    public static final GenericFileExportVO EMPTY_RESULT = new GenericFileExportVO();

    @Schema(description = "文件路径")
    private String filePath = EMPTY;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "下载中心给出来的导出任务id")
    private Long exportTaskId = -1L;

    public static GenericFileExportVO byFilePath(String filePath) {
        GenericFileExportVO genericFileExportVO = new GenericFileExportVO();
        genericFileExportVO.setFilePath(filePath);
        return genericFileExportVO;
    }

}
