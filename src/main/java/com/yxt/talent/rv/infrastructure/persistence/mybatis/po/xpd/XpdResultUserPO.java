package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.rv.domain.RvBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 盘点用户结果
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_result_user")
public class XpdResultUserPO extends RvBaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 盘点项目id
     */
    private String xpdId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 分层id, rv_xpd_level.id
     */
    private String xpdLevelId;

    /**
     * 是否胜任:0-未胜任,1-胜任
     */
    private Integer competent;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 执行计算批次号
     */
    private Integer calcBatchNo;

    /**
     * 项目结果-分值
     */
    private BigDecimal scoreValue;

    /**
     * 项目结果-达标率
     */
    private BigDecimal qualifiedPtg;

    /**
     * 是否被校准过，0-否 1-是
     */
    private Integer caliFlag;

    /**
     * 被校准结果覆盖之前的原始的计算出来的数据快照
     */
    private String originalSnap;
}