package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface CalimeetParticipantsMapper extends CommonMapper<CalimeetParticipantsPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetParticipantsPO record);

    CalimeetParticipantsPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetParticipantsPO record);

    int updateBatch(@Param("list") List<CalimeetParticipantsPO> list);

    int batchInsert(@Param("list") List<CalimeetParticipantsPO> list);

    void deleteByMeetId(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId);

    List<CalimeetParticipantsPO> selectByCaliMeetId(@Param("orgId") String orgId,
            @Param("caliMeetId") String caliMeetId);

    List<CalimeetParticipantsPO> selectByCaliMeetIds(@Param("orgId") String orgId,
            @Param("caliMeetIds") List<String> caliMeetIds);

    List<CalimeetParticipantsPO> listByCalimeetIdAndUserId(@Param("orgId") String orgId,
        @Param("calimeetId") String calimeetId, @Param("userId") String userId);

    int updateFinishByUserId(@Param("orgId") String orgId,
        @Param("calimeetId") String calimeetId, @Param("userId") String userId);

    List<CalimeetParticipantsPO> listByCalimeetIdsAndUserId(@Param("orgId") String orgId,
        @Param("calimeetIds") List<String> calimeetIds, @Param("userId") String userId);
}