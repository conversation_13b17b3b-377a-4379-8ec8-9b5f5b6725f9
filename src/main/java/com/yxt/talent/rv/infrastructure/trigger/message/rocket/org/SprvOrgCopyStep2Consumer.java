package com.yxt.talent.rv.infrastructure.trigger.message.rocket.org;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import com.yxt.event.EventPublisher;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.domain.org.event.SprvOrgCopyStep2Event;
import com.yxt.talent.rv.infrastructure.common.constant.MQConstant;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import com.yxt.talentbkfacade.bean.SpDemoInitCmd4Mq;
import com.yxt.talentbkfacade.bean.SpDemoInitStatus4Mq;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.yxt.talentbkfacade.constant.BkFacadeContants.TOPIC_SP_DEMO_INIT_STATUS;

/**
 * BkDemoInitCopyConsumer
 *
 * <AUTHOR> harleyge
 * @Date 13/9/24 3:44 pm
 */
@Component
@AllArgsConstructor
@Slf4j
@RocketMQMessageListener(consumerGroup = MQConstant.CONSUMER_GROUP_PREFIX + MQConstant.TOPIC_SE_C_ORG_COPY_STEP_2,         topic = MQConstant.TOPIC_SE_C_ORG_COPY_STEP_2, consumeThreadNumber = 2, consumeTimeout = 30)
public class SprvOrgCopyStep2Consumer implements RocketMQListener<SpDemoInitCmd4Mq> {
    private final EventPublisher eventPublisher;
    private final RocketMqAclSender rocketMqAclSender;
    @Override
    public void onMessage(SpDemoInitCmd4Mq message) {
        SpDemoInitStatus4Mq status4Mq = new SpDemoInitStatus4Mq();
        status4Mq.setModule(SprvDemoOrgCopyConstants.MODULE_CODE_SPRV);
        try {
            log.info("LOG10032:{}", toJSONString(message));
            status4Mq.setOrgId(message.getOrgId());
            status4Mq.setStage(SpDemoInitStatus4Mq.STAGE_COPY);
            status4Mq.setStatus(YesOrNo.NO.getValue());
            eventPublisher.publish(new SprvOrgCopyStep2Event(JSON.parseObject(message.getOrgInitData(), OrgInit4Mq.class)));
            status4Mq.setStatus(YesOrNo.YES.getValue());
        } catch (Exception e) {
            log.error("BkDemoInitCopyConsumer fail msg {}", toJSONString(message), e);
        } finally {
            rocketMqAclSender.send(TOPIC_SP_DEMO_INIT_STATUS, toJSONString(status4Mq));
        }
    }
}
