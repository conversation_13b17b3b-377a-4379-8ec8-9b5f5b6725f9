package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.application.prj.user.dto.PrjUserSimpleResultDTO;
import com.yxt.talent.rv.application.prj.user.dto.UserDimGridAppDTO;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjGridScopeAuthQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.XpdUserGridResultVO;
import com.yxt.talent.rv.controller.openapi.viewobj.PrjUserResultOpenVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Mapper
@Repository
public interface PrjUserResultMapper extends CommonMapper<PrjUserResultPO> {

    @Nonnull
    IPage<XpdUserGridResultVO> selectByXyAxis(
            @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("search") PrjGridScopeAuthQuery search,
            @Param("requestPage") IPage<XpdUserGridResultVO> requestPage);

    int countUserByOrgIdAndPrjIdAndDimId(
            @Nonnull @Param("orgId") String orgId, @Param("projectId") String projectId,
            @Param("dimensionId") String dimensionId);

    @Nonnull
    List<UserDimGridAppDTO> selectUserDimensionResult(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId);

    @Nonnull
    IPage<PrjUserResultOpenVO> searchPage(
            @Nonnull IPage<PrjUserResultOpenVO> page, @Nonnull @Param("orgId") String orgId,
            @Param("projectIds") List<String> projectIds,
            @Param("searchStartTime") String searchStartTime,
            @Param("searchEndTime") String searchEndTime);

    @Nonnull
    List<PrjUserSimpleResultDTO> selectPrjUserLatestResults(
            @Nonnull @Param("orgId") String orgId, @Param("userIds") List<String> userIds);

    @Nonnull
    List<PrjUserResultPO> listByPrjIdAndUserIds(
            @Nonnull @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("userIds") List<String> userIds);

    long batchInsertOrUpdate(@Param("list") Collection<PrjUserResultPO> list);

    void deleteBatch(
            @Param("orgId") String orgId, @Param("removedIds") Collection<String> removedIds);

    default void insertOrUpdate(PrjUserResultPO prjUserResultPos) {
        batchInsertOrUpdate(List.of(prjUserResultPos));
    }

    default void insertOrUpdateBatch(Collection<PrjUserResultPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    @Nonnull
    List<PrjUserResultPO> selectByOrgIdAndPrjIdAndDimIdAndIfPrjUserIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("dimId") String dimId, @Nullable @Param("prjUserIds") List<String> prjUserIds);

    @Nonnull
    List<PrjUserResultPO> selectByOrgIdAndPrjIdAndUserIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("userIds") List<String> userIds);

    @Nonnull
    List<PrjUserResultPO> selectByOrgIdAndPrjIdAndLastLevelNe0AndIfUserIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("userIds") List<String> userIds);

    void removeByOrgIdAndPrjIdAndDimIdsAndUserIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("dimIds") Collection<String> dimIds,
            @Param("userIds") Collection<String> userIds);

    @Nonnull
    List<PrjUserResultPO> selectByOrgIdAndPrjIdAndUserIdAndDimIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("userId") String userId, @Param("dimIds") List<String> dimIds);

    @Nonnull
    List<PrjUserResultPO> selectByOrgIdAndPrjIdAndDimIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("dimIds") List<String> dimIds);

    @Nonnull
    List<PrjUserResultPO> selectByOrgIdAndUserIdAndPrjIds(
            @Param("orgId") String orgId, @Param("userId") String userId,
            @Param("prjIds") List<String> prjIds);

    @Nonnull
    List<PrjUserResultPO> selectByOrgId(@Param("orgId") String orgId);

}
