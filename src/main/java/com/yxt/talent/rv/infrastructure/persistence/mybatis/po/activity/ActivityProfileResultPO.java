package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 活动模型-动态人才评估-结果表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActivityProfileResultPO implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 活动ID, rv_activity_profile.id
     */
    private String actvProfileId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 指标ID
     */
    private String sdIndicatorId;

    /**
     * 是否达标(0-不达标, 1-达标)
     */
    private Integer qualified;

    /**
     * 创建人主键
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}