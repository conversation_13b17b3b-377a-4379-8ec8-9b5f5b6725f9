package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 盘点人员表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_prj_user")
public class PrjUserPO {
    // 主键
    @TableId
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 盘点项目id
    @TableField("project_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String projectId;

    // 人员id
    @TableField("user_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String userId;

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    // 发展建议
    @TableField("suggestion")
    private String suggestion = "";

    // 盘点结果：取自机构自定义人才标签（默认：0-未知，1-中坚力量, 2-待提升人员, 3-优秀人才）
    @TableField("prj_result")
    private Integer prjResult = 0;

    @Transient
    @TableField(exist = false)
    private String username;

    @Transient
    @TableField(exist = false)
    private String fullname;

    @Transient
    @TableField(exist = false)
    private String deptName;

    @Transient
    @TableField(exist = false)
    private String positionId;

    @Transient
    @TableField(exist = false)
    private String positionName;

    @Transient
    @TableField(exist = false)
    private String managerId;

    @Transient
    @TableField(exist = false)
    private String managerFullname;

    @Transient
    @TableField(exist = false)
    private String deptManagerId;

    @Transient
    @TableField(exist = false)
    private String deptManagerFullname;
}
