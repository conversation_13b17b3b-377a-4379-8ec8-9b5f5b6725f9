package com.yxt.talent.rv.infrastructure.repository.user.assembler;

import com.yxt.talent.rv.domain.user.entity.UdpUser;
import com.yxt.talent.rv.domain.user.entity.UserFocus;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;

/**
 * 将数据库映射实体转换为UdpUser领域对象的转换器类
 */
@Mapper(config = BaseAssemblerConfig.class)
public interface UserAssembler {

    @jakarta.annotation.Nullable
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    UdpUser toUdpUser(@Nullable UdpLiteUserPO udpLiteUser);

    @jakarta.annotation.Nullable
    Collection<UdpUser> toUdpUsers(Collection<UdpLiteUserPO> udpLiteUsers);

    @jakarta.annotation.Nullable
    UserFocus toUserFocus(UserFocusPO userFocusPo);

    @jakarta.annotation.Nullable
    UserFocusPO toUserFocusPo(UserFocus userFocus);

    @jakarta.annotation.Nullable
    Collection<UserFocus> toUserFocus(Collection<UserFocusPO> userFocuses);

    @jakarta.annotation.Nullable
    Collection<UserFocusPO> toUserFocusPo(Collection<UserFocus> userFocuses);

}
