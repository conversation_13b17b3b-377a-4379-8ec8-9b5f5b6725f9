package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 校准会
 */
@Getter
@Setter
@TableName(value = "rv_calibration_meeting")
public class CaliMeetPO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey= SprvDemoOrgCopyConstants.SPRV_CALIMEET_ID)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 盘点项目id
     */
    @TableField(value = "project_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String projectId;

    /**
     * 盘点项目名称
     */
    @Transient
    @TableField(exist = false)
    private String projectName;

    @Transient
    @TableField(exist = false)
    private String activeId;

    /**
     * 校准会状态（0-未开始，1-进行中，2-已结束）
     */
    @TableField(value = "meet_status")
    private Integer meetStatus = 0;

    /**
     * 会议名称
     */
    @TableField(value = "meet_name")
    private String meetName;

    // 会议时间
    @TableField("meet_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date meetTime;

    /**
     * 创建人主键
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    /**
     * 更新人主键
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    /**
     * 会议记录
     */
    @TableField(value = "meet_minutes")
    private String meetMinutes = "";

    // 是否删除(0-否,1-是)
    @TableField("deleted")
    private int deleted = 0;

    /**
     * 显示校准路径 0 关闭 1开启
     */
    @TableField(value = "meet_route_status")
    private Integer meetRouteStatus = 1;

    /**
     * 会议持续时间，单位分钟（用于绚星工作场会议）
     */
    @TableField(value = "duration")
    private Integer duration = 0;

    /**
     * 会议号（11位号码）
     */
    @TableField(value = "meet_no")
    private String meetNo;

    /**
     * 会议id
     */
    @TableField(value = "meet_id")
    private String meetId;
}
