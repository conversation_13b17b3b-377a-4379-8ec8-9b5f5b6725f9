package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.result;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.yxt.common.Constants;
import com.yxt.common.util.BeanHelper;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * 盘点人才定义规则表
 */
@Data
@TableName(value = "rv_prj_result_rule", autoResultMap = true)
public class PrjResultRulePO {
    // 主键
    @TableId
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 人才定义表id
    @TableField("result_config_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_RESULT_CONFIG_ID)
    private String resultConfigId;

    /**
     * 规则配置，JSON存储
     */
    @TableField(value = "rule_config", typeHandler = RuleJsonBeanTypeHandle.class)
    private RuleJsonBean ruleConfig;

    @TableField("rule_expression")
    private String ruleExpression;

    @TableField("rule_expression_show")
    private String ruleExpressionShow;

    //是否删除(0-未删除,1-已删除)
    @TableField("deleted")
    private Integer deleted;

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    public static class RuleJsonBeanTypeHandle extends AbstractJsonTypeHandler<RuleJsonBean> {
        @Override
        protected RuleJsonBean parse(String json) {
            return JSON.parseObject(json, new TypeReference<RuleJsonBean>() {
            });
        }

        @Override
        protected String toJson(RuleJsonBean obj) {
            return BeanHelper.bean2Json(obj, ALWAYS);
        }
    }
}