package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimDetailPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface DmpUserDimDetailMapper extends CommonMapper<DmpUserDimDetailPO> {

    long batchInsertOrUpdate(@Nonnull @Param("list") List<DmpUserDimDetailPO> list);

    int batchInsert(@Nonnull @Param("list") List<DmpUserDimDetailPO> list);

    @Nullable
    DmpUserDimDetailPO selectByOrgIdAndId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    default void insertBatch(Collection<DmpUserDimDetailPO> list) {
        batchExecute(list, this::batchInsert);
    }

    @Nonnull
    List<DmpUserDimDetailPO> selectByTaskIdAndUserIdAndDimId(
            @Nonnull @Param("orgId") String orgId, @Param("taskId") String taskId,
            @Nonnull @Param("userId") String userId, @Param("dimId") String dimId);

    void deleteDimDetails(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("taskId") String taskId, @Param("dimId") String dimId,
            @Param("userIds") List<String> userIds);

    @Nonnull
    List<DmpUserDimDetailPO> selectByDmpIdAndUserId(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Nonnull @Param("userId") String userId);

    List<DmpUserDimDetailPO> selectByTaskIdAndUserIds(
            @Nonnull @Param("orgId") String orgId, @Param("taskId") String taskId,
            @Nonnull @Param("userIds") List<String> userIds);

    default void insertOrUpdateBatch(Collection<DmpUserDimDetailPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    long deleteBatch(
            @Nonnull @Param("orgId") String orgId,
            @Param("ids") Collection<String> ids);

    void deleteByDimIds(
            @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("dimIds") List<String> dimIds);

    void deleteByUserIds(
            @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("userIds") List<String> userIds);

    void deleteByDmpId(@Param("orgId") String orgId, @Param("dmpId") String dmpId);

    @Nonnull
    List<DmpUserDimDetailPO> selectByOrgId(@Param("orgId") String orgId);

}
