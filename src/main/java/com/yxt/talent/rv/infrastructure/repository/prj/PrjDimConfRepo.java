package com.yxt.talent.rv.infrastructure.repository.prj;


import com.yxt.CommonRepository;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConf;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimSameModel;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjImptType;
import com.yxt.talent.rv.domain.prj.entity.conf.rule.PrjDimRule;
import com.yxt.talent.rv.domain.prj.entity.conf.rule.PrjDimRuleCond;
import com.yxt.talent.rv.domain.prj.entity.conf.rule.PrjDimRuleExpr;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.cache.Cacheable;
import com.yxt.talent.rv.infrastructure.persistence.cache.CommonCacheRepository;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjImptTypeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimSameModelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleCondMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleExprMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimSameModelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleCondPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleExprPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRulePO;
import com.yxt.talent.rv.infrastructure.repository.prj.assembler.PrjDimConfAssembler;
import com.yxt.talent.rv.infrastructure.repository.prj.assembler.PrjDimRuleAssembler;
import jakarta.annotation.Nonnull;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConf.CK_PRJ_DIM_IMPORT_TYPE;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNull;
import static java.lang.String.format;


@Slf4j
@Repository
@RequiredArgsConstructor
class PrjDimConfRepo implements CommonRepository {

    private final PrjDimConfMapper prjDimConfMapper;
    private final PrjDimConfToolMapper prjDimConfToolMapper;
    private final PrjDimSameModelMapper prjDimSameModelMapper;
    private final PrjDimRuleMapper prjDimRuleMapper;
    private final PrjDimConfAssembler prjDimConfAssembler;
    private final PrjDimRuleAssembler prjDimRuleAssembler;
    private final PrjDimRuleCondMapper prjDimRuleCondMapper;
    private final PrjDimRuleExprMapper prjDimRuleExprMapper;
    private final PrjImptTypeMapper prjImptTypeMapper;
    private final CommonCacheRepository cacheRepository;

    Optional<Collection<PrjDimConf>> loadByPrjId(
            @NonNull String orgId, @NonNull String prjId,
            @NonNull PrjDimConf.LoadConfig loadConfig) {
        List<PrjDimConfPO> prjDimConfPos = prjDimConfMapper.listActiveDimConfByPrjId(orgId, prjId);
        List<PrjDimConf> prjDimConfs = prjDimConfPos.stream()
                .map(prjDimConfAssembler::toPrjDimConf)
                .filter(Objects::nonNull)
                .map(prjDimConf -> loadSub(prjDimConf, loadConfig))
                .collect(Collectors.toList());
        return Optional.of(prjDimConfs);
    }

    void save(@NonNull PrjDimConf entity) {
        String orgId = entity.getOrgId();

        deleteConvertUpdate(orgId, entity, prjDimConfAssembler::toPrjDimConfPO,
                prjDimConfMapper::deleteById, prjDimConfMapper::insertOrUpdate);

        deleteConvertUpdateBatch(orgId, entity.getPrjDimConfTools(),
                prjDimConfAssembler::toPrjDimConfToolPos, prjDimConfToolMapper::deleteBatch,
                prjDimConfToolMapper::insertOrUpdateBatch);

        deleteConvertUpdateBatch(orgId, entity.getPrjDimRules(),
                prjDimRuleAssembler::toPrjDimRulePos, prjDimRuleMapper::deleteBatch,
                prjDimRuleMapper::insertOrUpdateBatch);

        deleteConvertUpdateBatch(orgId, entity.getPrjDimSameModels(),
                prjDimConfAssembler::toPrjDimSameModelPos, prjDimSameModelMapper::deleteBatch,
                prjDimSameModelMapper::insertOrUpdateBatch);

        cacheRepository.evict(entity.buildPrjDimImptTypeCK(),
                () -> convertUpdateBatch(entity.getPrjImptTypes(),
                        prjDimConfAssembler::toPrjImptTypePos,
                        prjImptTypeMapper::insertOrUpdateBatch));
    }

    /**
     * 批量保存
     *
     * @param entities
     */
    void save(@NonNull Collection<PrjDimConf> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.warn("LOG13415:");
            return;
        }
        entities.forEach(this::save);
    }

    /**
     * 物理删除
     *
     * @param entity
     */
    void delete(@NonNull PrjDimConf entity) {
        EntityUtil.delete(entity.getPrjImptTypes());
        EntityUtil.delete(entity.getPrjDimRules());
        EntityUtil.delete(entity.getPrjDimConfTools());
        EntityUtil.delete(entity.getPrjDimSameModels());
        EntityUtil.delete(entity).ifPresent(this::save);
    }

    void delete(@NonNull Collection<PrjDimConf> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.warn("LOG13425:");
            return;
        }
        entities.forEach(this::delete);
    }

    private PrjDimConf loadSub(
            @NonNull PrjDimConf prjDimConf, @NonNull PrjDimConf.LoadConfig loadConfig) {
        String orgId = prjDimConf.getOrgId();
        String prjId = prjDimConf.getPrjId();
        String dimId = prjDimConf.getDimId();
        String entityId = prjDimConf.getId();

        loadPrjDimConfTools(orgId, entityId).ifPresent(prjDimConf::addPrjDimConfTools);
        loadPrjDimSameModels(orgId, entityId).ifPresent(prjDimConf::addPrjDimSameModels);
        loadPrjDimRule(orgId, entityId).ifPresent(prjDimConf::addPrjDimRules);

        if (loadConfig.loadPrjImptType()) {
            loadPrjImptType(prjDimConf, orgId, prjId, dimId);
        }
        return prjDimConf;
    }

    private void loadPrjImptType(
            @Nonnull PrjDimConf prjDimConf, @Nonnull String orgId, String prjId, String dimId) {
        String key = format(CK_PRJ_DIM_IMPORT_TYPE, orgId, prjId, dimId);
        cacheRepository.cache(key, 1, TimeUnit.DAYS,
                        getOptionalRedisCacheCallback(orgId, prjId, dimId), PrjImptType.class)
                .ifPresent(prjDimConf::addPrjImptType);
    }

    @Nonnull
    private Cacheable<Optional<PrjImptType>> getOptionalRedisCacheCallback(
            @Nonnull String orgId, String prjId, String dimId) {
        return () -> prjImptTypeMapper.selectByPrjIdAndDimId(orgId, prjId, dimId)
                .map(prjDimConfAssembler::toPrjImptType);
    }

    private Optional<Collection<PrjDimRule>> loadPrjDimRule(
            @NonNull String orgId, @NonNull String entityId) {
        List<PrjDimRulePO> prjDimRulePos =
                prjDimRuleMapper.selectByOrgIdAndPrjDimConfId(orgId, entityId);
        return Optional.ofNullable(prjDimRuleAssembler.toPrjDimRules(prjDimRulePos))
                .map(prjRules -> {
                    prjRules.forEach(prjRule -> {
                        this.loadPrjDimRuleCond(orgId, prjRule.getId())
                                .ifPresent(prjRule::addPrjDimRuleCond);
                        this.loadPrjDimRuleExpr(orgId, prjRule.getId())
                                .ifPresent(prjRule::addPrjDimRuleExpr);
                    });
                    return prjRules;
                });
    }

    private Optional<Collection<PrjDimRuleExpr>> loadPrjDimRuleExpr(
            @NonNull String orgId, @NonNull String prjRuleId) {
        List<PrjDimRuleExprPO> prjDimRuleExprPos =
                prjDimRuleExprMapper.selectByPrjDimRuleId(orgId, prjRuleId);
        return Optional.ofNullable(prjDimRuleAssembler.toPrjDimRuleExpr(prjDimRuleExprPos));
    }

    private Optional<Collection<PrjDimRuleCond>> loadPrjDimRuleCond(
            @NonNull String orgId, @NonNull String prjRuleId) {
        List<PrjDimRuleCondPO> prjDimRuleCondPos =
                prjDimRuleCondMapper.selectByPrjDimRuleId(orgId, prjRuleId);
        return Optional.ofNullable(prjDimRuleAssembler.toPrjDimRuleCond(prjDimRuleCondPos));
    }

    private Optional<Collection<PrjDimSameModel>> loadPrjDimSameModels(
            @NonNull String orgId, @NonNull String entityId) {
        List<PrjDimSameModelPO> prjDimSameModelPos =
                prjDimSameModelMapper.selectByPrjDimConfId(orgId, entityId);
        return Optional.ofNullable(prjDimConfAssembler.toPrjDimSameModels(prjDimSameModelPos));
    }

    private Optional<Collection<PrjDimConfTool>> loadPrjDimConfTools(
            @NonNull String orgId, @NonNull String entityId) {
        List<PrjDimConfToolPO> prjDimConfToolPos =
                prjDimConfToolMapper.selectByPrjDimConfId(orgId, entityId);
        return Optional.ofNullable(prjDimConfAssembler.toPrjDimConfTools(prjDimConfToolPos));
    }


}
