package com.yxt.talent.rv.infrastructure.common.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Collection;

@Setter
@Getter
@ToString(callSuper = true)
public class CollectionHolder<T> {

    @Valid
    @Size(min = 1, max = 100, message = "apis.sptalentrv.transmit.import.data.out.limit")
    private Collection<T> datas;

}
