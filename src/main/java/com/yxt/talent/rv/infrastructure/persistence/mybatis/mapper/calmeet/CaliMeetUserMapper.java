package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Mapper
@Repository
public interface CaliMeetUserMapper {

    int deleteByOrgIdAndIdIn(
            @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("idCollection") Collection<String> idCollection);

    int insertList(@Nonnull @Param("list") List<CaliMeetUserPO> list);

    int updateById(@Nonnull @Param("updated") CaliMeetUserPO updated);

    @Nonnull
    List<CaliMeetUserPO> listByOrgId(@Nonnull @Param("orgId") String orgId);

    @Nonnull
    List<CaliMeetUserPO> listByOrgIdAndMeetingId(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId);

    @Nullable
    CaliMeetUserPO selectByIdAndOrgId(
            @Nonnull @Param("id") String id, @Nonnull @Param("orgId") String orgId);

    @Nonnull
    List<CaliMeetUserPO> listByOrgIdAndMeetingIdAndIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("idCollection") Collection<String> idCollection);

    @Nonnull
    IPage<CaliMeetUserVO> pagingVo(
            @Param("page") IPage<CaliMeetUserVO> page, @Nonnull @Param("orgId") String orgId,
            @Param("meetingId") String meetingId,
            @Nonnull @Param("search") CaliMeetUserQuery search,
            @Param("qwUserIds") List<String> qwUserIds);

    @Nullable
    CaliMeetUserVO selectVoByIdAndOrgId(
            @Nonnull @Param("id") String id, @Nonnull @Param("orgId") String orgId);

    @Nonnull
    List<CaliMeetUserPO> list(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userIdCollection") Collection<String> userIdCollection);

    List<String> selectByOrgIdAndUserIds(@Param("orgId") String orgId, @Param("userIds") List<String> userIds);

    List<String> selectByMeetingId4UserIds(@Param("orgId") String orgId, @Param("meetingId") String meetingId);

    long countByMeetingId(@Param("orgId") String orgId, @Param("meetingId") String meetingId);
}
