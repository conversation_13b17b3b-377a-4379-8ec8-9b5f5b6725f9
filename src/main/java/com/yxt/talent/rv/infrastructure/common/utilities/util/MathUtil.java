package com.yxt.talent.rv.infrastructure.common.utilities.util;

import cn.hutool.core.util.NumberUtil;
import jakarta.annotation.Nonnull;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@UtilityClass
@SuppressWarnings("BooleanMethodIsAlwaysInverted")
public final class MathUtil {

    public static final String THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO =
            "The scale must be a positive integer or zero";

    /**
     * 高精度除法，四舍五入
     *
     * @param a
     * @param b
     * @param numScale
     * @return
     */
    public static BigDecimal divide(long a, long b, int numScale) {
        if (numScale < 0) {
            throw new IllegalArgumentException(THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO);
        }
        if (a == 0 || b == 0) {
            return BigDecimal.ZERO.setScale(numScale, RoundingMode.UNNECESSARY);
        }
        return BigDecimal.valueOf(a).divide(BigDecimal.valueOf(b), numScale, RoundingMode.HALF_UP);
    }

    /**
     * 高精度百分比计算，四舍五入
     *
     * @param a
     * @param b
     * @param numScale
     */
    public static BigDecimal dividePer(long a, long b, int numScale) {
        if (numScale < 0) {
            throw new IllegalArgumentException(THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO);
        }
        if (a == 0 || b == 0) {
            return BigDecimal.ZERO.setScale(numScale, RoundingMode.UNNECESSARY);
        }
        return BigDecimal.valueOf(a)
                .divide(BigDecimal.valueOf(b), 10, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(numScale, RoundingMode.HALF_UP);
    }

    /**
     * 高精度百分比计算，四舍五入, 返回值拼接上‘%’
     *
     * @param a
     * @param b
     * @param numScale
     */
    public static String dividePerStr(long a, long b, int numScale) {
        BigDecimal bigDecimal = dividePer(a, b, numScale);
        // 如果是整数不加后面的小数点，直接返回
        if (bigDecimal.compareTo(BigDecimal.valueOf(bigDecimal.intValue())) == 0) {
            return bigDecimal.intValue() + "%";
        }
        return bigDecimal + "%";
    }

    /**
     * 高精度除法计算，自定义精度和舍入规则
     *
     * @param a
     * @param b
     * @param numScale
     * @param round
     * @return
     */
    public static BigDecimal divide(long a, long b, int numScale, RoundingMode round) {
        if (numScale < 0) {
            throw new IllegalArgumentException(THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO);
        }
        if (a == 0 || b == 0) {
            return BigDecimal.ZERO.setScale(numScale, RoundingMode.UNNECESSARY);
        }
        return BigDecimal.valueOf(a)
                .divide(BigDecimal.valueOf(b), 10, RoundingMode.HALF_UP)
                .setScale(numScale, round);
    }

    /**
     * 保留两位小数
     *
     * @param a
     * @param b
     * @param numScale
     * @param round
     * @return
     */
    public static BigDecimal divide4Int(long a, long b, int numScale, RoundingMode round) {
        if (numScale < 0) {
            throw new IllegalArgumentException(THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO);
        }
        if (a == 0 || b == 0) {
            return BigDecimal.ZERO.setScale(numScale, RoundingMode.UNNECESSARY);
        }
        return BigDecimal.valueOf(a)
            .divide(BigDecimal.valueOf(b), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100))
            .setScale(numScale, RoundingMode.HALF_UP);
    }

    /**
     * 基础数字格式验证
     * @param str 输入字符串
     * @param allowScientific 是否允许科学计数法
     * @return 是否合法数字格式
     */
    public static boolean isNumber(String str, boolean allowScientific) {
        if (allowScientific) {
            return NumberUtil.isNumber(str);
        }
        // 自定义严格数字格式验证
        return str.matches("^[+-]?(0|[1-9]\\d*)(\\.\\d+)?$");
    }

    /**
     * 整数类型验证
     * @param str 输入字符串
     * @param signType 符号类型枚举
     * @return 是否符合整数条件
     */
    public static boolean isInteger(String str, SignType signType) {
        if (!NumberUtil.isInteger(str)) {
            return false;
        }
        Number number = NumberUtil.parseNumber(str);
        return checkSign(number, signType);
    }

    /**
     * 数值符号验证
     * @param str 输入字符串
     * @param signType 符号类型枚举
     * @param includeZero 是否包含零值
     * @return 是否符合符号条件
     */
    public static boolean checkSign(String str, SignType signType, boolean includeZero) {
        if (!isNumber(str, true)) {
            return false;
        }
        Number number = NumberUtil.parseNumber(str);
        return checkSign(number, signType) && (includeZero || number.doubleValue() != 0);
    }

    private static boolean checkSign(Number number, SignType signType) {
        double value = number.doubleValue();
        return switch (signType) {
            case POSITIVE -> value > 0;
            case NEGATIVE -> value < 0;
            case ZERO -> value == 0;
        };
    }

    @Nonnull
    public static Integer null2zero(Integer num) {
        return num == null ? 0 : num;
    }

    @Nonnull
    public static Long null2zero(Long num) {
        return num == null ? 0L : num;
    }

    @Nonnull
    public static BigDecimal null2zero(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }

    @Nonnull
    public static Double null2zero(Double num) {
        return num == null ? 0D : num;
    }

    public enum SignType {
        POSITIVE,
        NEGATIVE,
        ZERO
    }

    /**
     * 百分比数值校验
     * @param percentage 百分比字符串（支持带%符号）
     * @param allowOver100 是否允许超过100%
     */
    public static boolean isValidPercentage(String percentage, boolean allowOver100) {
        // 去除百分号处理
        String numStr = percentage.replace("%", "").trim();
        if (!isNumber(numStr, false)) {
            return false;
        }

        BigDecimal value = NumberUtil.toBigDecimal(numStr);
        return allowOver100 || value.compareTo(BigDecimal.ZERO) >= 0 && value.compareTo(new BigDecimal(100)) <= 0;
    }

    /**
     * 增强型范围校验
     * @param value 输入值
     * @param min 最小值
     * @param max 最大值
     * @param leftInclusive 是否包含左边界
     * @param rightInclusive 是否包含右边界
     */
    public static boolean isInRange(
        String value, BigDecimal min, BigDecimal max, boolean leftInclusive,
        boolean rightInclusive) {
        if (!isNumber(value, true)) {
            return false;
        }

        return isInRange(NumberUtil.toBigDecimal(value), min, max, leftInclusive, rightInclusive);
    }

    /**
     * 增强型范围校验
     *
     * @param num          输入值
     * @param min            最小值
     * @param max            最大值
     * @param leftInclusive  是否包含左边界
     * @param rightInclusive 是否包含右边界
     */
    public static boolean isInRange(
        BigDecimal num, BigDecimal min, BigDecimal max, boolean leftInclusive, boolean rightInclusive) {

        boolean lowerBound = leftInclusive ? num.compareTo(min) >= 0 : num.compareTo(min) > 0;

        boolean upperBound = rightInclusive ? num.compareTo(max) <= 0 : num.compareTo(max) < 0;

        return lowerBound && upperBound;
    }

    // 自动交换min/max值
    public static boolean safeRangeCheck(String value, BigDecimal a, BigDecimal b, RangeType type) {
        if (a.compareTo(b) > 0) {
            return isInRangeByType(value, b, a, type);
        }
        return isInRangeByType(value, a, b, type);
    }

    // 带精度控制的区间校验
    public static boolean isInRangeWithScale(String value, BigDecimal min, BigDecimal max, RangeType type, int scale) {
        BigDecimal num = NumberUtil.toBigDecimal(value).setScale(scale, RoundingMode.HALF_UP);
        min = min.setScale(scale, RoundingMode.HALF_UP);
        max = max.setScale(scale, RoundingMode.HALF_UP);
        return isInRangeByType(num.toPlainString(), min, max, type);
    }

    /**
     * 预定义区间类型校验
     */
    public static boolean isInRangeByType(String value, BigDecimal min, BigDecimal max, RangeType type) {
        return switch (type) {
            case CLOSED -> isInRange(value, min, max, true, true);
            case OPEN -> isInRange(value, min, max, false, false);
            case LEFT_OPEN -> isInRange(value, min, max, false, true);
            case RIGHT_OPEN -> isInRange(value, min, max, true, false);
        };
    }

    /**
     * 预定义区间类型校验
     */
    public static boolean isInRangeByType(BigDecimal value, BigDecimal min, BigDecimal max, RangeType type) {
        return switch (type) {
            case CLOSED -> isInRange(value, min, max, true, true);
            case OPEN -> isInRange(value, min, max, false, false);
            case LEFT_OPEN -> isInRange(value, min, max, false, true);
            case RIGHT_OPEN -> isInRange(value, min, max, true, false);
        };
    }

    /**
     * 区间类型枚举
     */
    public enum RangeType {
        CLOSED,         // [a, b]
        OPEN,           // (a, b)
        LEFT_OPEN,      // (a, b]
        RIGHT_OPEN      // [a, b)
    }

    /**
     * 非负数校验（≥0）
     */
    public static boolean isNonNegative(String str) {
        return isNumber(str, true) && checkSign(str, SignType.POSITIVE, true);
    }


    /**
     * 非正数校验（≤0）
     */
    public static boolean isNonPositive(String str) {
        return isNumber(str, true) && checkSign(str, SignType.NEGATIVE, true);
    }

    /**
     * 严格正数校验（＞0）
     */
    public static boolean isPositive(String str) {
        return isNumber(str, true) && checkSign(str, SignType.POSITIVE, false);
    }

    /**
     * 严格负数校验（＜0）
     */
    public static boolean isNegative(String str) {
        return isNumber(str, true) && checkSign(str, SignType.NEGATIVE, false);
    }

    /**
     * 正整数校验（不含零）
     */
    public static boolean isPositiveInteger(String str) {
        return isInteger(str, SignType.POSITIVE) && !NumberUtil.equals(NumberUtil.parseNumber(str), BigDecimal.ZERO);
    }

    /**
     * 非负整数校验（≥0）
     */
    public static boolean isNonNegativeInteger(String str) {
        return isInteger(str, SignType.POSITIVE) || NumberUtil.equals(NumberUtil.parseNumber(str), BigDecimal.ZERO);
    }

    /**
     * 自然数校验（≥0 整数）
     */
    public static boolean isNaturalNumber(String str) {
        return isNumber(str, true) && isNonNegativeInteger(str);
    }

    /**
     * 严格零值校验（等于0）
     */
    public static boolean isZero(String str) {
        return isNumber(str, true) && checkSign(str, SignType.ZERO, true);
    }

    public static boolean isPositiveOrZero(String str) {
        return isNumber(str, true) && (isZero(str) || isPositive(str));
    }

    public static boolean isPositiveOrZero(BigDecimal num) {
        return num != null && (num.compareTo(BigDecimal.ZERO) >= 0);
    }

    public static boolean isNonPositiveOrZero(String str) {
        return !isPositiveOrZero(str);
    }

    public static boolean isNonPositiveOrZero(BigDecimal num) {
        return !isPositiveOrZero(num);
    }

    /**
     * 非零校验（≠0）
     */
    public static boolean isNonZero(String str) {
        return isNumber(str, true) && !NumberUtil.equals(NumberUtil.parseNumber(str), BigDecimal.ZERO);
    }

    /**
     * 截取指定位数的小数
     * 
     * @param value 需要处理的数值
     * @param scale 保留的小数位数
     * @param roundingMode
     * @return 截取后的BigDecimal
     */
    public static BigDecimal truncateDecimal(BigDecimal value, int scale, RoundingMode roundingMode) {
        if (value == null) {
            return null;
        }
        if (scale < 0) {
            throw new IllegalArgumentException(THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO);
        }
        return value.setScale(scale, roundingMode);
    }

    /**
     * 截取指定位数的小数（不进行四舍五入）
     * 
     * @param valueStr 需要处理的数值字符串
     * @param scale 保留的小数位数
     * @param roundingMode
     * @return 截取后的字符串，如果输入无效则返回原字符串
     */
    public static String truncateDecimal(String valueStr, int scale, RoundingMode roundingMode) {
        if (valueStr == null || valueStr.isEmpty() || !isNumber(valueStr, false)) {
            return valueStr;
        }
        if (scale < 0) {
            throw new IllegalArgumentException(THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO);
        }
        try {
            BigDecimal value = new BigDecimal(valueStr);
            // 只有当小数位数超过指定位数时才进行截取
            if (value.scale() > scale) {
                return value.setScale(scale, roundingMode).toPlainString();
            }
            return valueStr;
        } catch (NumberFormatException e) {
            log.warn("无法将字符串转换为数字: {}", valueStr);
            return valueStr;
        }
    }

    /**
     * 检查字符串是否为有效数字并且小数位数不超过指定位数
     * 
     * @param valueStr 需要检查的字符串
     * @param scale 允许的最大小数位数
     * @return 如果是有效数字且小数位数不超过指定位数则返回true
     */
    public static boolean chkDecimalScale(String valueStr, int scale) {
        if (valueStr == null || valueStr.isEmpty() || !isNumber(valueStr, false)) {
            return false;
        }
        if (scale < 0) {
            throw new IllegalArgumentException(THE_SCALE_MUST_BE_A_POSITIVE_INTEGER_OR_ZERO);
        }
        try {
            BigDecimal value = new BigDecimal(valueStr);
            return value.scale() <= scale;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 正则表达式：带有最多2位小数的正数
     */
    public static final String POSITIVE_DECIMAL_2_REGEX = "^[+]?(0|[1-9]\\d*)(\\.\\d{1,2})?$";

    /**
     * 检查是否为带有最多2位小数的正数
     * 
     * @param str 需要检查的字符串
     * @return 如果是带有最多2位小数的正数则返回true
     */
    public static boolean chkPositiveDecimal2(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches(POSITIVE_DECIMAL_2_REGEX);
    }


    /**
     * 计算曼哈顿距离，宫格两点之间距离
     *
     * @param x1
     * @param y1
     * @param x2
     * @param y2
     * @return
     */
    public static int calculateShortestDistance(int x1, int y1, int x2, int y2) {
        return Math.abs(x2 - x1) + Math.abs(y2 - y1);
    }

}
