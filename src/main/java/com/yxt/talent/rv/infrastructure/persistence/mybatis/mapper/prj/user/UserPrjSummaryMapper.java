package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.UserPrjSummaryPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface UserPrjSummaryMapper extends CommonMapper<UserPrjSummaryPO> {

    default long insertOrUpdate(@Nonnull UserPrjSummaryPO entity) {
        return batchInsertOrUpdate(List.of(entity));
    }

    default void insertOrUpdateBatch(@Nonnull Collection<UserPrjSummaryPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    long batchInsertOrUpdate(@Param("list") List<UserPrjSummaryPO> list);

    @Nonnull
    List<UserPrjSummaryPO> selectByUserId(
            @Param("orgId") String orgId, @Param("userId") String userId);

    long deleteByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    List<UserPrjSummaryPO> selectByOrgIdAndUserIds(
            @Param("orgId") String orgId, @Param("userIds") Set<String> userIds);

    @Nullable
    UserPrjSummaryPO selectByOrgIdAndUserId(
            @Param("orgId") String orgId, @Param("userId") String userId);

    long deleteByOrgIdAndPrjId(@Param("orgId") String orgId, @Param("prjId") String prjId);

    List<UserPrjSummaryPO> selectByOrgId(@Param("orgId") String orgId);

    void deleteBatch(@Param("orgId") String orgId, @Param("removeIds") List<String> removeIds);


    void cleanPrjResult(@Param("orgIds") Set<String> orgIds);
}
