package com.yxt.talent.rv.infrastructure.common.constant;

import lombok.experimental.UtilityClass;

@UtilityClass
public final class AuthCodes {
    // @formatter:off
    public static final String AUTH_CODE_ALL = "*";

    // -----------------------------盘点项目跟踪管理------------------------------
    public static final String PRJ_USER_ADD = "sp_gwnl_talentrv_project|sp_talentrv_projectmember_add";
    public static final String PRJ_USER_DEL = "sp_gwnl_talentrv_project|sp_talentrv_projectmember_del";
    public static final String PRJ_TOOL_LIST = "sp_gwnl_talentrv_project|sp_talentrv_projectschemelist_get";
    public static final String PRJ_EVAL_RELATION_SET = "sp_gwnl_talentrv_project|sp_talentrv_projectscheme_setrelation";
    public static final String PRJ_USER_RESULT_IMPORT = "sp_gwnl_talentrv_project|sp_talentrv_projectscheme_input";
    public static final String PRJ_START = "sp_gwnl_talentrv_project|sp_talentrv_projectoperate_start";
    public static final String PRJ_END = "sp_gwnl_talentrv_project|sp_talentrv_projectoperate_end";
    public static final String PRJ_RESULT_EXPORT = "sp_gwnl_talentrv_project|sp_talentrv_projectoperate_out";
    public static final String PRJ_CALCULATE = "sp_gwnl_talentrv_project|sp_talentrv_projectoperate_calculate";
    public static final String PRJ_RESULT_GET = "sp_gwnl_talentrv_project|sp_talentrv_projectresult_get";

    // -----------------校准会管理（老）---------------------
    public static final String CALI_MEET_LIST = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationlist_get";
    public static final String CALI_MEET_ADD = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationlist_add";
    public static final String CALI_MEET_DEL = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationlist_del";
    public static final String CALI_MEET_END = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationlist_end";

    // --------------------绩效-------------------------
    public static final String PERF_USER_LIST = "sp_gwnl_talentrv_achievements|sp_talentrv_achievementslist_get";
    public static final String PERF_IMPORT = "sp_gwnl_talentrv_achievements|sp_talentrv_achievementsoperate_input";
    public static final String PERF_CYCLE_SET = "sp_gwnl_talentrv_achievements|sp_talentrv_achievementsoperate_cycle";
    public static final String PERF_SET = "sp_gwnl_talentrv_achievements|sp_achievements_setting_operation";
    public static final String PERF_CLEAR = "sp_gwnl_talentrv_achievements|sp_talentrv_achievement_drop";

    // --------------------维度-------------------------
    public static final String PRJ_DIM_LIST = "sp_gwnl_talentrv_setting|sp_talentrv_dimensionsetting_getlist";
    public static final String PRJ_DIM_ENABLE = "sp_gwnl_talentrv_setting|sp_talentrv_dimensionsetting_enable";
    public static final String PRJ_DIM_DISABLE = "sp_gwnl_talentrv_setting|sp_talentrv_dimensionsetting_disable";
    public static final String PRJ_DIM_DEL = "sp_gwnl_talentrv_setting|sp_talentrv_dimensionsetting_del";

    // --------------------分类-------------------------
    public static final String CAT_EDIT = "sp_gwnl_talentrv_setting|sp_talentrv_classsetting_edit";
    public static final String CAT_ENABLE = "sp_gwnl_talentrv_setting|sp_talentrv_classsetting_enable";
    public static final String CAT_DISABLE = "sp_gwnl_talentrv_setting|sp_talentrv_classsetting_disable";
    public static final String CAT_DEL = "sp_gwnl_talentrv_setting|sp_talentrv_classsetting_del";

    // ---------------校准会跟踪管理--------------------
    public static final String CALI_MEET_TRAIN_GET = "sp_gwnl_talentrv_project|sp_talentrv_projecttraininglist_get";
    public static final String CALI_MEET_TRAIN_INIT = "sp_gwnl_talentrv_project|sp_talentrv_projecttraining_initiate";
    public static final String CALI_MEET_GET = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationinfo_get";
    public static final String CALI_MEET_USER_GET = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationmemberlist_get";
    public static final String CALI_MEET_USER_DEL = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationmemberlist_del";
    public static final String CALI_MEET_USER_SET = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationmember_set";
    public static final String CALI_MEET_USER_IMPORT = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationoperate_input";
    public static final String CALI_MEET_USER_EXPORT = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationoperate_out";
    public static final String CALI_MEET_GRID_GET = "sp_gwnl_talentrv_calibration|sp_talentrv_calibrationoresult_getninepic";

    // -------------------人岗匹配项目-----------------------
    public static final String DMP_CREATE = "sp_gwnl_dynamic_matching|sp_dynamic_matching_creat";
    public static final String DMP_TRACK = "sp_gwnl_dynamic_matching|sp_dynamic_project_track";
    public static final String DMP_PUBLISH = "sp_gwnl_dynamic_matching|sp_dynamic_project_publish";
    public static final String DMP_RUSHTAK = "sp_gwnl_dynamic_matching|sp_dynamic_project_rushtak";
    public static final String DMP_END = "sp_gwnl_dynamic_matching|sp_dynamic_project_end";
    public static final String DMP_COPY = "sp_gwnl_dynamic_matching|sp_dynamic_project_copy";
    public static final String DMP_PAUSE = "sp_gwnl_dynamic_matching|sp_dynamic_project_pause";
    public static final String DMP_DEL = "sp_gwnl_dynamic_matching|sp_dynamic_project_del";
    public static final String DMP_WITHDRAW = "sp_gwnl_dynamic_matching|sp_dynamic_project_withdraw";
    public static final String DMP_AUTHORITY = "sp_gwnl_dynamic_matching|sp_dynamic_project_authority";
    public static final String DMP_OVERVIEW = "sp_gwnl_dynamic_matching|sp_dynamic_project_overview";
    public static final String DMP_DESIGN = "sp_gwnl_dynamic_matching|sp_dynamic_project_design";
    public static final String DMP_RULES = "sp_gwnl_dynamic_matching|sp_dynamic_project_rules";
    public static final String DMP_PERSON = "sp_gwnl_dynamic_matching|sp_dynamic_project_person";
    public static final String DMP_SETTING = "sp_gwnl_dynamic_matching|sp_dynamic_project_setting";

    // -------------------XPD项目-----------------------

    // -------------------校准会(新)-----------------------
    public static final String SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT = "sp_talentrv_calibrationmemberlist_dep_extent";

    // -------------------Activity活动模型-----------------------


}
