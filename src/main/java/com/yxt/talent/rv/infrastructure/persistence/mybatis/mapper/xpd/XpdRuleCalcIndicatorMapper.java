package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcIndicatorPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XpdRuleCalcIndicatorMapper extends CommonMapper<XpdRuleCalcIndicatorPO> {
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(XpdRuleCalcIndicatorPO record);

    int insertOrUpdate(XpdRuleCalcIndicatorPO record);

    int batchInsert(@Param("list") List<XpdRuleCalcIndicatorPO> list);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    XpdRuleCalcIndicatorPO selectByPrimaryKey(String id);

    List<XpdRuleCalcIndicatorPO> getByXpdId(
            @Param("orgId") String orgId,
            @Param("xpdId") String xpdId);

    /**
     * 根据项目规则ID查所有的指标
     * 项目计算方式：按指标结果计算
     *
     * @param orgId     机构ID
     * @param xpdRuleId 项目规则ID
     * @return list
     */
    List<XpdRuleCalcIndicatorPO> listByXpdRuleId(@Param("orgId") String orgId,
                                                 @Param("xpdRuleId") String xpdRuleId);

    /**
     * 批量逻辑删
     *
     * @param orgId     机构ID
     * @param xpdRuleId 项目规则ID
     * @param userId    操作人ID
     */
    void deleteByXpdRuleId(@Param("orgId") String orgId,
                           @Param("xpdRuleId") String xpdRuleId,
                           @Param("userId") String userId);

    void deleteByXpdId(@Param("orgId") String orgId,
                       @Param("userId") String userId,
                       @Param("xpdId") String xpdId);
}
