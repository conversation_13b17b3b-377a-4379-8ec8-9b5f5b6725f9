package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.ai;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolSessionPO;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

public interface AiToolSessionMapper extends CommonMapper<AiToolSessionPO> {

    int insertOrUpdate(AiToolSessionPO data);

    @Nullable
    AiToolSessionPO selectByOrgIdAndId(@Param("orgId") String orgId, @Param("id") Long id);

    @Nullable
    AiToolSessionPO selectByOrgIdAndSessionId(
        @Param("orgId") String orgId, @Param("sessionId") String sessionId);

    Collection<AiToolSessionPO> selectByOrgIdAndUserId(
        @Param("orgId") String orgId, @Param("userId") String userId);

}