package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.RvActivityParticipationRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动/项目参与人员关系表 Mapper
 */
public interface RvActivityParticipationRelationMapper extends CommonMapper<RvActivityParticipationRelationPO> {

    /**
     * 根据活动ID查询项目负责人
     *
     * @param orgId 机构ID
     * @param actvId 活动ID
     * @return 项目负责人列表
     */
    List<RvActivityParticipationRelationPO> findManagersByActvId(
            @Param("orgId") String orgId,
            @Param("actvId") String actvId);

    /**
     * 批量查询多个活动的项目负责人
     *
     * @param orgId 机构ID
     * @param actvIds 活动ID列表
     * @return 项目负责人列表
     */
    List<RvActivityParticipationRelationPO> findManagersByActvIds(
            @Param("orgId") String orgId,
            @Param("actvIds") List<String> actvIds);
}
