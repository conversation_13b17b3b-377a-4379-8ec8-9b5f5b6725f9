package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.controller.client.general.calimeet.query.CaliMeetClientQuery;
import com.yxt.talent.rv.controller.client.general.calimeet.viewobj.CaliMeetClientVO;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetQuery;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface CaliMeetMapper extends CommonMapper<CaliMeetPO> {

    void updateById(@Nonnull CaliMeetPO caliMeetPo);

    int countByOrgIdAndProjectIdAndMeetStatus(
            @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("projectId") String projectId);

    int countByOrgIdAndProjectId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("projectId") String projectId);

    @Nullable
    CaliMeetPO selectByIdAndOrgIdAndDeleted(
            @Nonnull @Param("id") String id, @Nonnull @Param("orgId") String orgId,
            @Param("deleted") Integer deleted);

    int countByOrgIdAndId(@Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    @Nullable
    CaliMeetPO selectByIdAndOrgId(
            @Nonnull @Param("id") String id, @Nonnull @Param("orgId") String orgId);

    int countByOrgIdAndProjectIdAndMeetNameAndIdNot(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("projectId") String projectId, @Param("meetName") String meetName,
            @Param("notId") String notId);

    @Nonnull
    List<CaliMeetPO> listByMeetStatusAndIdIn(
            @Param("meetStatus") Integer meetStatus,
            @Param("idCollection") Collection<String> idCollection);

    @Nonnull
    IPage<CaliMeetPO> paging(
            @Nonnull IPage<CaliMeetPO> page, @Nonnull @Param("search") CaliMeetQuery search,
            @Nonnull @Param("orgId") String orgId, @Param("nowDate") Date nowDate);

    @Nonnull
    IPage<CaliMeetClientVO> pagingVO(
            @Nonnull IPage<CaliMeetClientVO> page,
            @Nonnull @Param("search") CaliMeetClientQuery search,
            @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("userId") String userId, @Param("nowDate") Date nowDate);

    @Nonnull
    List<CaliMeetPO> listByMeetTimeRange(
            @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Nonnull
    List<CaliMeetPO> listByOrgId(@Nonnull @Param("orgId") String orgId);

    void insertOrUpdate(@Param("element") CaliMeetPO element);

    List<String> getAllPrjCreators(@Param("orgId") String orgId);

    int countByUserScope(@Param("orgId") String orgId, @Param("userId") String userId);

    void transferResource(
            @Param("orgId") String orgId, @Param("fromUserId") String fromUserId,
            @Param("toUserId") String toUserId);

    default void insertOrUpdateBatch(Collection<CaliMeetPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    long batchInsertOrUpdate(@Param("list") Collection<CaliMeetPO> list);

    void deleteMeetingByProjectId(@Param("orgId") String orgId,
            @Param("projectId") String projectId, @Param("userId") String userId);

}
