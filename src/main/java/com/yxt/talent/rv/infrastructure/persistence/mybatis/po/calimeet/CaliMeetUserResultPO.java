package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 盘点校准会用户结果表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_calibration_meeting_result")
public class CaliMeetUserResultPO {
    // 主键
    @TableId
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 盘点项目id
    @TableField("project_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String projectId;

    // 会议Id
    @TableField("meeting_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_CALIMEET_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String meetingId;

    // 维度id
    @TableField("dimension_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_DIM_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String dimensionId;

    // 用户id
    @TableField("user_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String userId;

    // 原始结果（0-默认，1-低，2-中，3-高）
    @TableField("init_level")
    private Integer originalLevel = 0;

    @TableField("grid_level")
    private String gridLevel;

    // 原始分数
    @TableField("init_score")
    private BigDecimal originalScore = BigDecimal.valueOf(-1);

    // 校准结果（0-默认，1-低，2-中，3-高）
    @TableField("last_level")
    private Integer calibrationLevel = 0;

    @TableField("cali_level")
    private String caliLevel;

    // 数据来源（0-初始化 1-校准）
    @TableField("init_type")
    private Integer initType = 0;

    // 校准分数
    @TableField("last_score")
    private BigDecimal calibrationScore = BigDecimal.valueOf(-1);

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
