package com.yxt.talent.rv.infrastructure.service.audit.dto;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/8/6 14:07
 **/

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class RvBindLogDTO {

    @AuditLogField(name = "人员列表", orderIndex = 1)
    private String user;

    @AuditLogField(name = "培训项目", orderIndex = 2)
    private String projectName;



}
