package com.yxt.talent.rv.infrastructure.common.utilities.circuit;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * 方法断路器, 用于添加在方法上, 通过与@CircuitCondition注解配置实现方法的短路处理
 *
 */
@Documented
@Target({METHOD, TYPE})
@Retention(RUNTIME)
public @interface ShortCircuit {

    /**
     * 指定处理方式
     */
    Action action() default Action.RETURN_DEFAULT;

    /**
     * SpEL表达式，用于自定义返回值（仅当action为RETURN_CUSTOM时有效）
     */
    String customReturn() default "";

    /**
     * 自定义短路异常, 支持SpEL表达式, 方便将当前上下文调试信息打印出来
     *
     * @return
     */
    String message() default "Short Circuit!!!";

    enum Action {
        /* 返回默认值 */
        RETURN_DEFAULT,

        /* 抛出异常 */
        THROW_EXCEPTION,

        /* 返回自定义值（通过SpEL表达式指定） */
        RETURN_CUSTOM,
    }

}
