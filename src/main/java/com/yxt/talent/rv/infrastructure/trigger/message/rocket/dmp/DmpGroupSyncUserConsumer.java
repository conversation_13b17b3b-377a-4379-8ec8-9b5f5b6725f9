package com.yxt.talent.rv.infrastructure.trigger.message.rocket.dmp;

import com.alibaba.fastjson2.JSON;
import com.yxt.event.EventPublisher;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.domain.common.event.DynamicGroupUserChangeDataMessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_SE_C_UDP_DYNAMIC_GROUP_USER_CHANGE;

/**
 * @Description 同步udp动态用户组人员队列
 * <AUTHOR>
 * @Date 2024/5/7 19:11
 **/
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(         consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_SE_C_UDP_DYNAMIC_GROUP_USER_CHANGE,         topic = TOPIC_SE_C_UDP_DYNAMIC_GROUP_USER_CHANGE,         consumeThreadNumber = 2, consumeTimeout = 30)
public class DmpGroupSyncUserConsumer implements RocketMQListener<DynamicGroupUserChangeDataMessageEvent> {

    private final EventPublisher eventPublisher;

    @Override
    public void onMessage(DynamicGroupUserChangeDataMessageEvent event) {
        log.info("LOG13295:DmpGroupSyncUserConsumer msg={}", JSON.toJSONString(event));
        try {
            eventPublisher.publish(event);
        } catch (Exception e){
            log.error("LOG14645:DmpGroupSyncUserConsumer error:", e);
        }

    }
}
