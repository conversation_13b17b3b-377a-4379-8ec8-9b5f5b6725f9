package com.yxt.talent.rv.infrastructure.service.auth.tinahe;

import com.yxt.talent.rv.controller.manage.auth.viewobj.TianheUserVo;
import com.yxt.talent.rv.infrastructure.common.utilities.util.jwt.JwtUserInfo;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.service.auth.AuthenticateService;
import com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto.TianheLogin;
import com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto.TianheToken;
import com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto.TianheUser;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static com.yxt.common.util.ApiUtil.getRequestByContext;
import static com.yxt.common.util.ApiUtil.getToken;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.getFirstNotNull;
import static com.yxt.talent.rv.infrastructure.config.AppProperties.TianHe.GRANT_TYPE;
import static com.yxt.talent.rv.infrastructure.service.auth.tinahe.TianheResult.resolve;

@Service
@RequiredArgsConstructor
public class TianheAuthService {

    private final TianheApiClient tianheApiClient;
    private final AppProperties appProperties;
    private final AuthenticateService authenticateService;

    /**
     * 天和登陆
     *
     * @param input
     * @return
     */
    public TianheUserVo login(TianheLogin input) {
        TianheToken tianheToken = this.acquireToken(input.getCode());
        TianheUser authUser = acquireUserInfo(tianheToken);
        JwtUserInfo claim = getJwtClaim(authUser);
        String jwt = authenticateService.generateToken(claim);
        return TianheUserVo.of(authUser, jwt);
    }

    private static JwtUserInfo getJwtClaim(TianheUser authUser) {
        return JwtUserInfo.builder()
            .orgId(authUser.getTenantId())
            .userId(authUser.getUserId())
            .userName(authUser.getAccountNo())
            .fullName(authUser.getUsername())
            .build();
    }

    /**
     * 天和登录
     */
    public TianheToken acquireToken(String code) {
        AppProperties.TianHe tianHe = appProperties.getTianHe();
        String appId = tianHe.getAppId();
        String sk = tianHe.getSk();
        String authType = getFirstNotNull(tianHe.getGrantType(), GRANT_TYPE);
        return resolve(() -> tianheApiClient.acquireToken(authType, appId, sk, code));
    }

    /**
     * 根据token获取登录人信息
     */
    public TianheUser acquireUserInfo(TianheToken tianheToken) {
        return resolve(() -> tianheApiClient.acquireUser(tianheToken.getAccessToken()));
    }

    /**
     * 天和登出
     */
    public void logout() {
        resolve(() -> tianheApiClient.logout(getToken(getRequestByContext())));
    }

    /**
     * 天和登出
     */
    public void logout(String token) {
        resolve(() -> tianheApiClient.logout(token));
    }
}
