package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet;

import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserSimpleVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetAttendeePO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CaliMeetAttendeeMapper {

    @Nonnull
    List<CaliMeetAttendeePO> listByOrgId(@Nonnull @Param("orgId") String orgId);

    @Nonnull
    List<CaliMeetUserSimpleVO> listByMeetingIdAndUserType(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userType") Integer userType);

    @Nonnull
    List<CaliMeetAttendeePO> listByOrgIdAndMeetingIdAndUserType(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userType") Integer userType);

    @Nonnull
    List<CaliMeetAttendeePO> listByOrgIdAndMeetingId(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId);

    int deleteByOrgIdAndCalimeetIdAndUserType(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userType") Integer userType);

    int insertList(@Nonnull @Param("list") List<CaliMeetAttendeePO> list);

    void transferResource(
            @Param("orgId") String orgId, @Param("fromUserId") String fromUserId,
            @Param("toUserId") String toUserId);

    List<String> listByOrgIdAndUserId4Org(@Param("orgId") String orgId, @Param("userIds") List<String> userIds);

    List<String> listAllUserIds(@Param("orgId") String orgId, @Param("userType") Integer userType);

}
