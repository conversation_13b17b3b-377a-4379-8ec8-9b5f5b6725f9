package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 维度配置关系表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_dimension_config")
public class PrjDimConfPO implements Serializable {
    // 主键
    @TableId
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_DIM_CONFIG_ID)
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 项目id
    @TableField("project_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_ID)
    private String projectId = "";

    // 维度id
    @TableField("dimension_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_DIM_ID)
    private String dimensionId = "";

    // 维度名称
    private String dimensionName = "";

    // 维度状态（0-未配置，1-已配置）
    @TableField("dimension_status")
    private Integer dimensionStatus = 0;

    // 模型id
    @TableField("model_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_SKILL_MODEL_ID)
    private String modelId = "";

    // 废弃，需查询rv_dimension_config_tool表
    @TableField("tool_id")
    private String toolId = "";

    // 盘点工具类型（0-未配置，1-绩效，2-测评，3-盘点数据导入,4：倍智测评）
    @TableField("tool_type")
    private Integer toolType = 0;

    // 排序，默认从0开始
    @TableField("order_index")
    private Integer orderIndex = 0;

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
