package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRuleScorePO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface DmpRuleScoreMapper extends CommonMapper<DmpRuleScorePO> {

    long insertOrUpdate(@Nonnull DmpRuleScorePO entity);

    int updateBatch(@Nonnull @Param("list") Collection<DmpRuleScorePO> list);

    default void batchUpdate(Collection<DmpRuleScorePO> list) {
        batchExecute(list, this::updateBatch);
    }

    int batchInsert(@Nonnull @Param("list") Collection<DmpRuleScorePO> list);

    /**
     * 根据匹配规则id删除维度分设置
     *
     * @param orgId
     * @param matchRuleId
     * @param userId
     */
    void deleteByMatchRuleId(
            @Nonnull @Param("orgId") String orgId, @Param("matchRuleId") String matchRuleId,
            @Nonnull @Param("userId") String userId);

    /**
     * 根据匹配规则id查询
     *
     * @param orgId
     * @param matchRuleId
     */
    @Nonnull
    List<DmpRuleScorePO> selectByMatchRuleId(
            @Nonnull @Param("orgId") String orgId, @Param("matchRuleId") String matchRuleId);

    @Nonnull
    List<DmpRuleScorePO> selectByDmpId(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId);

    @Nonnull
    List<DmpRuleScorePO> selectByDimIds(
            @Nonnull @Param("orgId") String orgId, @Param("dimIds") List<String> dimIds);

    List<DmpRuleScorePO> selectByOrgId(@Param("orgId") String orgId);
}
