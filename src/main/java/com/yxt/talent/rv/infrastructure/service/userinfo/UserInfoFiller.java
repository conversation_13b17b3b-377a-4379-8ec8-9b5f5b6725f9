package com.yxt.talent.rv.infrastructure.service.userinfo;

import com.yxt.common.repo.RedisRepository;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserInfoFill.UserInfoFillType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;

/**
 * 用户信息填充工具类，支持使用注解的方式标识需要填充的字段
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserInfoFiller {
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final RedisRepository talentRedisRepository;
    private final I18nTranslator i18nTranslator;

    /**
     * 统一的用户信息填充方法，支持对象和集合
     *
     * @param orgId 组织ID
     * @param obj   需要填充的对象或对象集合
     */
    public void fill(String orgId, Object obj) {
        if (obj == null) {
            return;
        }

        // 处理集合类型
        if (obj instanceof Collection<?> collection) {
            if (!collection.isEmpty()) {
                // 检查集合中的第一个非空元素
                Object firstItem = collection.stream().filter(Objects::nonNull).findFirst().orElse(null);
                if (firstItem != null) {
                    // 收集所有需要填充的字段信息
                    List<UserInfoFieldInfo> allFields = new ArrayList<>();
                    collection.forEach(item -> collectUserInfoFields(item, allFields));
                    
                    // 批量填充所有用户信息
                    if (!allFields.isEmpty()) {
                        log.debug("LOG30001: 批量填充用户信息, 字段数量: {}", allFields.size());
                        fillUserInfoFieldsBatch(allFields, orgId);
                    }
                }
            }
            return;
        }

        // 处理Map类型
        if (obj instanceof Map<?, ?>) {
            ((Map<?, ?>) obj).values().forEach(value -> fill(orgId, value));
            return;
        }

        // 处理普通对象类型
        List<UserInfoFieldInfo> userInfoFields = new ArrayList<>();
        collectUserInfoFields(obj, userInfoFields);
        if (!userInfoFields.isEmpty()) {
            fillUserInfoFieldsBatch(userInfoFields, orgId);
        }
    }

    /**
     * 批量填充用户信息
     */
    private void fillUserInfoFieldsBatch(List<UserInfoFieldInfo> allFields, String orgId) {
        try {
            // 1. 按字段类型分组
            Map<UserInfoFillType, List<UserInfoFieldInfo>> typeFieldsMap = allFields.stream()
                    .collect(Collectors.groupingBy(UserInfoFieldInfo::getType));
            
            // 2. 收集所有需要查询的用户ID
            Set<String> allUserIds = allFields.stream()
                    .map(UserInfoFieldInfo::getUserId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            
            if (allUserIds.isEmpty()) {
                return;
            }

            // 3. 尝试从缓存获取用户信息
            Map<String, UdpLiteUserPO> userInfoCache = getUserInfoFromCache(orgId, allUserIds);
            
            // 4. 对于缓存中不存在的用户，从数据库查询
            Set<String> uncachedUserIds = new HashSet<>(allUserIds);
            uncachedUserIds.removeAll(userInfoCache.keySet());
            
            if (!uncachedUserIds.isEmpty()) {
                List<UdpLiteUserPO> dbUserInfoList = udpLiteUserMapper.selectByUserIds(orgId, uncachedUserIds);
                if (CollectionUtils.isNotEmpty(dbUserInfoList)) {
                    // 将查询结果添加到缓存
                    for (UdpLiteUserPO userInfo : dbUserInfoList) {
                        userInfoCache.put(userInfo.getId(), userInfo);
                        // 更新缓存
                        updateUserInfoCache(orgId, userInfo);
                    }
                }
            }

            // 5. 按不同类型进行填充
            for (Map.Entry<UserInfoFillType, List<UserInfoFieldInfo>> entry : typeFieldsMap.entrySet()) {
                UserInfoFillType type = entry.getKey();
                List<UserInfoFieldInfo> fields = entry.getValue();
                
                for (UserInfoFieldInfo field : fields) {
                    UdpLiteUserPO userInfo = userInfoCache.get(field.getUserId());
                    if (userInfo == null) {
                        continue;
                    }
                    
                    fillFieldByType(field, userInfo, type);
                }
            }
            
            log.debug("LOG30002: 用户信息填充完成, 用户数: {}", userInfoCache.size());
        } catch (Exception e) {
            log.error("LOG30003: 填充用户信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据类型填充字段
     */
    private void fillFieldByType(UserInfoFieldInfo fieldInfo, UdpLiteUserPO userInfo, UserInfoFillType type) {
        try {
            Object target = fieldInfo.getTargetObject();
            Field field = fieldInfo.getField();
            
            // 确保字段可访问
            boolean accessible = field.canAccess(target);
            try {
                if (!accessible) {
                    field.setAccessible(true);
                }
                
                // 获取字段类型
                Class<?> fieldType = field.getType();
                
                // 根据填充类型和字段类型进行处理
                switch (type) {
                    case FULL_NAME:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getFullname());
                        }
                        break;
                    case USER_NAME:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getUsername());
                        }
                        break;
                    case STATUS:
                        if (fieldType.isAssignableFrom(Integer.class)) {
                            field.set(target, userInfo.getDeleted() == 0 ? 2 : userInfo.getStatus());
                        }
                        break;
                    case DEPT_ID:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getDeptId());
                        }
                        break;
                    case FULL_DEPT_NAME:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getDeptName());
                        }
                        break;
                    case SHORT_DEPT_NAME:
                        if (fieldType.isAssignableFrom(String.class)) {
                            // 提取部门名称的最后一部分，例如：总部/技术部/研发部 -> 研发部
                            String deptName = userInfo.getDeptName();
                            if (deptName != null && deptName.contains("/")) {
                                field.set(target, deptName.substring(deptName.lastIndexOf("/") + 1));
                            } else {
                                field.set(target, deptName);
                            }
                        }
                        break;
                    case POSITION_ID:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getPositionId());
                        }
                        break;
                    case POSITION_NAME:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getPositionName());
                        }
                        break;
                    case GRADE_ID:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getGradeId());
                        }
                        break;
                    case GRADE_NAME:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getGradeName());
                        }
                        break;
                    case IMG_URL:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getImgUrl());
                        }
                        break;
                    case USER_NO:
                        if (fieldType.isAssignableFrom(String.class)) {
                            field.set(target, userInfo.getUserNo());
                        }
                        break;
                    default:
                        // 对于未知类型，不做任何处理
                        log.warn("LOG21033:未知的填充类型: {}, 字段: {}", type, field.getName());
                        break;
                }
            } finally {
                // 恢复字段访问权限
                if (!accessible) {
                    field.setAccessible(accessible);
                }
            }
        } catch (Exception e) {
            log.error("LOG21043:填充字段失败: {}, 字段: {}", e.getMessage(), fieldInfo.getField().getName(), e);
        }
    }

    /**
     * 收集需要填充用户信息的字段
     */
    private void collectUserInfoFields(Object obj, List<UserInfoFieldInfo> userInfoFields) {
        if (obj == null) {
            return;
        }
        
        Set<Object> visited = new HashSet<>();
        collectUserInfoFields(obj, userInfoFields, visited);
    }

    /**
     * 递归收集需要填充用户信息的字段
     */
    private void collectUserInfoFields(Object obj, List<UserInfoFieldInfo> userInfoFields, Set<Object> visited) {
        if (obj == null || visited.contains(obj)) {
            return;
        }
        
        visited.add(obj);
        Class<?> clazz = obj.getClass();
        
        // 处理当前类的字段
        for (Field field : clazz.getDeclaredFields()) {
            UserInfoFill annotation = field.getAnnotation(UserInfoFill.class);
            if (annotation != null) {
                try {
                    processAnnotatedField(obj, field, annotation, userInfoFields);
                } catch (Exception e) {
                    log.error("LOG30005: 处理注解字段失败: {}, 字段: {}", e.getMessage(), field.getName(), e);
                }
            }
        }
        
        // 处理父类字段
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null && superClass != Object.class) {
            Field[] superFields = superClass.getDeclaredFields();
            for (Field field : superFields) {
                UserInfoFill annotation = field.getAnnotation(UserInfoFill.class);
                if (annotation != null) {
                    try {
                        processAnnotatedField(obj, field, annotation, userInfoFields);
                    } catch (Exception e) {
                        log.error("LOG30006: 处理父类注解字段失败: {}, 字段: {}", e.getMessage(), field.getName(), e);
                    }
                }
            }
        }
        
        // 递归处理复杂对象字段
        for (Field field : clazz.getDeclaredFields()) {
            if (isFieldAccessible(field)) {
                try {
                    boolean accessible = field.canAccess(obj);
                    try {
                        if (!accessible) {
                            field.setAccessible(true);
                        }
                        
                        Object fieldValue = field.get(obj);
                        if (fieldValue != null) {
                            // 处理集合类型
                            if (fieldValue instanceof Collection) {
                                Collection<?> collection = (Collection<?>) fieldValue;
                                for (Object item : collection) {
                                    if (item != null && !visited.contains(item)) {
                                        collectUserInfoFields(item, userInfoFields, visited);
                                    }
                                }
                            }
                            // 处理Map类型
                            else if (fieldValue instanceof Map) {
                                Map<?, ?> map = (Map<?, ?>) fieldValue;
                                for (Object value : map.values()) {
                                    if (value != null && !visited.contains(value)) {
                                        collectUserInfoFields(value, userInfoFields, visited);
                                    }
                                }
                            }
                            // 处理自定义对象类型（非基本类型）
                            else if (!fieldValue.getClass().getName().startsWith("java.") && 
                                    !fieldValue.getClass().isPrimitive() && 
                                    !fieldValue.getClass().isEnum()) {
                                collectUserInfoFields(fieldValue, userInfoFields, visited);
                            }
                        }
                    } finally {
                        if (!accessible) {
                            field.setAccessible(accessible);
                        }
                    }
                } catch (Exception e) {
                    log.error("LOG30007: 处理复杂字段失败: {}, 字段: {}", e.getMessage(), field.getName(), e);
                }
            }
        }
    }
    
    /**
     * 处理标注了UserInfoFill的字段
     */
    private void processAnnotatedField(Object obj, Field field, UserInfoFill annotation, List<UserInfoFieldInfo> userInfoFields) 
            throws IllegalAccessException, NoSuchFieldException {
        String userIdFieldName = annotation.userIdField();
        UserInfoFillType type = annotation.type();

        boolean accessible = field.canAccess(obj);
        try {
            if (!accessible) {
                field.setAccessible(true);
            }

            String userId;
            if (StringUtils.isBlank(userIdFieldName)) {
                // 如果没有指定用户ID字段，则使用当前字段的值作为用户ID
                Object fieldValue = field.get(obj);
                userId = fieldValue != null ? fieldValue.toString() : null;
            } else {
                // 如果指定了用户ID字段，则从指定字段获取用户ID
                Field userIdField = findField(obj.getClass(), userIdFieldName);
                boolean userIdFieldAccessible = userIdField.canAccess(obj);
                try {
                    if (!userIdFieldAccessible) {
                        userIdField.setAccessible(true);
                    }
                    Object userIdValue = userIdField.get(obj);
                    userId = userIdValue != null ? userIdValue.toString() : null;
                } finally {
                    if (!userIdFieldAccessible) {
                        userIdField.setAccessible(userIdFieldAccessible);
                    }
                }
            }
            
            if (StringUtils.isNotBlank(userId)) {
                userInfoFields.add(new UserInfoFieldInfo(field, obj, userId, type));
            }
        } finally {
            if (!accessible) {
                field.setAccessible(accessible);
            }
        }
    }
    
    /**
     * 查找字段（包括父类字段）
     */
    private Field findField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && superClass != Object.class) {
                return findField(superClass, fieldName);
            }
            throw e;
        }
    }

    /**
     * 判断字段是否可以访问
     */
    private boolean isFieldAccessible(Field field) {
        return !field.isSynthetic() && !java.lang.reflect.Modifier.isStatic(field.getModifiers());
    }
    
    /**
     * 从缓存获取用户信息
     */
    private Map<String, UdpLiteUserPO> getUserInfoFromCache(String orgId, Set<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        
        Map<String, UdpLiteUserPO> result = new HashMap<>();
        String cacheKey = String.format(RedisKeys.CK_USER_INFO_CACHE, orgId);
        
        try {
            // 批量从缓存获取
            List<Object> cacheValues = talentRedisRepository.opsForHash().multiGet(
                    cacheKey, userIds.stream().map(Object::toString).collect(Collectors.toList()));
            
            if (CollectionUtils.isNotEmpty(cacheValues)) {
                int index = 0;
                for (String userId : userIds) {
                    Object value = cacheValues.get(index++);
                    if (value instanceof String obj) {
                        // 这里假设缓存中存储的是UdpLiteUserPO序列化后的JSON字符串
                        UdpLiteUserPO userInfo = BeanHelper.json2Bean(obj, UdpLiteUserPO.class);
                        result.put(userId, userInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("LOG30008: 从缓存获取用户信息失败: {}", e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 更新用户信息缓存
     */
    private void updateUserInfoCache(String orgId, UdpLiteUserPO userInfo) {
        if (userInfo == null || StringUtils.isBlank(userInfo.getId())) {
            return;
        }
        
        try {
            String cacheKey = String.format(RedisKeys.CK_USER_INFO_CACHE, orgId);
            talentRedisRepository.opsForHash().put(cacheKey, userInfo.getId(), bean2Json(userInfo, ALWAYS));
            
            // 设置缓存过期时间（1小时）
            long expirationHours = 1;
            talentRedisRepository.expire(cacheKey, expirationHours, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("LOG30009: 更新用户信息缓存失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 用户信息字段信息类
     */
    @Data
    @AllArgsConstructor
    private static class UserInfoFieldInfo {
        /**
         * 字段
         */
        private Field field;
        
        /**
         * 目标对象
         */
        private Object targetObject;
        
        /**
         * 用户ID
         */
        private String userId;
        
        /**
         * 填充类型
         */
        private UserInfoFillType type;

    }

} 