package com.yxt.talent.rv.infrastructure.service.auth;

import lombok.Getter;


@Getter
public enum DataPermEnum {
    /**
     * 数据权限枚举
     */
    ALL_0("我创建+我管辖+我负责", 0),
    ALL("我创建+我管辖+我负责", -1),
    CREATE_BY_ME("我创建", 1),
    PERMISSION_BY_ME("我管辖", 2),
    CHARGE_BY_ME("我负责", 3),
    ;

    private final String name;

    private final int value;

    DataPermEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }
}
