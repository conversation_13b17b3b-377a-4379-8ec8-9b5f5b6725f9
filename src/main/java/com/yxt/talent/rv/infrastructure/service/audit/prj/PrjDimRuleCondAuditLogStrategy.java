package com.yxt.talent.rv.infrastructure.service.audit.prj;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.bean.EntityChange;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanHelper;
import com.yxt.spevalfacade.bean.Evaluation4Get;
import com.yxt.talent.rv.controller.manage.prj.rule.command.PrjDimRuleCreateCmd;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleExprMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleExprPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRulePO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class PrjDimRuleCondAuditLogStrategy implements AuditLogStrategy {
    private final AuthService authService;
    private final PrjDimConfMapper prjDimConfMapper;
    private final PrjDimConfToolMapper prjDimConfToolMapper;
    private final PrjDimMapper prjDimMapper;
    private final PrjDimRuleMapper prjDimRuleMapper;
    private final PrjDimRuleExprMapper prjDimRuleExprMapper;
    private final SpevalAclService spevalAclService;
    private final PerfPeriodMapper perfPeriodMapper;
    private final PrjMapper prjMapper;

    @Nullable
    PrjDimConfPO oldDimCfg;
    @Nullable
    PrjDimPO oldPrjDim;
    @Nullable
    List<PrjDimConfToolPO> oldToolList;
    @Nullable
    PrjDimRulePO oldPrjDimRule;
    @Nullable
    PrjDimRuleExprPO oldRuleExpr;

    @Override
    public void doPrepare(String[] argsNames, Object[] args) {
        HttpServletRequest request = (HttpServletRequest) args[0];
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        String orgId = operator.getOrgId();
        PrjDimRuleCreateCmd prjRuleCreate = (PrjDimRuleCreateCmd) args[1];

        oldPrjDim = null;
        oldToolList = null;
        oldPrjDimRule = null;
        oldRuleExpr = null;

        oldDimCfg = prjDimConfMapper.selectByOrgIdAndId(
                orgId, prjRuleCreate.getDimensionConfigId());
        if (oldDimCfg != null) {
            oldToolList = prjDimConfToolMapper.selectByOrgIdAndDimConfId(orgId, oldDimCfg.getId());
            oldPrjDimRule = prjDimRuleMapper.selectByDimConfId(orgId, oldDimCfg.getId());
            oldPrjDim = prjDimMapper.selectByOrgIdAndId(orgId, oldDimCfg.getDimensionId());
            if (oldPrjDimRule != null) {
                oldRuleExpr = prjDimRuleExprMapper.selectByPrjRuleId(orgId, oldPrjDimRule.getId());
            }
        }
    }

    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        log.info("LOG64340:{}", BeanHelper.bean2Json(argsNames, ALWAYS));
        if (args == null || args.length == 0) {
            log.error("LOG63910:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        PrjDimRuleCreateCmd prjRuleCreate = (PrjDimRuleCreateCmd) args[1];
        PrjPO prj =
                prjMapper.selectByOrgIdAndId(auditLog.getOrgId(), prjRuleCreate.getProjectId());
        log.debug("LOG64350:cls={}, 业务Id={}, orgId={}", this.getClass().getName(),
                prjRuleCreate.getProjectId(),
                auditLog.getOrgId());
        String bizName = prjRuleCreate.getProjectId(); // 极端情况，从库无数据，就用业务Id做名字
        if (prj != null) {
            bizName = prj.getProjectName();
        }

        // 组装日志
        AuditDetail ad = getUpdateAuditDetail(AuditLogHelper.Module.PROJECT.getCode());
        ad.setEntityId(prjRuleCreate.getProjectId());
        ad.setEntityName(AuditLogHelper.Module.PROJECT.getName() + "-" + bizName);
        ad.setChanges(generateEntityChangeList(prjRuleCreate, auditLog.getOrgId(),
                auditLog.getOperator().getId()));
        removeUnchangedProperty(ad.getChanges());
        if (CollectionUtils.isEmpty(ad.getChanges())) {
            return;
        }
        auditLog.setDetails(Collections.singletonList(ad));
        AuditLogContext.asyncCommit(auditLog);
    }

    private List<EntityChange> generateEntityChangeList(
            PrjDimRuleCreateCmd prjRuleCreate, String orgId,
            String userId) {

        List<EntityChange> list = new ArrayList<>();
        List<PrjDimConfToolPO> newToolList =
                prjDimConfToolMapper.selectByOrgIdAndDimConfId(orgId, prjRuleCreate.getDimensionConfigId());
        PrjDimRulePO newPrjDimRule =
                prjDimRuleMapper.selectByDimConfId(
                        orgId, prjRuleCreate.getDimensionConfigId());
        if (newPrjDimRule == null) {
            log.warn("LOG20030:orgId={}, prjId={}, dimensionConfigId={}", orgId,
                    prjRuleCreate.getProjectId(),
                    prjRuleCreate.getDimensionConfigId());
            return Collections.emptyList();
        }
        PrjDimRuleExprPO newRuleExpr =
                prjDimRuleExprMapper.selectByPrjRuleId(orgId, newPrjDimRule.getId());

        list.add(new EntityChange().setFieldName("盘点工具")
                .setFieldPath(PrjDimRuleCreateCmd.class.getName() + ".rvType")
                .setField("rvType")
                .setNewValue(getRvToolStr(newToolList, orgId, userId))
                .setOldValue(getRvToolStr(oldToolList, orgId, userId)));

        list.add(
                new EntityChange().setFieldName("计算公式")
                        .setFieldPath(PrjDimRuleCreateCmd.class.getName() + ".expressionShow")
                        .setField("expressionShow")
                        .setNewValue(getRuleExp(newRuleExpr))
                        .setOldValue(getRuleExp(oldRuleExpr)));

        list.add(new EntityChange().setFieldName("分级规则")
                .setFieldPath(PrjDimRuleCreateCmd.class.getName() + ".classType")
                .setField("classType")
                .setNewValue(getClassTypeStr(newPrjDimRule, newToolList))
                .setOldValue(getClassTypeStr(oldPrjDimRule, oldToolList)));

        return list;
    }

    /**
     * 分级规则 *
     */
    private String getClassTypeStr(@Nullable PrjDimRulePO rule, @Nullable List<PrjDimConfToolPO> toolList) {
        StringBuilder ctSb = new StringBuilder();
        if (rule == null) {
            return ctSb.toString();
        }

        String classTypeName = "--";
        if (rule.getClassType() == 0 && CollectionUtils.isNotEmpty(toolList) &&
            toolList.get(0).getToolType() == 1) {
            // 处理数据库默认为枚举的情况，绩效才能用枚举
            classTypeName = "枚举";
        } else if (rule.getClassType() == 1) {
            classTypeName = "百分比";
        } else if (rule.getClassType() == 2) {
            classTypeName = "绝对值";
        }

        ctSb.append(classTypeName);
        String dimensionName = oldPrjDim == null ? "" : oldPrjDim.getDimensionName(); // 维度名称
        return dimensionName + "：" + ctSb;
    }

    /**
     * 计算公式 *
     */
    private String getRuleExp(@Nullable PrjDimRuleExprPO ruleExpression) {
        StringBuilder reSb = new StringBuilder();
        if (ruleExpression == null) {
            return reSb.toString();
        }
        reSb.append(ruleExpression.getExpressionShow() == null ? " -- " :
                ruleExpression.getExpressionShow());
        String dimensionName = oldPrjDim == null ? "" : oldPrjDim.getDimensionName(); // 维度名称
        return dimensionName + "：" + reSb;
    }

    /**
     * 盘点工具 *
     */
    private String getRvToolStr(@Nullable List<PrjDimConfToolPO> toolList, String orgId, String userId) {

        StringBuilder toolNameSb = new StringBuilder();
        if (CollectionUtils.isEmpty(toolList)) {
            return toolNameSb.toString();
        }
        Integer toolType = toolList.get(0).getToolType();
        if (toolType == 2 || toolType == 4) { // 测评
            for (PrjDimConfToolPO tool : toolList) {
                Evaluation4Get evaluationDetail =
                        spevalAclService.getEvaluationDetail(tool.getToolId(), orgId, userId);
                if (evaluationDetail != null) {
                    toolNameSb.append(evaluationDetail.getName()).append("，");
                }
            }
        } else if (toolList.get(0).getToolType() == 1) { // 绩效
            List<String> toolIds =
                    toolList.stream()
                            .map(PrjDimConfToolPO::getToolId)
                            .collect(Collectors.toList());
            List<PerfPeriodPO> periods = perfPeriodMapper.selectByOrgIdAndIds(orgId, toolIds);
            for (PerfPeriodPO p : periods) {
                toolNameSb.append(p.getPeriodName()).append("，");
            }
        } else { // 导入
            toolNameSb.append("盘点数据导入，");
        }

        if (!toolNameSb.isEmpty()) {
            toolNameSb.delete(toolNameSb.length() - 1, toolNameSb.length());
        }
        String dimensionName = oldPrjDim == null ? "" : oldPrjDim.getDimensionName(); // 维度名称

        return dimensionName + "：" + toolNameSb;
    }
}
