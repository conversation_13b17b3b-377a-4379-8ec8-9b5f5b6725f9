package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.train;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.NULL_STRATEGY_EMPTY;

/**
 * 盘点项目关联培训项目关系表
 */
@Getter
@Setter
@TableName(value = "rv_project_training")
public class PrjTrainMapPO {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 盘点项目id
     */
    @TableField(value = "project_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_ID, mappedNullStrategy = NULL_STRATEGY_EMPTY)
    private String projectId;

    /**
     * 测训项目id
     */
    @TableField(value = "training_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.O2O_TRAINING_ID, mappedNullStrategy = NULL_STRATEGY_EMPTY)
    private String trainingId;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted = 0;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
