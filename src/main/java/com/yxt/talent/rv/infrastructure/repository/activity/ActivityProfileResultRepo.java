package com.yxt.talent.rv.infrastructure.repository.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultPO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/9
 */
@RequiredArgsConstructor
@Repository
public class ActivityProfileResultRepo {

    private final ActivityProfileResultMapper activityProfileResultMapper;


    public long findFinishedUserCount(String orgId, String actProfileId) {
        return activityProfileResultMapper.findFinishedUserCount(orgId, actProfileId);
    }

    public long findFinishedUserCountRangeUser(String orgId, String actProfileId, List<String> userIds) {
        return activityProfileResultMapper.findFinishedUserCountRangeUser(orgId, actProfileId, userIds);
    }

    public List<ActivityProfileResultPO> findByActProfileIdAndUserIds(String orgId, String actProfileId,
            List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        return activityProfileResultMapper.findByActProfileIdAndUserIds(orgId, actProfileId, userIds);
    }

    public void batchInsert(List<ActivityProfileResultPO> profileResultPOList) {
        if (CollectionUtils.isEmpty(profileResultPOList)) {
            return;
        }
        activityProfileResultMapper.batchInsert(profileResultPOList);
    }

    public void deleteByIds(String orgId, List<String> ids) {
        if (StringUtils.isBlank(orgId) || CollectionUtils.isEmpty(ids)) {
            return;
        }
        activityProfileResultMapper.deleteByIds(orgId, ids);
    }
}
