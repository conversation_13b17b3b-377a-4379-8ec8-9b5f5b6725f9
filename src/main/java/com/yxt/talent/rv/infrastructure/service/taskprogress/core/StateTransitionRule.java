package com.yxt.talent.rv.infrastructure.service.taskprogress.core;

/**
 * 状态转换规则接口
 * 定义了状态之间是否可以转换的规则
 */
public interface StateTransitionRule {
    /**
     * 判断状态是否可以从from转换到to
     *
     * @param from 当前状态
     * @param to   目标状态
     * @return 是否可以转换
     */
    boolean canTransit(TaskState from, TaskState to);
    
    /**
     * 判断状态码是否可以从fromCode转换到toCode
     *
     * @param fromCode 当前状态码
     * @param toCode   目标状态码
     * @param stateFactory 状态工厂
     * @return 是否可以转换
     */
    default boolean canTransitByCode(String fromCode, String toCode, TaskStateFactory stateFactory) {
        TaskState from = stateFactory.getStateByCode(fromCode);
        TaskState to = stateFactory.getStateByCode(toCode);
        return canTransit(from, to);
    }
}
