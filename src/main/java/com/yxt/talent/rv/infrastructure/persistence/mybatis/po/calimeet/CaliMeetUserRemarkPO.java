package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 盘点校准备注
 */
@Setter
@Getter
@TableName(value = "rv_meeting_user_remark")
public class CaliMeetUserRemarkPO {
    /**
     * 主键 -- GETTER -- 获取主键
     *
     * @return id - 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 机构id -- GETTER -- 获取机构id
     *
     * @return org_id - 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 盘点项目id -- GETTER -- 获取盘点项目id
     *
     * @return project_id - 盘点项目id
     */
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 校准会id -- GETTER -- 获取校准会id
     *
     * @return meeting_id - 校准会id
     */
    @TableField(value = "meeting_id")
    private String meetingId;

    /**
     * 人员id -- GETTER -- 获取人员id
     *
     * @return user_id - 人员id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 校准备注 -- GETTER -- 获取校准备注
     *
     * @return remark - 校准备注
     */
    @TableField(value = "remark")
    private String remark = "";

    /**
     * 创建人主键 -- GETTER -- 获取创建人主键
     *
     * @return create_user_id - 创建人主键
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间 -- GETTER -- 获取创建时间
     *
     * @return create_time - 创建时间
     */
    @TableField(value = "create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    /**
     * 更新人主键 -- GETTER -- 获取更新人主键
     *
     * @return update_user_id - 更新人主键
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间 -- GETTER -- 获取更新时间
     *
     * @return update_time - 更新时间
     */
    @TableField(value = "update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    /**
     * 盘点校准会名称 *
     */
    @TableField(exist = false)
    private String meetingName;
}
