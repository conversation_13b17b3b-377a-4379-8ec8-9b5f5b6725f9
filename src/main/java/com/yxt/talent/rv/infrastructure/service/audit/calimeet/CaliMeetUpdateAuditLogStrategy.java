package com.yxt.talent.rv.infrastructure.service.audit.calimeet;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.bean.EntityChange;
import com.yxt.auditlog.consts.AuditConsts;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.meet.legacy.MeetAppService;
import com.yxt.talent.rv.controller.manage.meet.command.MeetCreateCmd;
import com.yxt.talent.rv.controller.manage.common.command.AttachmentCreateCmd;
import com.yxt.talent.rv.domain.meet.meet.Meet;
import com.yxt.talent.rv.domain.meet.meet.MeetAttendee;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetAttendeeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.AttachmentMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetAttendeePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper.Module.CALIBRATION;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class CaliMeetUpdateAuditLogStrategy implements AuditLogStrategy {
    private final AuthService authService;
    private final MeetAppService caliMeetAppService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final AttachmentMapper attachmentMapper;
    private final MeetAttendeeMapper meetAttendeeMapper;
    private final MeetMapper meetMapper;

    @jakarta.annotation.Nullable
    private MeetPO oldMeeting;
    private List<String> oldOrganizerIds;
    private List<String> oldTcIds;
    private List<AttachmentPO> attachmentList;

    @Override
    public void doPrepare(String[] argsNames, Object[] args) {
        if (args == null || args.length == 0) {
            log.error("LOG63360:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        oldMeeting = null;
        oldOrganizerIds = Collections.emptyList();
        oldTcIds = Collections.emptyList();
        attachmentList = Collections.emptyList();

        HttpServletRequest request = (HttpServletRequest) args[0];
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        MeetCreateCmd bean = (MeetCreateCmd) args[1];
        if (operator == null || bean == null) {
            log.warn("LOG20060:");
            return;
        }
        oldMeeting = meetMapper.selectByIdAndOrgId(bean.getId(), operator.getOrgId());

        if (oldMeeting == null) {
            log.warn("LOG20070:id={}, orgId={}", bean.getId(), operator.getOrgId());
            return;
        }
        /*
         校准会编辑逻辑： 未开始： 可以修改基本信息; 已开始：可修改附件、会议记录。
        */
        int meetingStatus = caliMeetAppService.calcMeetingStatus(
                oldMeeting.getMeetStatus(),
                oldMeeting.getMeetTime());
        if (meetingStatus <= Meet.CaliMeetStatusEnum.DEFAULT.getCode()) { // 已开始 | 已结束
            // 准备好修改前的会议组织者和人才委员会成员
            doPrepareCmUser(operator.getOrgId(), oldMeeting.getId());
        } else {
            // 附件
            doPrepareAppendix(operator.getOrgId(), oldMeeting.getId());
            // 会议记录
            // * 会议记录在 oldMeeting 中
        }

        if (log.isInfoEnabled()) {
            log.info(
                    "LOG63370:{}", this.getClass().getName() + ": " +
                                   BeanHelper.bean2Json(oldMeeting, ALWAYS));
        }
    }

    /**
     * <pre>
     *     读取 老的会议组织者 & 老的人才委员会成员
     * </pre>
     */
    private void doPrepareCmUser(String orgId, String mtId) {
        // 老的会议组织者
        List<MeetAttendeePO> organizerList =
                meetAttendeeMapper.listByOrgIdAndMeetingIdAndUserType(orgId, mtId,
                        MeetAttendee.UserTypeEnum.ORGANIZER.getCode());
        oldOrganizerIds = organizerList.stream().map(MeetAttendeePO::getUserId)
                .collect(Collectors.toList());

        // 老的人才委员会成员
        List<MeetAttendeePO> cmUserList =
                meetAttendeeMapper.listByOrgIdAndMeetingIdAndUserType(orgId, mtId,
                        MeetAttendee.UserTypeEnum.TALENT_COMMITTEE.getCode());
        oldTcIds =
                cmUserList.stream().map(MeetAttendeePO::getUserId).collect(Collectors.toList());
    }

    /**
     * <pre>
     *     准备好 修改前附件数据
     * </pre>
     */
    private void doPrepareAppendix(String orgId, String mtId) {
        attachmentList = attachmentMapper.listByAppSourceId(orgId, mtId);
    }

    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        if (log.isInfoEnabled()) {
            log.info("LOG63380:{}", BeanHelper.bean2Json(argsNames, ALWAYS));
        }
        if (args == null || args.length == 0) {
            log.error("LOG63620:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        MeetCreateCmd bean = (MeetCreateCmd) args[1];
        if (bean == null) {
            log.warn("LOG20080:");
            return;
        }
        AuditDetail ad = new AuditDetail();

        if (oldMeeting == null) {
            log.warn("LOG20090:");
            return;
        }
        /*
         * 校准会编辑逻辑： 未开始： 可以修改基本信息; 已开始：可修改附件、会议记录。
         **/
        int meetingStatus = caliMeetAppService.calcMeetingStatus(
                oldMeeting.getMeetStatus(),
                oldMeeting.getMeetTime());
        if (meetingStatus > Meet.CaliMeetStatusEnum.DEFAULT.getCode()) { // 一 开始
            // 附件日志 新增 或 编辑
            if (CollectionUtils.isNotEmpty(attachmentList) ||
                CollectionUtils.isNotEmpty(bean.getAppendixList())) {
                ad = auditDetailForEditAppendix(bean, ad);
            } else {
                // 会议记录 新增 或 编辑
                ad = getAuditDetailForMeetMinutes(bean);
            }

        } else {
            ad = getAuditDetailForUpdateBasic(bean, auditLog.getOrgId(),
                    auditLog.getOperator().getId()); // 校准会基本信息编辑日志
            ad.setEntityName(CALIBRATION.getName() + "-" + bean.getMeetName());
        }

        // 组装日志
        ad.setEntityId(bean.getId());
        if (log.isInfoEnabled()) {
            log.info(
                    "LOG61630:EntityChange:{}",
                    BeanHelper.bean2Json(ad, ALWAYS));
        }
        if (AuditConsts.UPDATE.equals(ad.getAction())) {
            removeUnchangedProperty(ad.getChanges());
            if (CollectionUtils.isEmpty(ad.getChanges())) {
                return;
            }
        }
        auditLog.setDetails(Collections.singletonList(ad));
        AuditLogContext.asyncCommit(auditLog);
    }

    private AuditDetail getAuditDetailForMeetMinutes(MeetCreateCmd bean) {
        List<EntityChange> result = new ArrayList<>();
        AuditDetail ad = (oldMeeting == null || StringUtils.isEmpty(oldMeeting.getMeetMinutes()) ?
                                  getCreateAuditDetail(CALIBRATION.getCode()) :
                                  getUpdateAuditDetail(CALIBRATION.getCode()));
        String meetName = bean.getMeetName();
        ad.setEntityName("%s-%s-会议纪要".formatted(CALIBRATION.getName(), meetName));
        String meetMinutes = bean.getMeetMinutes();
        result.add(new EntityChange().setFieldName("会议纪要")
                .setNewValue(meetMinutes)
                .setOldValue(oldMeeting.getMeetMinutes()));
        ad.setChanges(result);
        return ad;
    }

    private AuditDetail auditDetailForEditAppendix(MeetCreateCmd bean, AuditDetail ad) {
        Map<String, String> acMap = bean.getAppendixList()
                .stream()
                .collect(Collectors.toMap(AttachmentCreateCmd::getId,
                        AttachmentCreateCmd::getAppName, (k1, k2) -> k1));
        Map<String, String> aMap = attachmentList.stream()
                .collect(Collectors.toMap(AttachmentPO::getId, AttachmentPO::getAppName));

        if (bean.getAppendixList().size() > attachmentList.size()) { // 新增附件
            for (AttachmentCreateCmd ac : bean.getAppendixList()) {
                if (!aMap.containsKey(ac.getId())) {
                    ad = getAuditDetailForEditAppendix(0);
                    ad.setEntityName(
                            CALIBRATION.getName() + "-" + bean.getMeetName() + "-上传附件-" +
                            ac.getAppName());
                    break;
                }
            }
        } else {
            for (AttachmentPO app : attachmentList) {
                if (!acMap.containsKey(app.getId())) {
                    ad = getAuditDetailForEditAppendix(1);
                    ad.setEntityName(
                            CALIBRATION.getName() + "-" + bean.getMeetName() + "-删除附件-" +
                            app.getAppName());
                    break;
                }
            }
        }
        return ad;
    }

    private AuditDetail getAuditDetailForEditAppendix(int type) {
        return (type == 0 ? getCreateAuditDetail(CALIBRATION.getCode()) :
                        getDeleteAuditDetail(CALIBRATION.getCode()));
    }

    private AuditDetail getAuditDetailForUpdateBasic(
            MeetCreateCmd bean, String orgId, String userId) {
        List<EntityChange> result = new ArrayList<>();
        AuditDetail ad = getUpdateAuditDetail(CALIBRATION.getCode());
        ad.setChanges(result);

        if (oldMeeting == null) {
            throw new NullPointerException(
                    this.getClass().getName() + " : cm 老数据为空, 日志记录失败");
        }

        result.add(new EntityChange().setFieldName("会议名称")
                .setNewValue(bean.getMeetName())
                .setOldValue(oldMeeting.getMeetName()));
        String newFormat =
                FastDateFormat.getInstance("yyyy-MM-dd HH:mm").format(bean.getMeetTime());
        String oldFormat =
                FastDateFormat.getInstance("yyyy-MM-dd HH:mm").format(oldMeeting.getMeetTime());
        result.add(new EntityChange().setFieldName("会议时间")
                .setNewValue(newFormat)
                .setOldValue(oldFormat));

        // 人才委员会
        result.add(new EntityChange().setFieldName("人才委员会")
                .setNewValue(getUserString(orgId, bean.getTalentCommitteeList()))
                .setOldValue(getUserString(orgId, oldTcIds)));

        // 会议组织者
        if (CollectionUtils.isEmpty(bean.getOrganizerList())) {
            bean.setOrganizerList(Collections.singletonList(userId));
        }
        result.add(new EntityChange().setFieldName("会议组织者")
                .setNewValue(getUserString(orgId, bean.getOrganizerList()))
                .setOldValue(getUserString(orgId, oldOrganizerIds)));

        return ad;
    }

    private String getUserString(String orgId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return "";
        }
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
                .append("（")
                .append(a.getUsername())
                .append("）")
                .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }
}
