package com.yxt.talent.rv.infrastructure.service.remote;

import com.yxt.talent.rv.infrastructure.service.remote.dto.MqSendResult;

/**
 * 针对消息队列服务的防腐层，封装一些通用的发送行为，以便在不同的消息队列实现之间切换时，不需要修改业务代码
 */
public interface MqAclSender {

    /**
     * 同步发送消息
     *
     * @param topic
     * @param body
     * @return
     */
    MqSendResult send(String topic, String body);

    /**
     * 异步发送消息
     *
     * @param topic
     * @param jsonBody
     */
    void asyncSend(String topic, String jsonBody);

    /**
     * 同步发送延迟消息，延迟队列各个开源组件之间实现差异较大
     * rocketmq只实现了18个固定等级的延迟队列，
     * kafka只能通过自定义topic来实现
     * rabbitmq只能通过插件来实现
     * 所以默认只有rocketmq的发送服务实现了这个方法，其他实现方法可能会抛出UnsupportedOperationException
     *
     * @param topic
     * @param body
     * @param delayLevel
     * @return
     */
    MqSendResult sendDelay(String topic, String body, int delayLevel);

    /**
     * 异步发送延迟消息，延迟队列各个开源组件之间实现差异较大
     * rocketmq只实现了18个固定等级的延迟队列，
     * kafka只能通过自定义topic来实现
     * rabbitmq只能通过插件来实现
     * 所以默认只有rocketmq的发送服务实现了这个方法，其他实现方法可能会抛出UnsupportedOperationException
     *
     * @param topic
     * @param body
     * @param delayLevel
     */
    void asyncSendDelay(String topic, String body, int delayLevel);

    /**
     * 如果使用rabbitmq消息队列实现，那么需要注意它的延迟插件最长只支持延迟24天；
     * 如果使用rocketmq实现，则会直接抛出UnsupportedOperationException；
     * 后续会考虑由应用底层实现通用的延迟消息机制，即通过保存消息状态，然后定时轮询消息状态，到达指定时间后再发送消息
     *
     * @param topic
     * @param body
     * @param delayTime 发送之后延迟的时间，单位毫秒,最长不能超过24天
     * @return
     */
    MqSendResult sendDelay(String topic, String body, long delayTime);

    /**
     * 如果使用rabbitmq消息队列实现，那么需要注意它的延迟插件最长只支持延迟24天；
     * 如果使用rocketmq实现，则会直接抛出UnsupportedOperationException；
     * 后续会考虑由应用底层实现通用的延迟消息机制，即通过保存消息状态，然后定时轮询消息状态，到达指定时间后再发送消息
     *
     * @param topic
     * @param body
     * @param delayTime 发送之后延迟的时间，单位毫秒,最长不能超过24天
     * @return
     */
    void asyncSendDelay(String topic, String body, long delayTime);

    /**
     * 同步发送带Tag的消息, 以便消费端可以根据tag值进行选择性消费
     * 某些消息队列可能不支持给消息打tag
     * 所以默认只有rocketmq的发送服务实现了这个方法，其他实现方法可能会抛出UnsupportedOperationException
     *
     * @param topic
     * @param tag
     * @param body
     * @return
     */
    MqSendResult sendWithTag(String topic, String tag, String body);

    /**
     * 异步发送带Tag的消息, 以便消费端可以根据tag值进行选择性消费
     * 某些消息队列可能不支持给消息打tag
     * 所以默认只有rocketmq的发送服务实现了这个方法，其他实现方法可能会抛出UnsupportedOperationException
     *
     * @param topic
     * @param tag
     * @param body
     */
    void asyncSendWithTag(String topic, String tag, String body);

    /**
     * 同步发送有序消息，根据hashKey来判断应该发送到哪个队列
     * 某些消息队列可能不支持有序消息
     * 所以默认只有rocketmq的发送服务实现了这个方法，其他实现方法可能会抛出UnsupportedOperationException
     *
     * @param topic
     * @param jsonBody
     * @param hashKey
     * @return
     */
    MqSendResult sendOrderly(String topic, String jsonBody, String hashKey);

    /**
     * 异步发送有序消息，根据hashKey来判断应该发送到哪个队列
     * 某些消息队列可能不支持有序消息
     * 所以默认只有rocketmq的发送服务实现了这个方法，其他实现方法可能会抛出UnsupportedOperationException
     *
     * @param topic
     * @param jsonBody
     * @param hashKey
     */
    void asyncSendOrderly(String topic, String jsonBody, String hashKey);
}
