package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetResultQuery;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserResultVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserResultPO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Mapper
@Repository
public interface CaliMeetUserResultMapper {

    int insertList(@Nonnull @Param("list") List<CaliMeetUserResultPO> list);

    int updateById(@Nonnull @Param("updated") CaliMeetUserResultPO updated);

    @Nonnull
    List<CaliMeetUserResultPO> listByMeetIdAndPrjIdAndDimIdInAndUserIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetId") String meetId,
            @Param("prjId") String prjId, @Param("dimIds") Collection<String> dimIds,
            @Param("userIds") Collection<String> userIds);

    @Nonnull
    List<CaliMeetUserResultPO> listByOrgIdAndMeetIdAndUserIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userIdCollection") Collection<String> userIdCollection);

    @Nonnull
    List<CaliMeetUserResultPO> listByOrgIdAndMeetIdAndUserIdAndDimIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Nonnull @Param("userId") String userId,
            @Param("dimensionIdCollection") Collection<String> dimensionIdCollection);

    @Nonnull
    IPage<PrjUserResultVO> paging(
            @Nonnull @Param("orgId") String orgId, @Param("xValue") int xValue,
            @Param("yValue") int yValue, @Nonnull @Param("search") CaliMeetResultQuery search,
            @Param("page") IPage<PrjUserResultVO> page);

    int deleteByOrgIdAndMeetIdAndUserIdIn(
            @Nonnull @Param("orgId") String orgId, @Param("meetingId") String meetingId,
            @Param("userIdCollection") Collection<String> userIdCollection);

    @Nonnull
    List<CaliMeetUserResultPO> listByOrgId(@Nonnull @Param("orgId") String orgId);

    void insertOrUpdate(CaliMeetUserResultPO entity);

    Long countByOrgIdAndProjectIdAndMeetingId(
            @Nonnull @Param("orgId") String orgId,
            @Param("projectId") String projectId, @Param("meetingId") String meetingId);
}
