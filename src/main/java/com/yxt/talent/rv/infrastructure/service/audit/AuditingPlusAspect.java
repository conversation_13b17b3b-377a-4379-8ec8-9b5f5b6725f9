package com.yxt.talent.rv.infrastructure.service.audit;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.Operator;
import com.yxt.auditlog.bean.PageInfo;
import com.yxt.auditlog.resolver.ProfileFactory;
import com.yxt.auditlog.resolver.ProfileResolver;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * 1. 日志注解AuditingPlus 配合 Auditing使用
 * 2. 采用面向切面编程 + 策略模式，可使得日志与业务解耦，AuditingPlus仅停留在Controller层，不侵入业务， 减少了代码复杂度、使得代码更清晰、易读
 * 3. AuditingPlus注解的日志均异步提交， 不影响原业务代码性能，日志提交失败不影响主业务
 * 4. AuditingPlus注解可完全覆盖包括增、删、改、查、导入、导出等所有日志场景
 */
@Slf4j
@Aspect
@Order(10_000 + 10)
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "sprv.audit.log.enable", havingValue = "true")
public class AuditingPlusAspect {

    private final ApplicationContext applicationContext;
    private final AuthService authService;

    @Pointcut("execution(public * com.yxt.talent..*(..)) && @annotation(auditingPlus)")
    public void pointcut(AuditingPlus auditingPlus) {
        // NOSONAR
    }

    @Around(value = "pointcut(auditingPlus)", argNames = "point,auditingPlus")
    public Object around(ProceedingJoinPoint point, AuditingPlus auditingPlus) throws Throwable {
        // 获取请求数据，
        Object[] args = point.getArgs();
        String[] names = ((CodeSignature) point.getSignature()).getParameterNames();

        Optional<AuditLogStrategy> auditLogStrategy =
                getAuditLogStrategy(auditingPlus.strategyClass().getSimpleName());

        Optional<AuditLog> auditLogOp = getAuditLog();

        // 前置区域 -业务执行前，数据准备
        try {
            // 调用策略实现, 做一些准备工作【如记录编辑日志时、备份编辑前数据】
            auditLogStrategy.ifPresent(executor -> executor.doPrepare(names, args));
        } catch (Exception e) {
            log.error("LOG64630:{}", this.getClass().getName(), e);
        }

        // 主业务代码执行
        Object result = point.proceed();

        // 后置区域 - 主业务成功后，执行日志组装及发送任务
        try {
            // 调用策略实现, 发送日志 【异步】
            auditLogOp.ifPresent(logContext -> {
                log.info("LOG12155:addr={}, json={}", ObjectUtils.identityToString(logContext),
                        BeanHelper.bean2Json(logContext, ALWAYS));
                if (StringUtils.isBlank(logContext.getOrgId())) {
                    CommonUtil.printCurrentStackTrace();
                }
                auditLogStrategy.ifPresent(
                        executor -> executor.doExecute(logContext, names, args, result));
            });
        } catch (Exception e) {
            log.error("LOG63540:{}", this.getClass().getName(), e);
        }

        // 返回
        return result;
    }

    private Optional<AuditLog> getAuditLog() {
        AuditLog auditLog = AuditLogContext.get().setAsync(true).setAutoCommit(false);
        try {
            if (!StringUtils.isBlank(auditLog.getOrgId()) && auditLog.getOperator() != null &&
                !StringUtils.isBlank(auditLog.getOperator().getId())) {
                return Optional.of(auditLog);
            }

            Optional<HttpServletRequest> currentRequest =
                    Optional.ofNullable(ApiUtil.getRequestByContext());
            if (currentRequest.isPresent()) {
                HttpServletRequest request = currentRequest.get();
                PageInfo pageInfo = Optional.ofNullable(auditLog.getPageInfo())
                        .orElse(PageInfo.of())
                        .setUserAgent(request.getHeader("User-Agent"));

                auditLog.setSource(request.getHeader("Source"));
                auditLog.setPageInfo(pageInfo);

                Operator operator =
                        Optional.ofNullable(auditLog.getOperator()).orElse(Operator.of());
                final ProfileResolver resolver = ProfileFactory.getResolver();
                resolver.getUserProfile(request).ifPresent(userProfile -> {
                    auditLog.setOrgId(userProfile.getOrgId());
                    auditLog.setOperator(operator.setId(userProfile.getUserId())
                            .setFullName(userProfile.getFullname())
                            .setIpAddress(userProfile.getIpAddress()));
                });
            } else {
                log.warn("LOG11565:Not a web environment");
            }
            return Optional.of(auditLog);
        } catch (Exception e) {
            log.error("LOG11495:", e);
        }
        return Optional.ofNullable(auditLog);
    }

    private Optional<AuditLogStrategy> getAuditLogStrategy(String simpleName) {
        simpleName = simpleName.substring(0, 1).toLowerCase() + simpleName.substring(1);
        try {
            return Optional.of(applicationContext.getBean(simpleName, AuditLogStrategy.class));
        } catch (Exception e) {
            // NOSONAR
        }
        return Optional.empty();
    }

}
