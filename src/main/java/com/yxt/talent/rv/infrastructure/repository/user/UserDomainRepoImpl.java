package com.yxt.talent.rv.infrastructure.repository.user;

import com.yxt.talent.rv.domain.user.User;
import com.yxt.talent.rv.domain.user.UserDomainRepo;
import com.yxt.talent.rv.domain.user.entity.UdpUser;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.repository.user.assembler.UserAssembler;
import com.yxt.util.OptionalEmpty;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.mapListThenFilterNull;

@Slf4j
@RequiredArgsConstructor
@Repository("userDomainRepo")
public class UserDomainRepoImpl implements UserDomainRepo {

    private final UdpLiteUserMapper udpLiteUserMapper;
    private final UserAssembler userAssembler;
    private final UserFocusRepo userFocusRepo;
    private final UserPrjSummaryRepo userPrjSummaryRepo;

    @Override
    public Optional<User> load(
            @NonNull String orgId, @NonNull String entityId, @NonNull User.LoadConfig loadConfig) {
        return this.load(orgId, entityId).map(user -> loadSub(user, loadConfig));
    }

    private User loadSub(User user, User.LoadConfig loadConfig) {
        String orgId = user.getOrgId();
        String entityId = user.getId();
        if (loadConfig.loadUserFocuses()) {
            userFocusRepo.loadByUserId(orgId, entityId).ifPresent(user::addUserFocuses);
        }
        if (loadConfig.loadPrjSummaries()) {
            userPrjSummaryRepo.loadByUserId(orgId, entityId).ifPresent(user::addUserPrjSummaries);
        }
        return user;
    }

    @Override
    public Optional<User> load(@NonNull String orgId, @NonNull String entityId) {
        UdpLiteUserPO udpLiteUserPo = udpLiteUserMapper.selectByUserId(orgId, entityId);
        return Optional.ofNullable(udpLiteUserPo).map(userAssembler::toUdpUser).map(User::new);
    }

    @NotNull
    @Override
    public List<User> load(String orgId, List<String> entityIds) {
        List<UdpLiteUserPO> udpLiteUserPos = udpLiteUserMapper.selectByUserIds(orgId, entityIds);
        return Optional.of(udpLiteUserPos)
                .map(userAssembler::toUdpUsers)
                .map(coll -> mapListThenFilterNull(coll, User::new))
                .orElseGet(ArrayList::new);
    }

    @Override
    public Optional<User> loadByThirdId(@NonNull String orgId, @NonNull String thirdId) {
        UdpLiteUserPO udpLiteUserPo = udpLiteUserMapper.selectByThirdUserId(orgId, thirdId);
        UdpUser udpUser = userAssembler.toUdpUser(udpLiteUserPo);
        return udpUser == null ? Optional.empty() : Optional.of(new User(udpUser));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(@NonNull User entity) {
        OptionalEmpty.of(entity.getUserFocuses()).ifNotEmpty(userFocusRepo::save);
        OptionalEmpty.of(entity.getUserPrjSummaries()).ifNotEmpty(userPrjSummaryRepo::save);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@NonNull User entity) {
        OptionalEmpty.of(entity.getUserFocuses()).ifNotEmpty(userFocusRepo::delete);
        OptionalEmpty.of(entity.getUserPrjSummaries()).ifNotEmpty(userPrjSummaryRepo::delete);
    }


}
