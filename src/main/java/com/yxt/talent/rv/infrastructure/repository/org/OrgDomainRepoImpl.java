package com.yxt.talent.rv.infrastructure.repository.org;

import com.yxt.enums.DeleteEnum;
import com.yxt.talent.rv.domain.org.Org;
import com.yxt.talent.rv.domain.org.OrgDomainRepo;
import com.yxt.talent.rv.domain.org.entity.OrgSetting;
import com.yxt.talent.rv.infrastructure.common.exception.UdpUnsupportedOperationException;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.org.OrgSettingMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpOrgMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.org.OrgSettingPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpOrgPO;
import com.yxt.util.OptionalEmpty;
import jakarta.annotation.Nonnull;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.yxt.common.util.StreamUtil.filterList;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNull;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.mapListThenFilterNull;

@Slf4j
@Repository
@RequiredArgsConstructor
public class OrgDomainRepoImpl implements OrgDomainRepo {

    private final UdpOrgMapper udpOrgMapper;
    private final OrgAssembler orgAssembler;
    private final OrgSettingMapper orgSettingMapper;

    @Override
    public Optional<Org> load(
            @Nonnull String orgId, @Nonnull String entityId, @Nonnull Org.LoadConfig loadConfig) {
        return this.load(orgId, entityId).map(org -> loadSub(orgId, loadConfig, org));
    }

    private Org loadSub(@Nonnull String orgId, @Nonnull Org.LoadConfig loadConfig, Org org) {
        if (loadConfig.loadOrgSettings()) {
            loadOrgSettings(orgId).ifPresent(org::addOrgSettings);
        }
        return org;
    }

    @Override
    public Optional<Org> load(@Nonnull String orgId, @Nonnull String entityId) {
        UdpOrgPO udpOrgPo = udpOrgMapper.selectById(orgId);
        return Optional.ofNullable(udpOrgPo).map(orgAssembler::toUdpOrg).map(Org::new);
    }

    @Override
    public Optional<Org> load(@Nonnull String entityId) {
        return load(entityId, entityId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(@Nonnull Org entity) {
        Set<OrgSetting> orgSettings = entity.getOrgSettings();

        // 清理被标记了删除的子实体
        List<OrgSetting> removeList =
                filterList(orgSettings, e -> DeleteEnum.isDeleted(e.getDeleted()));
        OptionalEmpty.of(removeList).ifNotEmpty(list -> deleteOrgSettings(entity.getId(), list));

        List<OrgSetting> updateList =
                filterList(orgSettings, e -> DeleteEnum.isNotDeleted(e.getDeleted()));
        OptionalEmpty.of(updateList)
                .map(orgAssembler::toOrgSettingPos)
                .ifPresent(orgSettingMapper::batchInsertOrUpdate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@Nonnull Org entity) {
        throw new UdpUnsupportedOperationException();
    }

    private void deleteOrgSettings(@NonNull String orgId, Collection<OrgSetting> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.warn("LOG13635:");
            return;
        }

        List<String> ids = mapListThenFilterNull(entities, OrgSetting::getId);
        orgSettingMapper.deleteByIds(orgId, ids);
    }

    private Optional<Collection<OrgSetting>> loadOrgSettings(@Nonnull String orgId) {
        List<OrgSettingPO> orgSettingPos = orgSettingMapper.listByOrgId(orgId);
        return Optional.of(orgSettingPos).map(orgAssembler::toOrgSettings);
    }
}
