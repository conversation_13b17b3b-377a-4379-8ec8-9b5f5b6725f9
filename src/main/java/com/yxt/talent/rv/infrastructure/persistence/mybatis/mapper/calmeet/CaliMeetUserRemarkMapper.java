package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserRemarkPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CaliMeetUserRemarkMapper {

    int updateById(@Nonnull @Param("updated") CaliMeetUserRemarkPO updated);

    @Nonnull
    List<CaliMeetUserRemarkPO> listByPrjIdAndMeetingIdAndUserId(
            @Nonnull @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Nullable @Param("meetingId") String meetingId,
            @Nonnull @Param("userId") String userId);

    long insertOrUpdate(@Nonnull CaliMeetUserRemarkPO calMeetUserRemark);
}
