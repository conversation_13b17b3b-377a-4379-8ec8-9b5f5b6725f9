package com.yxt.talent.rv.infrastructure.repository.common.assembler;

import com.yxt.talent.rv.domain.common.Category;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.CategoryPO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = BaseAssemblerConfig.class)
public interface CategoryAssembler {

    @Nullable
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "pendingEvents", ignore = true)
    @Mapping(target = "enabled", source = "categoryEnable")
    @Mapping(target = "catType", source = "categoryType")
    @Mapping(target = "catName", source = "categoryName")
    Category toCategory(CategoryPO categoryPo);

    @Nullable
    @Mapping(target = "categoryType", source = "catType")
    @Mapping(target = "categoryName", source = "catName")
    @Mapping(target = "categoryEnable", source = "enabled")
    CategoryPO toCategoryPo(Category category);

}
