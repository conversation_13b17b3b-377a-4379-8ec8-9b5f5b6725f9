package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.aom.base.enums.AomItemTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * ActivityArrangeItem
 */
@Data
@TableName("rv_activity_arrange_item")
public class RvActivityArrangeItemPO implements Serializable {
    @Serial
    private static final long serialVersionUID = 211437803454755469L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动/项目id
     */
    private String actvId;

    /**
     * 活动/项目OrganizationID
     */
    private Long actvOrgId;

    /**
     * 上一级ItemID(第一级为0)
     */
    private Long parentId;

    /**
     * 叶节点引用对象的具体类型(UACD注册表中定义)
     */
    private String refRegId;

    /**
     * 叶节点引用对象的id
     */
    private String refId;

    /**
     * 叶子节点引用对象的名称
     */
    private String refName;

    /**
     * 业务自定义别名
     */
    private String itemName;

    /**
     * 节点类型(0-叶节点(任务), 1-目录节点-章节/阶段, 2-目录节点-任务组)
     * AomItemTypeEnum
     */
    private Integer itemType;

    /**
     * 类型国际化key
     */
    private String typeNameKey;

    /**
     * 活动任务类型自定义别名
     */
    private String actvAlias;

    /**
     * 节点层级(从1开始)
     */
    private Integer nodeLevel;

    /**
     * 节点描述
     */
    private String description;

    /**
     * 是否必修(0-否, 1-是)
     */
    private Integer required;

    /**
     * 是否锁定：0-否，1-是
     */
    private Integer locked;

    /**
     * 是否隐藏：0-否，1-是
     */
    private Integer hidden;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 学习时长
     */
    private Integer studyHours;

    /**
     * 业务扩展json
     */
    private String ext;

    /**
     * 时间模式(0-固定, 1-相对)
     */
    private Integer timeModel;

    /**
     * 固定开始时间
     */
    private Date startTime;

    /**
     * 固定截止时间
     */
    private Date endTime;

    /**
     * 相对开始天数
     */
    private Integer startDayOffset;

    /**
     * 相对截止天数
     */
    private Integer endDayOffset;

    /**
     * 数据状态(1-页面通过校验, 2-同步成功, 3-同步失败, 4-未同步; 默认为1)
     */
    private Integer itemStatus;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public boolean isLeaf() {
        return this.itemType != null && this.itemType == AomItemTypeEnum.LEAF.getType();
    }

}
