import pandas as pd
import re
from datetime import datetime

"""
根据ddl脚本和excel文件生成批量插入语句，可以一次性生成所有demo数据，
【注意】excel表头需要和ddl字段描述保持完全一致，因为脚本是按照表头和ddl中字段的描述是否相等来匹配的
"""

# 机构id
global_org_id = input("请输入org_id:")
ddl_file_path = "ddl.sql"  # DDL定义文件的路径
# Excel文件路径列表，每个Excel文件中包含多个sheet，每个sheet对应一个表
excel_paths = ["维度表.xlsx", "用户画像.xlsx", "组织数据.xlsx"]
# 输出文件的路径
output_file = "batch_insert.sql"
# 用于生成更新udp_dept部门sql时使用的sheet表名
target_sheet_name = "用户多维度基表"

# 检查配置是否已完成
if not ddl_file_path or not excel_paths or not output_file or not global_org_id:
    print("请在脚本开头配置所有必要的参数。")
    exit()


def read_ddl_from_file(file_path):
    # 使用UTF-8编码读取DDL定义
    with open(file_path, 'r', encoding='utf-8') as file:
        ddl = file.read()
    return ddl


def parse_ddl(ddl):
    lines = ddl.split('\n')
    table_definitions = {}
    ddl_defaults = {}
    current_table = None

    # 解析表的COMMENT
    table_comment_regex = re.compile(r"COMMENT\s*=\s*'(.*)'", re.IGNORECASE)

    for line in lines:
        line = line.strip()
        if line.startswith('CREATE TABLE'):
            current_table = line.split()[2].strip('`')
            table_definitions[current_table] = {}
        elif line.startswith('`'):
            # 解析列信息
            parts = line.split('`')
            column_name = parts[1]
            comment_index = line.find('COMMENT')
            default_index = line.find('DEFAULT')
            if comment_index != -1:
                comment = line[comment_index + 8:].strip(' \',').lower()
            else:
                comment = column_name.lower()

            table_definitions[current_table][comment] = column_name

            if default_index != -1:
                default_part = line[default_index:].split()[1].strip(' ,')
                ddl_defaults[column_name] = default_part
            else:
                ddl_defaults[column_name] = '\'\''

        elif 'COMMENT' in line and current_table:
            match = table_comment_regex.search(line)
            if match:
                table_comment = match.group(1)
                table_definitions[current_table]['comment'] = table_comment

    return table_definitions, ddl_defaults


def generate_insert_statements(table_name, columns_comments, data, ddl_defaults):
    insert_statements = []
    # 创建一个大小写不敏感的列名映射
    case_insensitive_columns = {col.lower(): col for col in data.columns}
    for _, row in data.iterrows():
        values = []
        for comment, col in columns_comments.items():
            comment_lower = comment.lower()
            if comment_lower == 'id' or comment_lower == 'id主键' or comment_lower == '主键' or comment_lower == '主键id':
                values.append('UUID()')
            elif comment_lower in ['db_create_time', 'db_update_time']:
                values.append('NOW()')
            elif col == 'org_id':
                values.append(f"'{global_org_id}'")
            elif comment_lower in case_insensitive_columns and pd.notna(row[case_insensitive_columns[comment_lower]]):
                value = row[case_insensitive_columns[comment_lower]]
                if isinstance(value, str):
                    values.append(f"'{value}'")
                elif isinstance(value, datetime):
                    values.append(value.strftime("'%Y-%m-%d %H:%M:%S'"))
                else:
                    values.append(str(value))
            else:
                default_value = ddl_defaults.get(col, '\'\'')
                values.append(default_value)

        value_str = '(' + ', '.join(values) + ')'
        insert_statements.append(value_str)

    # 预先删除该机构下该表的数据
    pre_del_statement = f"delete from {table_name} where org_id = '{global_org_id}';\n\n"
    base_statement = f"INSERT INTO {table_name} ({', '.join(columns_comments.values())}) VALUES "
    insert_statements = pre_del_statement + base_statement + ', '.join(insert_statements) + ';'
    return insert_statements


def generate_update_statements(data, target_columns):
    update_statements = []
    data_unique = data.drop_duplicates(subset=target_columns)
    for _, row in data_unique.iterrows():
        org_id = row['机构Id']
        third_party_id = row['三方部门id']
        dept_name = row['部门名称']
        update_statement = f"update udp_dept a set a.third_id = '{third_party_id}', update_time = now() where a.org_id = '{org_id}' and a.name = '{dept_name}';"
        update_statements.append(update_statement)
    return update_statements


def main():
    ddl = read_ddl_from_file(ddl_file_path)
    table_definitions, ddl_defaults = parse_ddl(ddl)

    excel_sheets = {}
    for excel_path in excel_paths:
        # 将sheet名称与其对应的文件路径存储在字典中
        xl = pd.ExcelFile(excel_path)
        for sheet_name in xl.sheet_names:
            excel_sheets[sheet_name.lower()] = excel_path

    total_tables = 0
    successful_tables = 0
    failed_tables = []
    missing_tables = []
    # 用于存储更新SQL语句
    update_sql_statements = []
    check_sql_statements = []

    with open(output_file, "w", encoding='utf-8') as file:  # 应当使用"w"而不是"a"，以覆盖旧文件
        try:
            for table_name, table_info in table_definitions.items():
                total_tables += 1

                table_comment = table_info.get('comment', '').lower()  # 获取表的comment
                # 获取表的comment之后，需要将这条记录从table_info中删除，否则会影响后续的处理
                table_info.pop('comment', None)

                if table_comment in excel_sheets:
                    # 根据comment匹配sheet，并读取数据
                    excel_path = excel_sheets[table_comment]
                    data = pd.read_excel(excel_path, sheet_name=table_comment)

                    # 新增功能：检测特定的sheet并生成更新SQL语句
                    if table_comment == target_sheet_name.lower():
                        target_columns = ['机构Id', '三方部门id', '部门名称']
                        update_statements = generate_update_statements(data, target_columns)
                        update_sql_statements.extend(update_statements)

                    # 继续生成插入SQL语句，不管sheet名称是否匹配

                    insert_statements = generate_insert_statements(table_name, table_info, data, ddl_defaults)
                    check_sql_statements.append(f"select * from {table_name} where org_id = '{global_org_id}';")
                    file.write(insert_statements + "\n\n\n")
                    successful_tables += 1
                else:
                    failed_tables.append(table_name)
                    missing_tables.append(table_comment)

            # 将更新SQL语句写入文件
            if update_sql_statements:
                file.write("\n\n\n#########################以下为更新udp_dept部门表sql#########################\n")
            for update_statement in update_sql_statements:
                file.write(update_statement + "\n")

            file.write("\n\n\n#########################以下为更新udp_lite_user三个用户表sql#########################\n")
            file.write(
                f"update udp_user a set a.third_user_id = a.username, update_time = now() where a.status = 1 and a.deleted = 0 and a.org_id = '{global_org_id}';\n\n")
            file.write(
                f"update udp_lite_user a set a.third_user_id = a.username, update_time = now() where a.status = 1 and a.deleted = 0 and a.org_id = '{global_org_id}';\n\n")
            file.write(
                f"update udp_full_lite_user a set a.third_user_id = a.username, update_time = now() where a.status = 1 and a.deleted = 0 and a.org_id = '{global_org_id}';\n\n")

            file.write("\n\n\n#########################以下为新老验证用的sql#########################\n")
            for check_sql_statement in check_sql_statements:
                file.write(check_sql_statement + "\n")

        except Exception as e:
            print(f"生成失败: {table_name}")
            print(e)
            failed_tables.append(table_name)

    if missing_tables:
        print("以下表在DDL中存在但不在Excel文件中:", missing_tables)

    print(f"总表数: {total_tables}")
    print(f"成功生成: {successful_tables}")
    print(f"失败个数: {len(failed_tables)}")
    print(f"失败的表: {failed_tables}")


if __name__ == "__main__":
    main()
