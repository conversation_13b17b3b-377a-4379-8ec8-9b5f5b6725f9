import random
import uuid


def generate_sql():
    # 定义随机选项
    user_ids = [f"user_{i}" for i in range(11)]
    dept_ids = [
        "D00001", "D00030", "D00031", "D00032", "D00033", "D00034", "D00035", "D00016", "D00017", "D00018",
        "D00019", "D00015", "D00020", "D00024", "D00026", "D00027", "D00028", "D00002", "D00011", "D00029",
        "D00036", "D00040", "D00045", "D00041"
    ]
    rv_names = ["优秀", "良好", "不足"]
    manage_options = [0, 1]
    component_options = [0, 1]

    # 生成UUIDs
    rv_ids = [str(uuid.uuid4()) for _ in range(5)]
    series_root_ids = [str(uuid.uuid4()) for _ in range(2)]
    third_position_ids = [str(uuid.uuid4()) for _ in range(2)]

    # 开始构建SQL语句
    sql_prefix = "INSERT INTO dwd_user_rv (id, org_id, third_user_id, third_dept_id, third_position_id, manage, rv_id, rv_name, competent, series_root_id, deleted) VALUES "
    values = []

    for _ in range(1000):
        id = str(uuid.uuid4())
        org_id = "a9352763-04f2-4459-aca9-6e464aac32e8"
        third_user_id = random.choice(user_ids)
        third_dept_id = random.choice(dept_ids)
        manage = random.choice(manage_options)
        rv_id = random.choice(rv_ids)
        rv_name = random.choice(rv_names)
        component = random.choice(component_options)
        series_root_id = random.choice(series_root_ids)
        third_position_id = random.choice(third_position_ids)
        deleted = 0  # 默认为0

        values.append(f"('{id}', '{org_id}', '{third_user_id}', '{third_dept_id}', '{third_position_id}', {manage}, '{rv_id}', '{rv_name}', {component}, '{series_root_id}', {deleted})")

    # 将所有值组合到一条SQL语句中
    full_sql = sql_prefix + ", ".join(values) + ";"
    return full_sql

def main():
    sql_inserts = generate_sql()
    with open('inserts.sql', 'w', encoding='utf-8') as f:
        f.write(sql_inserts)
        print("SQL语句已成功写入到 'inserts.sql' 文件。")

if __name__ == "__main__":
    main()
