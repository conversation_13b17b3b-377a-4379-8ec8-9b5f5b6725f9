import os
from datetime import datetime

import pymysql

"""
脚本的作用：
1. 从目标数据库中读取所有包含 org_id 字段的表名
2. 生成批量插入或替换 SQL 语句
3. 将 SQL 语句写入文件
"""


def main():
    target_org_id = input("请输入复制目标机构的orgId：")
    # target_org_id = '8eee1504-1ded-4b63-a89e-ba9031135015'
    target_db_name = "yxtdmt"

    if not target_org_id:
        print("未输入目标机构orgId。")
        return

    config = {
        'host': 'singular-model-prod-external.mysql.rds.aliyuncs.com',
        'user': 'etl',
        'password': 'o8BNeclsEf9raCRP',
        'database': target_db_name,
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }

    sql_directory = "sql_exports"
    if not os.path.exists(sql_directory):
        os.makedirs(sql_directory)  # 创建存放SQL文件的目录

    connection = None
    try:
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            tables_with_org_id = get_tables_with_org_id(cursor, target_db_name)
            for table in tables_with_org_id:
                generate_sql_for_table_and_export(cursor, table, target_org_id, sql_directory)
    finally:
        if connection:
            connection.close()


def get_tables_with_org_id(cursor, db_name):
    cursor.execute(
        "select distinct TABLE_NAME from information_schema.columns where column_name = 'org_id' and TABLE_SCHEMA = %s;",
        (db_name,)
    )
    return [row['TABLE_NAME'] for row in cursor.fetchall()]


def generate_sql_for_table_and_export(cursor, table, org_id, sql_directory):
    cursor.execute(f"SELECT * FROM {table} WHERE org_id = %s;", (org_id,))
    records = cursor.fetchall()

    if not records:
        return

    columns = ', '.join([f"`{col}`" for col in records[0].keys()])
    values_segments = []

    for record in records:
        value_segment = []
        for value in record.values():
            if isinstance(value, datetime):
                # 格式化日期时间字段
                formatted_value = value.strftime("'%Y-%m-%d %H:%M:%S'")
            elif isinstance(value, str):
                formatted_value = f"'{value}'"
            elif value is None:
                formatted_value = "NULL"
            else:
                formatted_value = str(value)
            value_segment.append(formatted_value)
        values_segments.append(f"({', '.join(value_segment)})")

    values_placeholder = ',\n'.join(values_segments)
    update_statement = ', '.join([f"`{col}` = VALUES(`{col}`)" for col in records[0].keys()])

    sql = f"INSERT INTO {table} ({columns}) VALUES\n{values_placeholder}\nON DUPLICATE KEY UPDATE {update_statement};"
    export_sql_to_file(sql, table, sql_directory)


def export_sql_to_file(sql, table, directory, filename=None):
    if not filename:
        filename = f"{table}.sql"  # 基于表名命名文件
    filepath = os.path.join(directory, filename)
    with open(filepath, 'a', encoding="utf-8") as f:  # 以追加模式打开文件
        f.write(f"-- Statements for table: {table}\n{sql}\n\n")


if __name__ == "__main__":
    main()
