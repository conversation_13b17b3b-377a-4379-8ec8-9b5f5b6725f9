import os
import re

"""
# 脚本的作用：
# 1. 读取指定目录下的SQL文件
# 2. 从SQL文件中提取出表名和ID
# 3. 生成删除SQL语句
# 4. 将删除SQL语句写入文件
"""


def read_sql_file(filepath):
    with open(filepath, 'r', encoding='utf-8') as file:
        return file.read()


def extract_data_from_sql(content):
    table_name_match = re.search(r'insert into (\w+)', content)
    if table_name_match:
        table_name = table_name_match.group(1)
        ids = re.findall(r"\('(\w+-\w+-\w+-\w+-\w+)'", content)
        return table_name, ids
    return None, []


def generate_delete_sql(table_name, ids, org_id):
    ids_str = ', '.join(f"'{id}'" for id in ids)
    return f"DELETE FROM {table_name} WHERE org_id = '{org_id}' AND `id` NOT IN ({ids_str});"


def main():
    directory = './sql_exports'  # 指定目录
    org_id = '8eee1504-1ded-4b63-a89e-ba9031135015'  # 组织ID
    output_file = 'delete_statements.sql'  # 输出文件名

    with open(output_file, 'w', encoding='utf-8') as out_file:
        for filename in os.listdir(directory):
            if filename.endswith('.sql'):
                filepath = os.path.join(directory, filename)
                content = read_sql_file(filepath)
                table_name, ids = extract_data_from_sql(content)
                if table_name and ids:
                    delete_sql = generate_delete_sql(table_name, ids, org_id)
                    out_file.write(delete_sql + "\n")


if __name__ == '__main__':
    main()
